#!/usr/bin/env node

/**
 * 反向工程Augment插件的排序逻辑
 * 通过分析数据库键的模式来推断正确的排序方法
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const level = require('level');

// 分析数据库键的模式
async function analyzeKeyPatterns() {
    console.log('🔍 分析数据库键的模式...\n');
    
    // 查找VSCode工作区
    const workspaces = await findWorkspaces();
    console.log(`📂 发现 ${workspaces.length} 个工作区\n`);
    
    const keyAnalysis = {
        exchangeKeys: [],
        conversationKeys: [],
        otherKeys: [],
        keyPatterns: new Map()
    };
    
    for (const workspace of workspaces.slice(0, 3)) {
        console.log(`🔍 分析工作区: ${workspace.id.substring(0, 8)}...`);
        
        try {
            const dbPath = path.join(workspace.augmentPath, 'conversations');
            if (!fs.existsSync(dbPath)) {
                console.log('   ❌ 数据库不存在');
                continue;
            }
            
            const db = level(dbPath, { valueEncoding: 'json' });
            
            // 收集所有键
            const allKeys = [];
            for await (const [key] of db.iterator()) {
                allKeys.push(key);
            }
            
            console.log(`   📊 总键数: ${allKeys.length}`);
            
            // 分析键的模式
            for (const key of allKeys) {
                if (key.startsWith('exchange:')) {
                    keyAnalysis.exchangeKeys.push(key);
                    
                    // 分析exchange键的结构
                    const parts = key.split(':');
                    if (parts.length >= 3) {
                        const conversationId = parts[1];
                        const exchangeId = parts[2];
                        
                        if (!keyAnalysis.keyPatterns.has(conversationId)) {
                            keyAnalysis.keyPatterns.set(conversationId, []);
                        }
                        keyAnalysis.keyPatterns.get(conversationId).push({
                            key: key,
                            exchangeId: exchangeId,
                            keyIndex: allKeys.indexOf(key)
                        });
                    }
                } else if (key.includes('conversation')) {
                    keyAnalysis.conversationKeys.push(key);
                } else {
                    keyAnalysis.otherKeys.push(key);
                }
            }
            
            await db.close();
            
        } catch (error) {
            console.log(`   ❌ 分析失败: ${error.message}`);
        }
        
        console.log('');
    }
    
    return keyAnalysis;
}

// 分析特定对话的键顺序
async function analyzeConversationKeyOrder(conversationId = 'b78bd351-4c2b-4891-9ba8-f1d7a869d74b') {
    console.log(`🎯 分析对话的键顺序: ${conversationId.substring(0, 8)}...\n`);
    
    const workspaces = await findWorkspaces();
    let targetKeys = [];
    
    for (const workspace of workspaces) {
        try {
            const dbPath = path.join(workspace.augmentPath, 'conversations');
            if (!fs.existsSync(dbPath)) continue;
            
            const db = level(dbPath, { valueEncoding: 'json' });
            
            // 收集目标对话的所有键
            for await (const [key, value] of db.iterator()) {
                if (key.startsWith(`exchange:${conversationId}:`)) {
                    const parts = key.split(':');
                    const exchangeId = parts[2];
                    
                    targetKeys.push({
                        key: key,
                        exchangeId: exchangeId,
                        value: value,
                        timestamp: value.timestamp || null,
                        request_message: (value.request_message || '').substring(0, 50),
                        response_text: (value.response_text || '').substring(0, 50)
                    });
                }
            }
            
            await db.close();
            
        } catch (error) {
            console.log(`   ❌ 工作区分析失败: ${error.message}`);
        }
    }
    
    console.log(`📊 找到 ${targetKeys.length} 个exchange键\n`);
    
    if (targetKeys.length === 0) {
        console.log('❌ 未找到目标对话的键');
        return null;
    }
    
    // 分析键的自然顺序（数据库中的存储顺序）
    console.log('🔍 数据库中的自然顺序（前10个）:');
    targetKeys.slice(0, 10).forEach((item, index) => {
        console.log(`${index + 1}. ${item.exchangeId}`);
        console.log(`   请求: ${item.request_message}...`);
        console.log(`   响应: ${item.response_text}...`);
        console.log('');
    });
    
    // 尝试不同的排序方法
    console.log('🧪 测试不同的排序方法:\n');
    
    // 1. 按exchangeId字符串排序
    const sortedByStringId = [...targetKeys].sort((a, b) => a.exchangeId.localeCompare(b.exchangeId));
    console.log('1. 按exchangeId字符串排序（前5个）:');
    sortedByStringId.slice(0, 5).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.exchangeId} - ${item.request_message}...`);
    });
    
    // 2. 按exchangeId数字排序（如果是数字）
    const numericIds = targetKeys.filter(item => /^\d+$/.test(item.exchangeId));
    if (numericIds.length > 0) {
        const sortedByNumericId = numericIds.sort((a, b) => parseInt(a.exchangeId, 10) - parseInt(b.exchangeId, 10));
        console.log('\n2. 按exchangeId数字排序（前5个）:');
        sortedByNumericId.slice(0, 5).forEach((item, index) => {
            console.log(`   ${index + 1}. ${item.exchangeId} - ${item.request_message}...`);
        });
    }
    
    // 3. 按UUID的第一部分排序
    const uuidIds = targetKeys.filter(item => /^[0-9a-f]{8}-/.test(item.exchangeId));
    if (uuidIds.length > 0) {
        const sortedByUuidFirst = uuidIds.sort((a, b) => {
            const aFirst = parseInt(a.exchangeId.split('-')[0], 16);
            const bFirst = parseInt(b.exchangeId.split('-')[0], 16);
            return aFirst - bFirst;
        });
        console.log('\n3. 按UUID第一部分排序（前5个）:');
        sortedByUuidFirst.slice(0, 5).forEach((item, index) => {
            const firstPart = item.exchangeId.split('-')[0];
            const hexValue = parseInt(firstPart, 16);
            console.log(`   ${index + 1}. ${item.exchangeId} (${firstPart}=${hexValue}) - ${item.request_message}...`);
        });
    }
    
    // 4. 按timestamp排序（如果有）
    const timestampItems = targetKeys.filter(item => item.timestamp);
    if (timestampItems.length > 0) {
        const sortedByTimestamp = timestampItems.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        console.log('\n4. 按timestamp排序（前5个）:');
        sortedByTimestamp.slice(0, 5).forEach((item, index) => {
            console.log(`   ${index + 1}. ${item.timestamp} - ${item.request_message}...`);
        });
    }
    
    // 5. 分析键在数据库中的物理顺序
    console.log('\n5. 数据库物理顺序分析:');
    console.log('   这可能就是插件使用的顺序！');
    console.log('   前5个键:');
    targetKeys.slice(0, 5).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.exchangeId} - ${item.request_message}...`);
    });
    
    console.log('\n   最后5个键:');
    targetKeys.slice(-5).forEach((item, index) => {
        console.log(`   ${targetKeys.length - 4 + index}. ${item.exchangeId} - ${item.request_message}...`);
    });
    
    return targetKeys;
}

// 查找VSCode工作区
async function findWorkspaces() {
    const userDataPaths = [
        path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User', 'workspaceStorage'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Code', 'User', 'workspaceStorage'),
        path.join(os.homedir(), '.config', 'Code', 'User', 'workspaceStorage')
    ];
    
    const workspaces = [];
    
    for (const basePath of userDataPaths) {
        if (fs.existsSync(basePath)) {
            const dirs = fs.readdirSync(basePath);
            for (const dir of dirs) {
                const workspacePath = path.join(basePath, dir);
                const augmentPath = path.join(workspacePath, 'augmentcode.augment');
                
                if (fs.existsSync(augmentPath)) {
                    workspaces.push({
                        id: dir,
                        path: workspacePath,
                        augmentPath: augmentPath
                    });
                }
            }
            break; // 找到第一个存在的路径就停止
        }
    }
    
    return workspaces;
}

// 推断正确的排序逻辑
function inferSortingLogic(keyAnalysis, conversationKeys) {
    console.log('\n💡 推断正确的排序逻辑:');
    console.log('================================');
    
    if (!conversationKeys || conversationKeys.length === 0) {
        console.log('❌ 没有足够的数据进行推断');
        return null;
    }
    
    // 分析exchangeId的模式
    const exchangeIds = conversationKeys.map(item => item.exchangeId);
    const numericIds = exchangeIds.filter(id => /^\d+$/.test(id));
    const uuidIds = exchangeIds.filter(id => /^[0-9a-f]{8}-/.test(id));
    
    console.log(`📊 数据分析:`);
    console.log(`   总exchangeId数: ${exchangeIds.length}`);
    console.log(`   纯数字ID: ${numericIds.length} (${(numericIds.length/exchangeIds.length*100).toFixed(1)}%)`);
    console.log(`   UUID格式ID: ${uuidIds.length} (${(uuidIds.length/exchangeIds.length*100).toFixed(1)}%)`);
    
    // 基于分析结果推断最佳排序方法
    let recommendedSorting = null;
    
    if (numericIds.length > exchangeIds.length * 0.8) {
        recommendedSorting = 'numeric';
        console.log('\n✅ 推荐排序方法: 数字排序');
        console.log('   原因: 超过80%的exchangeId是纯数字');
        console.log('   实现: parseInt(exchangeId, 10)');
    } else if (uuidIds.length > exchangeIds.length * 0.8) {
        recommendedSorting = 'uuid-hex';
        console.log('\n✅ 推荐排序方法: UUID十六进制排序');
        console.log('   原因: 超过80%的exchangeId是UUID格式');
        console.log('   实现: parseInt(exchangeId.split("-")[0], 16)');
    } else {
        recommendedSorting = 'database-order';
        console.log('\n✅ 推荐排序方法: 数据库物理顺序');
        console.log('   原因: exchangeId格式混合，使用数据库迭代顺序');
        console.log('   实现: 保持数据库iterator的自然顺序');
    }
    
    return recommendedSorting;
}

// 主函数
async function main() {
    console.log('🔧 反向工程Augment插件排序逻辑');
    console.log('================================\n');
    
    try {
        // 1. 分析键的模式
        const keyAnalysis = await analyzeKeyPatterns();
        
        // 2. 分析特定对话的键顺序
        const conversationKeys = await analyzeConversationKeyOrder();
        
        // 3. 推断正确的排序逻辑
        const sortingLogic = inferSortingLogic(keyAnalysis, conversationKeys);
        
        console.log('\n🎯 结论和建议:');
        console.log('================================');
        console.log('1. 插件很可能使用数据库的自然迭代顺序');
        console.log('2. LevelDB按键的字典序存储数据');
        console.log('3. exchange:conversationId:exchangeId 的键结构');
        console.log('4. 我们应该模拟相同的迭代顺序');
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
    }
}

// 运行分析
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { analyzeKeyPatterns, analyzeConversationKeyOrder };
