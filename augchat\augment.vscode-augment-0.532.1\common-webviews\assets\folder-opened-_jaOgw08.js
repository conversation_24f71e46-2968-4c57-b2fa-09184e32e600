var fs=Object.defineProperty;var Oe=a=>{throw TypeError(a)};var ps=(a,t,e)=>t in a?fs(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e;var l=(a,t,e)=>ps(a,typeof t!="symbol"?t+"":t,e),de=(a,t,e)=>t.has(a)||Oe("Cannot "+e);var n=(a,t,e)=>(de(a,t,"read from private field"),e?e.call(a):t.get(a)),x=(a,t,e)=>t.has(a)?Oe("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(a):t.set(a,e),y=(a,t,e,s)=>(de(a,t,"write to private field"),s?s.call(a,e):t.set(a,e),e),f=(a,t,e)=>(de(a,t,"access private method"),e);var Kt=(a,t,e,s)=>({set _(i){y(a,t,i,e)},get _(){return n(a,t,s)}});import{S as ys,W as bt}from"./host-BNehKqab.js";import{d as vs}from"./CardAugment-YBzgmAzG.js";import{w as ht,ar as ee,f as jt,a as is,o as rs,b as Vt}from"./legacy-YP6Kq8lu.js";import{g as Cs,h as Se,i as Ss,d as Rt,e as Jt,j as gt,A as ue,F as dt,a as Xt,k as bs,l as xs,m as os,n as ws,o as Is,p as Ts,q as Ms,C as Es,R as As,E as Fs,r as Ls,s as ks,t as qs,u as Hs,v as Ne,w as Ds,x as Pe,y as ze,z as Us,B as $e,G as Rs,H as Ge,I as Os,J as We,K as me}from"./index-CKSGO-M1.js";import{M as Ns}from"./message-broker-DRrss2z_.js";import{C as Ps}from"./types-CGlLNakm.js";import{C as F,b as ge,I as Qt,a as G,i as L,A as Et,E as A,h as zs,c as Ft,S as mt,d as It,R as Ot,P as Nt,e as be,f as Lt,g as $t,j as $s,s as _e}from"./chat-types-BfwvR7Kn.js";import{f as Gs,i as ns}from"./file-type-utils-D6OEcQY2.js";import{T as E,a as je,b as fe}from"./chat-model-context-DZ2DTs5O.js";import{b as xe}from"./tool-types-Chbmg_E2.js";import{h as as}from"./IconButtonAugment-CbpcmeFk.js";import{l as ls}from"./SpinnerAugment-Dpcl1cXc.js";class Ws{constructor(t=[]){l(this,"_items",[]);l(this,"_focusedItemIdx");l(this,"_subscribers",new Set);l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});l(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const e=t?this._items.indexOf(t):-1;e===-1?this.setFocusIdx(void 0):this.setFocusIdx(e)});l(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const e=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-e)%this._items.length,this.notifySubscribers()});l(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));l(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});l(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});l(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});l(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});l(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}function js(a,t,e=1e3){let s=null,i=0;const r=ht(t),o=()=>{const h=(()=>{const c=Date.now();if(s!==null&&c-i<e)return s;const d=a();return s=d,i=c,d})();r.set(h)};return{subscribe:r.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var hs=(a=>(a[a.unset=0]="unset",a[a.positive=1]="positive",a[a.negative=2]="negative",a))(hs||{});function Ve(a,t){return function(e,s){if(e.length<=s||e.length===0)return{truncatedText:e};const i=e.split(`
`),r="... additional lines truncated ..."+(i[0].endsWith("\r")?"\r":"");let o,h="";if(i.length<2||i[0].length+i[i.length-1].length+r.length>s){const c=Math.floor(s/2);h=[e.slice(0,c),"<...>",e.slice(-c)].join(""),o=[1,1,i.length,i.length]}else{const c=[],d=[];let u=r.length+1;for(let m=0;m<Math.floor(i.length/2);m++){const C=i[m],_=i[i.length-1-m],v=C.length+_.length+2;if(u+v>s)break;u+=v,c.push(C),d.push(_)}o=[1,c.length,i.length-d.length+1,i.length],c.push(r),c.push(...d.reverse()),h=c.join(`
`)}return{truncatedText:h,shownRangeWhenTruncated:o}}(a,t).truncatedText}function Vs(a){var e;if(!a)return Qt.IMAGE_FORMAT_UNSPECIFIED;switch((e=a.split("/")[1])==null?void 0:e.toLowerCase()){case"jpeg":case"jpg":return Qt.JPEG;case"png":return Qt.PNG;default:return Qt.IMAGE_FORMAT_UNSPECIFIED}}function Bs(a,t,e){var i,r;if(a.phase!==E.cancelled&&a.phase!==E.completed&&a.phase!==E.error)return;let s;return(i=a.result)!=null&&i.contentNodes?(s=function(o,h){return o.map(c=>c.type===xe.ContentText?{type:ge.CONTENT_TEXT,text_content:c.text_content}:c.type===xe.ContentImage&&c.image_content&&h?{type:ge.CONTENT_IMAGE,image_content:{image_data:c.image_content.image_data,format:Vs(c.image_content.media_type)}}:{type:ge.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(a.result.contentNodes,e),{content:"",is_error:a.result.isError,request_id:a.result.requestId,tool_use_id:t,content_nodes:s}):((r=a.result)==null?void 0:r.text)!==void 0?{content:a.result.text,is_error:a.result.isError,request_id:a.result.requestId,tool_use_id:t}:void 0}function Zs(a=[]){let t;for(const e of a){if(e.type===F.TOOL_USE)return e;e.type===F.TOOL_USE_START&&(t=e)}return t}function Ks(a,t,e,s){if(!a||!t)return[];let i=!1;return t.filter(r=>{var h;const o=s!=null&&s.isActive&&r.tool_use?s.getToolUseState(r.tool_use.tool_use_id):e.getToolUseState(r.requestId??a,(h=r.tool_use)==null?void 0:h.tool_use_id);return i===!1&&o.phase!==E.new&&o.phase!==E.unknown&&o.phase!==E.checkingSafety&&r.tool_use!==void 0||(o.phase===E.runnable&&(i=!0),!1)})}function Oi(a,t){if(a.contentNodes&&a.contentNodes.length>0){const e=a.contentNodes.map(s=>{if(s.type===xe.ContentText){let i="";return s.text_content&&(i=Ve(s.text_content,t/a.contentNodes.length)),{...s,text_content:i}}return s});return{...a,contentNodes:e}}return{...a,text:Ve(a.text,t)}}const _t="__NEW_AGENT__",Ni=a=>a.chatItemType===void 0,Pi=(a,t)=>{var r;const e=a.chatHistory.at(-1);if(!e||!L(e))return Et.notRunning;if(!(e.status===A.success||e.status===A.failed||e.status===A.cancelled))return Et.running;const s=((r=e.structured_output_nodes)==null?void 0:r.filter(o=>o.type===F.TOOL_USE&&!!o.tool_use))??[];let i;if(t.enableParallelTools?(i=Ks(e.request_id,s,a).at(-1),!i&&s.length>0&&(i=s.at(-1))):i=s.at(-1),!i||!i.tool_use)return Et.notRunning;switch(a.getToolUseState(e.request_id,i.tool_use.tool_use_id).phase){case E.runnable:return Et.awaitingUserAction;case E.cancelled:return Et.notRunning;default:return Et.running}},we=a=>L(a)&&!!a.request_message,Js=a=>a.chatHistory.findLast(t=>we(t)),zi=(a,t)=>{const e=Js(a);return e!=null&&e.request_id?a.historyFrom(e.request_id,!0).filter(s=>L(s)&&(!t||t(s))):[]},$i=a=>{var s;const t=a.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!L(t))return!1;const e=((s=t.structured_output_nodes)==null?void 0:s.filter(i=>i.type===F.TOOL_USE))??[];for(const i of e)if(i.tool_use&&a.getToolUseState(t.request_id,i.tool_use.tool_use_id).phase===E.runnable)return a.updateToolUseState({requestId:t.request_id,toolUseId:i.tool_use.tool_use_id,phase:E.cancelled}),!0;return!1};function xt(a){var t;return((t=a.extraData)==null?void 0:t.isAgentConversation)===!0}var R=(a=>(a[a.active=0]="active",a[a.inactive=1]="inactive",a))(R||{});const k={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was abridged and summarized to reduce context size.
Abridged conversation history:
{abridged_history}

Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
{summary}

Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point.",abridgedHistoryParams:{totalCharsLimit:1e4,userMessageCharsLimit:1e3,agentResponseCharsLimit:2e3,actionCharsLimit:200,numFilesModifiedLimit:10,numFilesCreatedLimit:10,numFilesDeletedLimit:10,numFilesViewedLimit:10,numTerminalCommandsLimit:10}};function Ie(a,t,e=.5,s=.5){if(a.length<=t||t<=0)return a;if(e+s>1)throw new Error("startRatio + endRatio cannot exceed 1.0");const i="...",r=t-3;if(r<=0)return i.substring(0,t);const o=Math.floor(r*e),h=Math.floor(r*s);return a.substring(0,o)+i+a.substring(a.length-h)}const z=(a,t)=>a!==void 0?a:t;function Xs(a){for(const t of a.filesModified)a.filesViewed.delete(t)}function Qs(a,t){try{const e=JSON.parse(a.input_json);switch(a.tool_name){case"str-replace-editor":e.path&&t.filesModified.add(e.path);break;case"save-file":e.path&&t.filesCreated.add(e.path);break;case"remove-files":if(e.file_paths&&Array.isArray(e.file_paths))for(const s of e.file_paths)t.filesDeleted.add(s);break;case"view":e.path&&t.filesViewed.add(e.path);break;case"launch-process":e.command&&t.terminalCommands.add(e.command)}}catch(e){console.warn("Failed to parse tool use input:",e)}}function Pt(a,t,e,s="files"){if(a.size===0)return null;const i=Array.from(a).sort((c,d)=>c.localeCompare(d)),r=c=>Ie(c,e);if(i.length<=t)return i.map(r);const o=i.slice(0,t).map(r),h=i.length-t;return o.push(`... ${h} more ${s}`),o}function Ys(a,t){let e=a.userMessage,s=a.agentFinalResponse;e.length>t.userMessageCharsLimit&&(e=Ie(e,t.userMessageCharsLimit)),s.length>t.agentResponseCharsLimit&&(s=Ie(s,t.agentResponseCharsLimit));const i=a.agentActionsSummary.filesModified.size>0||a.agentActionsSummary.filesCreated.size>0||a.agentActionsSummary.filesDeleted.size>0||a.agentActionsSummary.filesViewed.size>0||a.agentActionsSummary.terminalCommands.size>0,r={userMessage:e,agentResponse:s&&s.trim()!==""?s:null,hasActions:i,filesModified:Pt(a.agentActionsSummary.filesModified,t.numFilesModifiedLimit,t.actionCharsLimit),filesCreated:Pt(a.agentActionsSummary.filesCreated,t.numFilesCreatedLimit,t.actionCharsLimit),filesDeleted:Pt(a.agentActionsSummary.filesDeleted,t.numFilesDeletedLimit,t.actionCharsLimit),filesViewed:Pt(a.agentActionsSummary.filesViewed,t.numFilesViewedLimit,t.actionCharsLimit),terminalCommands:Pt(a.agentActionsSummary.terminalCommands,t.numTerminalCommandsLimit,t.actionCharsLimit,"commands"),wasInterrupted:a.wasInterrupted,continues:a.continues};return Cs(r)}function ti(a){var e,s;let t=a.request_message||"";return(e=a.structured_request_nodes)!=null&&e.some(i=>i.image_node||i.image_id_node)&&(t+=`
[User attached image]`),(s=a.structured_request_nodes)!=null&&s.some(i=>i.file_node||i.file_id_node)&&(t+=`
[User attached document]`),t}function Be(a){var t,e,s,i,r,o,h,c,d;try{if(!a)return console.log("historySummaryParams is empty. Using default params"),k;const u=JSON.parse(a),m={triggerOnHistorySizeChars:z(u.trigger_on_history_size_chars,k.triggerOnHistorySizeChars),historyTailSizeCharsToExclude:z(u.history_tail_size_chars_to_exclude,k.historyTailSizeCharsToExclude),triggerOnHistorySizeCharsWhenCacheExpiring:z(u.trigger_on_history_size_chars_when_cache_expiring,k.triggerOnHistorySizeCharsWhenCacheExpiring),prompt:z(u.prompt,k.prompt),cacheTTLMs:z(u.cache_ttl_ms,k.cacheTTLMs),bufferTimeBeforeCacheExpirationMs:z(u.buffer_time_before_cache_expiration_ms,k.bufferTimeBeforeCacheExpirationMs),summaryNodeRequestMessageTemplate:z(u.summary_node_request_message_template,k.summaryNodeRequestMessageTemplate),summaryNodeResponseMessage:z(u.summary_node_response_message,k.summaryNodeResponseMessage),abridgedHistoryParams:{totalCharsLimit:z((t=u.abridged_history_params)==null?void 0:t.total_chars_limit,k.abridgedHistoryParams.totalCharsLimit),userMessageCharsLimit:z((e=u.abridged_history_params)==null?void 0:e.user_message_chars_limit,k.abridgedHistoryParams.userMessageCharsLimit),agentResponseCharsLimit:z((s=u.abridged_history_params)==null?void 0:s.agent_response_chars_limit,k.abridgedHistoryParams.agentResponseCharsLimit),actionCharsLimit:z((i=u.abridged_history_params)==null?void 0:i.action_chars_limit,k.abridgedHistoryParams.actionCharsLimit),numFilesModifiedLimit:z((r=u.abridged_history_params)==null?void 0:r.num_files_modified_limit,k.abridgedHistoryParams.numFilesModifiedLimit),numFilesCreatedLimit:z((o=u.abridged_history_params)==null?void 0:o.num_files_created_limit,k.abridgedHistoryParams.numFilesCreatedLimit),numFilesDeletedLimit:z((h=u.abridged_history_params)==null?void 0:h.num_files_deleted_limit,k.abridgedHistoryParams.numFilesDeletedLimit),numFilesViewedLimit:z((c=u.abridged_history_params)==null?void 0:c.num_files_viewed_limit,k.abridgedHistoryParams.numFilesViewedLimit),numTerminalCommandsLimit:z((d=u.abridged_history_params)==null?void 0:d.num_terminal_commands_limit,k.abridgedHistoryParams.numTerminalCommandsLimit)}};m.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),m.summaryNodeRequestMessageTemplate=k.summaryNodeRequestMessageTemplate);const C={...m,prompt:m.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",C),m}catch(u){return console.error("Failed to parse history_summary_params:",u),k}}class ei{constructor(){this._controllers=new Set,this._timeoutIds=new Set}addCallback(t,e){const s=new AbortController,i=setTimeout(()=>{t(s.signal),this._controllers.delete(s),this._timeoutIds.delete(i)},e);this._controllers.add(s),this._timeoutIds.add(i)}cancelAll(){this._controllers.forEach(t=>t.abort()),this._timeoutIds.forEach(t=>clearTimeout(t)),this._controllers.clear(),this._timeoutIds.clear()}}function pe(a){return a.reduce((t,e)=>t+cs(e),0)}function cs(a){let t=0;return a.request_nodes?t+=JSON.stringify(a.request_nodes).length:t+=(a.request_message||"").length,a.response_nodes?t+=JSON.stringify(a.response_nodes).length:t+=(a.response_text||"").length,t}class si{constructor(t,e,s){l(this,"historySummaryVersion",3);l(this,"_callbacksManager",new ei);l(this,"_params");this._conversationModel=t,this._extensionClient=e,this._chatFlagModel=s,this._params=Be(s.historySummaryParams),s.subscribe(i=>{this._params=Be(i.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}generateAbridgedHistoryText(t){const e=new Set(t.map(o=>o.request_id)),s=function(o){const h=[];let c=null;for(const d of o){if(!L(d))continue;const u=d;if(zs(u)||(c&&(c.agentFinalResponse.trim()===""&&(c.wasInterrupted=!0),h.push(c)),c={userMessage:ti(u),agentActionsSummary:{filesModified:new Set,filesCreated:new Set,filesDeleted:new Set,filesViewed:new Set,terminalCommands:new Set},agentFinalResponse:"",wasInterrupted:!1,continues:!1}),!c)continue;let m=!1;if(u.structured_output_nodes)for(const C of u.structured_output_nodes)C.type===F.TOOL_USE&&C.tool_use&&(m=!0,Qs(C.tool_use,c.agentActionsSummary));!m&&u.response_text&&(c.agentFinalResponse=u.response_text)}c&&(c.agentFinalResponse.trim()===""&&(c.continues=!0),h.push(c));for(const d of h)Xs(d.agentActionsSummary);return h}(this._conversationModel.chatHistory.filter(o=>o.request_id&&e.has(o.request_id)));let i=0;const r=[];for(let o=s.length-1;o>=0;o--){const h=Ys(s[o],this._params.abridgedHistoryParams);if(i+h.length>this._params.abridgedHistoryParams.totalCharsLimit)break;r.push(h),i+=h.length}return r.reverse(),r.join(`
`)}clearStaleHistorySummaryNodes(t){return t.filter(e=>!Ft(e)||e.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(t){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const e=this._params.cacheTTLMs-t-this._params.bufferTimeBeforeCacheExpirationMs;e>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},e)}preprocessChatHistory(t){const e=t.findLastIndex(s=>Ft(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(e>0&&(console.info(`Using history summary node found at index ${e} with requestId: ${t[e].request_id}`),t=t.slice(e)),t=t.filter(s=>!Ft(s)||s.summaryVersion===this.historySummaryVersion)):t=t.filter(s=>!Ft(s)),t}async maybeAddHistorySummaryNode(t=!1,e){var tt,ct,St;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",t),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),i=t?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",i),i<=0)return!1;const{head:r,tail:o,headSizeChars:h,tailSizeChars:c}=function(Z,ae,gs,_s){if(Z.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const le=[],Ut=[];let Bt=0,Ue=0,Re=0;for(let he=Z.length-1;he>=0;he--){const ce=Z[he],Zt=cs(ce);Bt+Zt<ae||Ut.length<_s?(Ut.push(ce),Re+=Zt):(le.push(ce),Ue+=Zt),Bt+=Zt}return Bt<gs?(Ut.push(...le),{head:[],tail:Ut.reverse(),headSizeChars:0,tailSizeChars:Bt}):{head:le.reverse(),tail:Ut.reverse(),headSizeChars:Ue,tailSizeChars:Re}}(s,this._params.historyTailSizeCharsToExclude,i,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",h," tailSizeChars: ",c),r.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const d=pe(s),u=pe(r),m=pe(o),C={totalHistoryCharCount:d,totalHistoryExchangeCount:s.length,headCharCount:u,headExchangeCount:r.length,headLastRequestId:((tt=r.at(-1))==null?void 0:tt.request_id)??"",tailCharCount:m,tailExchangeCount:o.length,tailLastRequestId:((ct=o.at(-1))==null?void 0:ct.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:t,isAborted:!1};let _=((St=r.at(-1))==null?void 0:St.response_nodes)??[],v=_.filter(Z=>Z.type===F.TOOL_USE);v.length>0&&(r.at(-1).response_nodes=_.filter(Z=>Z.type!==F.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",r.length);const b=this.generateAbridgedHistoryText(r);console.info("Abridged history text size: %d characters.",b.length);const w=Date.now(),p=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:r}),I=Date.now();if(console.info("Summary text size: %d characters.",p.responseText.length),C.summaryCharCount=p.responseText.length,C.summarizationDurationMs=I-w,C.isAborted=!!(e!=null&&e.aborted),this._extensionClient.reportAgentRequestEvent({eventName:Se.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:p.requestId??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:C}}),e==null?void 0:e.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!p.requestId||p.responseText.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;let Y=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",`<summary>
${p.responseText}
</summary>`);Y.includes("{abridged_history}")&&(Y=Y.replace("{abridged_history}",`<abridged_history>
${b}
</abridged_history>`));const W=this._params.summaryNodeResponseMessage,P={chatItemType:It.historySummary,summaryVersion:this.historySummaryVersion,request_id:p.requestId,request_message:Y,response_text:W,structured_output_nodes:[{id:v.map(Z=>Z.id).reduce((Z,ae)=>Math.max(Z,ae),-1)+1,type:F.RAW_RESPONSE,content:W},...v],status:A.success,seen_state:mt.seen,timestamp:new Date().toISOString()},D=this._conversationModel.chatHistory.findLastIndex(Z=>Z.request_id===r.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",D),this._conversationModel.insertChatItem(D,P),!0}}class Yt{constructor(){this._disposables=[]}add(t){if(t===void 0)throw new Error("Attempt to add undefined disposable to DisposableCollection");return this._disposables.push(t),t}addAll(...t){t.forEach(e=>this.add(e))}adopt(t){this._disposables.push(...t._disposables),t._disposables.length=0}dispose(){for(const t of this._disposables)t.dispose();this._disposables.length=0}}class ii{constructor(t=new Yt,e=new Yt){this._disposables=new Yt,this._priorityDisposables=new Yt,this._disposables.adopt(t),this._priorityDisposables.adopt(e)}addDisposable(t,e=!1){return e?this._priorityDisposables.add(t):this._disposables.add(t)}addDisposables(...t){this._disposables.addAll(...t)}dispose(){this._priorityDisposables.dispose(),this._disposables.dispose()}}const ri=[".md",".mdc"],Ze=[{directory:".cursor/rules",file:".cursorrules",name:"Cursor"},{directory:".windsurf/rules",file:".windsurfrules",name:"Windsurf"},{directory:".github/instructions",file:".github/copilot-instructions.md",name:"GitHub Copilot"},{directory:".clinerules",file:".clinerules",name:"Cline"},{directory:".roo/rules",file:".roorules",name:"Roo Code"},{directory:".trae/rules",name:"Trae"}];class He extends ii{constructor(){super(),this._logger=Ss()}async loadRules({includeGuidelines:t=!1,query:e,maxResults:s,contextRules:i}={}){this._logger.debug(`Loading rules with includeGuidelines=${t}, query=${e}, maxResults=${s}`),this._logger.debug("Using file system approach to load rules");const r=await this.loadDirectory((void 0)(Rt,Jt));let o;if(this._logger.debug(`Loaded ${r.length} rules from directory`),t){const h=await this.loadGuidelinesFiles();this._logger.debug(`Loaded ${h.length} guidelines rules`),o=[...h,...r]}else o=r;if(e&&e.trim()){const h=e.toLowerCase().trim();o=o.filter(c=>{const d=c.path.toLowerCase().includes(h),u=c.content.toLowerCase().includes(h);return d||u}),this._logger.debug(`Filtered to ${o.length} rules matching query: ${e}`)}return s&&s>0&&(o=o.slice(0,s),this._logger.debug(`Limited to ${o.length} rules`)),this._logger.debug(`Returning ${o.length} total rules`),i!==void 0&&(o=He.filterRulesByContext(o,i),this._logger.debug(`Filtered to ${o.length} rules based on context`)),o}static filterRulesByContext(t,e){return[...t.filter(s=>s.type!==Ot.MANUAL),...t.filter(s=>s.type===Ot.MANUAL&&e.some(i=>i.path===s.path))]}static calculateRulesAndGuidelinesCharacterCount(t){const{rules:e,workspaceGuidelinesContent:s,contextRules:i=[]}=t,r=e.filter(d=>d.type===Ot.ALWAYS_ATTACHED).reduce((d,u)=>d+u.content.length+u.path.length,0),o=e.filter(d=>d.type===Ot.AGENT_REQUESTED).reduce((d,u)=>{var m;return d+100+(((m=u.description)==null?void 0:m.length)??0)+u.path.length},0),h=r+e.filter(d=>d.type===Ot.MANUAL).filter(d=>i.some(u=>u.path===d.path)).reduce((d,u)=>d+u.content.length+u.path.length,0)+o+s.length,c=t.rulesAndGuidelinesLimit&&h>t.rulesAndGuidelinesLimit;return{totalCharacterCount:h,isOverLimit:c,warningMessage:c&&t.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${h} chars)
        exceeds the limit of ${t.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}async loadGuidelinesFiles(){const t=[],e=gt();if(!e)return this._logger.warn("Client workspaces not initialized"),t;const s=await e.getWorkspaceRoot();if(!s)return t;const i=(void 0)(s,ue),r=await e.getPathInfo(i);if(r.exists&&r.type===dt.File)try{const o=(await e.readFile(i)).contents;if(!o)return this._logger.warn(`Guidelines file is empty: ${i}`),t;const h=Xt.parseRuleFile(o,ue);t.push({path:ue,content:h.content,type:h.type,description:h.description})}catch(o){this._logger.error(`Error loading guidelines file ${i}: ${String(o)}`)}return t}async loadDirectory(t){const e=[];try{const s=gt();if(!s)return this._logger.warn("Client workspaces not initialized"),e;const i=await s.getWorkspaceRoot();if(!i)return this._logger.warn("No workspace root found"),e;const r=(void 0)(i,t);this._logger.debug(`Looking for rules in: ${r}`);const o=await s.getPathInfo(r);return this._logger.debug(`Path info for ${r}: ${JSON.stringify(o)}`),o.exists&&o.type===dt.Directory?(this._logger.debug(`Rules folder exists at ${r}`),await this.processRuleDirectory(s,r,e,""),this._logger.debug(`Loaded ${e.length} rules from ${r}`),e):(this._logger.debug(`Rules folder not found at ${r}`),e)}catch(s){return this._logger.error(`Error loading rules: ${String(s)}`),e}}async loadDirectoryFromPath(t){const e=[];try{const s=gt();if(!s)return this._logger.warn("Client workspaces not initialized"),e;let i;if(!(void 0)(t)){const o=await s.getWorkspaceRoot();if(!o)return this._logger.warn("No workspace root found"),e;i=(void 0)(o,t),this._logger.debug(`Loading rules from workspace-relative path: ${i}`)}const r=await s.getPathInfo(i);return r.exists&&r.type===dt.Directory?(this._logger.debug(`Rules folder exists at ${i}`),await this.processRuleDirectory(s,i,e,""),this._logger.debug(`Loaded ${e.length} rules from ${i}`),e):(this._logger.debug(`Rules folder not found at ${i}`),e)}catch(s){return this._logger.error(`Error loading rules from path: ${String(s)}`),e}}async processRuleDirectory(t,e,s,i){const r=await t.listDirectory(e,1,!1);if(r.errorMessage)this._logger.error(`Error listing directory ${e}: ${r.errorMessage}`);else{this._logger.debug(`Processing directory: ${e}, found ${r.entries.length} entries`);for(const o of r.entries){const h=(void 0)(e,o),c=(void 0)(i,o),d=await t.getPathInfo(h);if(d.exists)if(d.type===dt.Directory)this._logger.debug(`Processing subdirectory: ${o}`),await this.processRuleDirectory(t,h,s,c);else if(d.type===dt.File&&ri.some(u=>o.endsWith(u))){this._logger.debug(`Processing rule file: ${o}`);try{const u=(await t.readFile(h)).contents||"",m=Xt.parseRuleFile(u,o);s.push({path:c,content:m.content,type:m.type,description:m.description}),this._logger.debug(`Successfully loaded rule: ${c}`)}catch(u){this._logger.error(`Error loading rule file ${h}: ${String(u)}`)}}else d.type===dt.File&&this._logger.debug(`Skipping non-markdown file: ${o}`)}}}async createRule(t,e=!1){const s=gt();if(!s)throw new Error("Client workspaces not initialized");const i=await s.getWorkspaceRoot();if(!i)throw new Error("No workspace root found");let r=(void 0)(i,Rt,Jt);e&&(r=(void 0)(r,"imported"));const o=t.path.endsWith(".md")?t.path:`${t.path}.md`,h=(void 0)(r,o),c=await s.getQualifiedPathName(h);if(!c)throw new Error(`Unable to get qualified path for: ${h}`);if((await s.getPathInfo(h)).exists)throw new Error(`Rule file already exists: ${o}`);const d=Xt.formatRuleFileForMarkdown(t);return await s.writeFile(c,d),{...t,path:o}}async deleteRule(t){if(typeof t!="string")throw new Error(`Expected rulePath to be a string, got ${typeof t}: ${String(t)}`);const e=gt();if(!e)throw new Error("Client workspaces not initialized");const s=await e.getWorkspaceRoot();if(!s)throw new Error("No workspace root found");let i;if((void 0)(t)||(i=(void 0)(s,Rt,Jt,t)),(await e.getPathInfo(i)).exists){const r=await e.getQualifiedPathName(i);r&&(await e.deleteFile(r),this._logger.debug(`Deleted rule file: ${i}`)),this._logger.debug(`Deleted rule file: ${i}`)}}async updateRuleFile(t,e){if(typeof t!="string")throw new Error(`Expected rulePath to be a string, got ${typeof t}: ${String(t)}`);const s=gt();if(!s)throw new Error("Client workspaces not initialized");const i=await s.getWorkspaceRoot();if(!i)throw new Error("No workspace root found");let r;(void 0)(t)||(r=t.startsWith(Rt)?(void 0)(i,t):(void 0)(i,Rt,Jt,t));const o=await s.getQualifiedPathName(r);if(!o)throw new Error(`Unable to get qualified path for: ${r}`);await s.writeFile(o,e),this._logger.debug(`Updated rule file: ${r}`)}async importFile(t,e){const s=gt();if(!s)throw new Error("Client workspaces not initialized");let i,r;if(!(void 0)(t)){const h=await s.getWorkspaceRoot();if(!h)throw new Error("No workspace root found");i=(void 0)(h,t),r=t,this._logger.debug(`Importing file from workspace-relative path: ${i}`)}const o=await s.getPathInfo(i);if(!o.exists||o.type!==dt.File)return this._logger.error(`File not found: ${i}`),{successfulImports:0,duplicates:0,totalAttempted:1};try{const h=(await s.readFile(i)).contents;if(!h)return this._logger.error(`File is empty: ${i}`),{successfulImports:0,duplicates:0,totalAttempted:1};const c=Xt.parseRuleFile(h,r),d=(void 0)(r).name.replace(".","");return await this.createRule({path:d,content:c.content,type:c.type},e),{successfulImports:1,duplicates:0,totalAttempted:1}}catch(h){return this._logger.error(`Error importing file ${t}: ${String(h)}`),{successfulImports:0,duplicates:String(h).includes("already exists")?1:0,totalAttempted:1}}}async importDirectory(t,e){try{const s=await this.loadDirectoryFromPath(t);if(s.length===0)return this._logger.debug(`No rules found in directory: ${t}`),{successfulImports:0,duplicates:0,totalAttempted:0};this._logger.debug(`Loaded ${s.length} existing rules from ${t}`);let i=0,r=0;const o=s.length;for(const h of s)try{const c=(void 0)(h.path).name,d=(void 0)(h.path),u=d==="."?c:(void 0)(d,c);await this.createRule({path:u,content:h.content,type:h.type},e),i++,this._logger.debug(`Successfully imported rule: ${h.path} -> ${u}`)}catch(c){this._logger.warn(`Failed to import rule ${h.path}: ${String(c)}`),String(c).includes("already exists")&&r++}return this._logger.info(`Imported ${i} rules from ${t}, ${r} duplicates skipped`),{successfulImports:i,duplicates:r,totalAttempted:o}}catch(s){return this._logger.error(`Error importing directory: ${String(s)}`),{successfulImports:0,duplicates:0,totalAttempted:0}}}async detectAutoImportOptions(){const t=gt();if(!t)return this._logger.warn("No workspace available for auto-import detection"),[];const e=await t.getWorkspaceRoot();if(!e)return this._logger.warn("No workspace root found for auto-import detection"),[];const s=[];for(const{directory:i,file:r,name:o}of Ze){let h=!1,c=!1;if(i)try{const d=(void 0)(e,i),u=await t.getPathInfo(d);h=u.exists===!0&&u.type===dt.Directory}catch(d){this._logger.debug(`Error checking directory ${i}: ${String(d)}`)}if(r)try{const d=(void 0)(e,r),u=await t.getPathInfo(d);c=u.exists===!0&&u.type===dt.File}catch(d){this._logger.debug(`Error checking file ${r}: ${String(d)}`)}h&&c?s.push({label:o,description:`Import existing rules from ${i} and ${r}`,directory:i,file:r}):h?s.push({label:o,description:`Import existing rules from ${i}`,directory:i}):c&&s.push({label:o,description:`Import existing rules from ${r}`,file:r})}return s}async processAutoImportSelection(t){const e=Ze.find(c=>c.name===t);if(!e)throw new Error(`Unknown auto-import option: ${t}`);const s=[];e.directory&&s.push(this.importDirectory(e.directory,!0)),e.file&&s.push(this.importFile(e.file,!0));const i=await Promise.all(s),r=i.reduce((c,d)=>c+d.successfulImports,0),o=i.reduce((c,d)=>c+d.duplicates,0),h=i.reduce((c,d)=>c+d.totalAttempted,0);return this._logger.debug(`Auto-import rules completed for ${t}, imported: ${r}, duplicates: ${o}, total attempted: ${h}`),{importedRulesCount:r,duplicatesCount:o,totalAttempted:h,source:t}}autoImportRules(){this._logger.debug("Auto import rules requested")}dispose(){super.dispose()}}const ye=new Map,oi=()=>{let a=Promise.resolve();const t=new Map,e=new Map,s=crypto.randomUUID(),i={end:r=>{const o=t.get(r);return console.debug("END LINK: ",r,s),o==null||o(),i},start:async(r,o)=>{const{promise:h,unlock:c,reject:d}=(m=>{let C=()=>{},_=()=>{},v=(b,w)=>()=>{w&&clearTimeout(w),b()};return{promise:new Promise((b,w)=>{let p,I=()=>{w("Chain was reset")};m&&m>0&&(p=setTimeout(I,m)),_=v(I,p),C=v(b,p)}),unlock:C,reject:_}})(o),u=a;return a=h.finally(()=>{t.delete(r),e.delete(r)}),t.set(r,()=>{c(),t.delete(r)}),e.set(r,()=>{d(),e.delete(r)}),await u,console.debug("START LINK: ",r,s),i},rejectAll:()=>{a=Promise.resolve();try{e.forEach(r=>{r(new Error("Chain was reset"))})}finally{t.clear(),e.clear()}},unlockAll:()=>{a=Promise.resolve();try{t.forEach(r=>{r()})}finally{t.clear(),e.clear()}}};return i},ve="temp-fe";class H{constructor(t,e,s,i,r,o){l(this,"_state");l(this,"_subscribers",new Set);l(this,"_focusModel",new Ws);l(this,"_onSendExchangeListeners",[]);l(this,"_onNewConversationListeners",[]);l(this,"_onHistoryDeleteListeners",[]);l(this,"_onBeforeChangeConversationListeners",[]);l(this,"_eventTracker");l(this,"_totalCharactersCacheThrottleMs",1e3);l(this,"_totalCharactersStore");l(this,"_chatHistorySummarizationModel");l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"setConversation",(t,e=!0,s=!0)=>{const i=t.id!==this._state.id;i&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,h])=>{if(h.requestId&&h.toolUseId){const{requestId:c,toolUseId:d}=je(o);return c===h.requestId&&d===h.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",fe(h)),[o,h]}return[o,{...h,...je(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&i&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const r=H.isEmpty(t);if(i&&r){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(L)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),i&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});l(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});l(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});l(this,"setName",t=>{this.update({name:t})});l(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});l(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});l(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[fe(t)]:t}})});l(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:E.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[fe({requestId:t,toolUseId:e})]||{phase:E.new});l(this,"getLastToolUseId",()=>{var s,i;const t=this.lastExchange;if(!t)return;const e=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(r=>r.type===F.TOOL_USE))??[]).at(-1);return e?(i=e.tool_use)==null?void 0:i.tool_use_id:void 0});l(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:E.unknown};const e=function(i=[]){let r;for(const o of i){if(o.type===F.TOOL_USE)return o;o.type===F.TOOL_USE_START&&(r=o)}return r}(t==null?void 0:t.structured_output_nodes);return e?this.getToolUseState(t.request_id,(s=e.tool_use)==null?void 0:s.tool_use_id):{phase:E.unknown}});l(this,"addExchange",(t,e)=>{const s=this._state.chatHistory;let i,r;i=e===void 0?[...s,t]:e===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,e),t,...s.slice(e)],L(t)&&(r=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:hs.unset,feedbackNote:""}}:void 0),this.update({chatHistory:i,...r?{feedbackStates:r}:{},lastUrl:void 0})});l(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});l(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});l(this,"updateExchangeById",(t,e,s=!1)=>{var h;const i=this.exchangeWithRequestId(e);if(i===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(i.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=function(c=[]){const d=Zs(c);return d&&d.type===F.TOOL_USE?c.filter(u=>u.type!==F.TOOL_USE_START):c}([...i.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==i.stop_reason&&i.stop_reason&&t.stop_reason===Ps.REASON_UNSPECIFIED&&(t.stop_reason=i.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...i.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const r=(h=(t.structured_output_nodes||[]).find(c=>c.type===F.MAIN_TEXT_FINISHED))==null?void 0:h.content;r&&r!==t.response_text&&(t.response_text=r);let o=this._state.isShareable||Lt({...i,...t});return this.update({chatHistory:this.chatHistory.map(c=>c.request_id===e?{...c,...t}:c),isShareable:o}),!0});l(this,"clearMessagesFromHistory",t=>{const e=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t),toolUseIds:e})});l(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});l(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),i=s.map(o=>o.request_id).filter(o=>o!==void 0),r=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:i,toolUseIds:r}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(h=>h(o))})});l(this,"clearMessageFromHistory",t=>{const e=this.chatHistory.find(i=>i.request_id===t),s=e?this._collectToolUseIdsFromMessages([e]):[];this.update({chatHistory:this.chatHistory.filter(i=>i.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t],toolUseIds:s})});l(this,"_collectToolUseIdsFromMessages",t=>{var s;const e=[];for(const i of t)if(L(i)&&i.structured_output_nodes)for(const r of i.structured_output_nodes)r.type===F.TOOL_USE&&((s=r.tool_use)!=null&&s.tool_use_id)&&e.push(r.tool_use.tool_use_id);return e});l(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});l(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});l(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});l(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:A.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));l(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});l(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);l(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});l(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:mt.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});l(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));l(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});l(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});l(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(i=>t.has(i.id)||i.recentFile||i.selection||i.sourceFolder),s=this._specialContextInputModel.recentItems.filter(i=>!(t.has(i.id)||i.recentFile||i.selection||i.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});l(this,"saveDraftExchange",(t,e)=>{var o,h,c;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),i=e!==((h=this.draftExchange)==null?void 0:h.rich_text_json_repr);if(!s&&!i)return;const r=(c=this.draftExchange)==null?void 0:c.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:r,status:A.draft}})});l(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});l(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!xt(this)){const i=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&i&&this.updateConversationTitle()}}).finally(()=>{var s;xt(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Se.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});l(this,"cancelMessage",async()=>{var e;if(!this.canCancelMessage||!((e=this.lastExchange)!=null&&e.request_id))return;const t=this.lastExchange.request_id;this.updateExchangeById({status:A.cancelled},t),await this._extensionClient.cancelChatStream(t)});l(this,"sendInstructionExchange",async(t,e)=>{let s=`${ve}-${crypto.randomUUID()}`;const i={status:A.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:mt.unseen,timestamp:new Date().toISOString()};this.addExchange(i);for await(const r of this._extensionClient.sendInstructionMessage(i,e)){if(!this.updateExchangeById(r,s,!0))return;s=r.request_id||s}});l(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});l(this,"checkAndGenerateAgentTitle",()=>{var e;if(!(!xt(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>we(s))).length===1&&!((e=this.extraData)!=null&&e.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});l(this,"sendSummaryExchange",()=>{const t={status:A.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:It.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});l(this,"generateCommitMessage",async()=>{let t=`${ve}-${crypto.randomUUID()}`;const e={status:A.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:mt.unseen,chatItemType:It.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});l(this,"sendExchange",async(t,e=!1,s)=>{var m,C,_;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let i=`${ve}-${crypto.randomUUID()}`,r=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(H.isNew(this._state)){const v=crypto.randomUUID(),b=this._state.id;try{await this._extensionClient.migrateConversationId(b,v)}catch(w){console.error("Failed to migrate conversation checkpoints:",w)}this._state={...this._state,id:v},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(v),this._subscribers.forEach(w=>w(this))}t=Je(t);let o={status:A.sent,request_id:i,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:r,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:mt.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(v=>v(o)),this._chatFlagModel.useHistorySummary&&!t.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},i,!1);const h=Date.now();let c=!1,d=0;for await(const v of this.sendUserMessage(i,o,e,s)){if(((m=this.exchangeWithRequestId(i))==null?void 0:m.status)!==A.sent||!this.updateExchangeById(v,i,!0))return;if(i=v.request_id||i,!c&&xt(this)){const b=Date.now();d=b-h,this._extensionClient.reportAgentRequestEvent({eventName:Se.firstTokenReceived,conversationId:this.id,requestId:i,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:h,firstTokenReceivedTimestampMs:b,timeToFirstTokenMs:d}}}),c=!0}}const u=Date.now()-h;(_=this._eventTracker)==null||_.trackEvent(bs.MESSAGE_SEND_TIMING,{requestId:i,timeToFirstTokenMs:d,timeToLastTokenMs:u,responseLength:(C=t==null?void 0:t.response_text)==null?void 0:C.length,chatHistoryLength:this.chatHistory.length,modelId:o.model_id}),this._chatHistorySummarizationModel.maybeScheduleSummarization(u)});l(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:A.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(xs.chatUseSuggestedQuestion)});l(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});l(this,"recoverExchange",async t=>{var i;if(!t.request_id||t.status!==A.sent)return;let e=t.request_id;const s=(i=t.structured_output_nodes)==null?void 0:i.filter(r=>r.type===F.AGENT_MEMORY);this.updateExchangeById({...t,response_text:t.lastChunkId?t.response_text:"",structured_output_nodes:t.lastChunkId?t.structured_output_nodes??[]:s},e);for await(const r of this.getChatStream(t)){if(!this.updateExchangeById(r,e,!0))return;e=r.request_id||e}});l(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{L(e)&&this._loadContextFromExchange(e)})});l(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});l(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{L(e)&&this._unloadContextFromExchange(e)})});l(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});l(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});l(this,"_jsonToStructuredRequest",t=>{const e=[],s=r=>{var h;const o=e.at(-1);if((o==null?void 0:o.type)===G.TEXT){const c=((h=o.text_node)==null?void 0:h.content)??"",d={...o,text_node:{content:c+r}};e[e.length-1]=d}else e.push({id:e.length,type:G.TEXT,text_node:{content:r}})},i=r=>{var o,h,c,d,u;if(r.type==="doc"||r.type==="paragraph")for(const m of r.content??[])i(m);else if(r.type==="hardBreak")s(`
`);else if(r.type==="text")s(r.text??"");else if(r.type==="file"){if(typeof((o=r.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(h=r.attrs)==null?void 0:h.src);if(r.attrs.isLoading)return;const m=(c=r.attrs)==null?void 0:c.title,C=Gs(m);ns(m)?e.push({id:e.length,type:G.IMAGE_ID,image_id_node:{image_id:r.attrs.src,format:C}}):e.push({id:e.length,type:G.FILE_ID,file_id_node:{file_id:r.attrs.src,file_name:m}})}else if(r.type==="mention"){const m=(d=r.attrs)==null?void 0:d.data;m&&os(m)?e.push({id:e.length,type:G.TEXT,text_node:{content:ws(this._chatFlagModel,m.personality.type)}}):m&&Is(m)?e.push({id:e.length,type:G.TEXT,text_node:{content:Ts.getTaskOrchestratorPrompt(m.task)}}):s(`@\`${(m==null?void 0:m.name)??(m==null?void 0:m.id)}\``)}else if(r.type==="askMode"){const m=(u=r.attrs)==null?void 0:u.prompt;m&&e.push({id:e.length,type:G.TEXT,text_node:{content:m}})}};return i(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=i,this._rulesModel=r,this._state={...H.create(o!=null&&o.forceAgentConversation?{extraData:{isAgentConversation:!0,hasAgentOnboarded:!0}}:void 0)},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new si(this,t,e)}get conversationId(){return this._state.id}insertChatItem(t,e){const s=[...this._state.chatHistory];s.splice(t,0,e),this.update({chatHistory:s})}_createTotalCharactersStore(){return js(()=>{let t=0;const e=this._state.chatHistory;return this.convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}setEventTracker(t){this._eventTracker=t}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,i)=>s+i,0))||0)<=4?Nt.PROTOTYPER:Nt.DEFAULT}catch(e){return console.error("Error determining persona type:",e),Nt.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Nt.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const e=t.chatHistory.find(L);return e&&e.request_message?H.toSentenceCase(e.request_message):xt(t)?"New Agent":"New Chat"}static isNew(t){return t.id===_t}static isEmpty(t){var i;const e=t.chatHistory.filter(r=>L(r)),s=t.chatHistory.filter(r=>be(r));return e.length===0&&s.length===0&&!((i=t.draftExchange)!=null&&i.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?H.lastMessageTimestamp(t):e==="lastInteractedAt"?H.lastInteractedAt(t):H.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const e=(s=t.chatHistory.findLast(L))==null?void 0:s.timestamp;return e?new Date(e):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!H.isEmpty(t)||H.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const i of this._onBeforeChangeConversationListeners){const r=i(t,s);r!==void 0&&(s=r)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return H.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Nt.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return H.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return H.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){if(this.flags.enableModelRegistry&&xt(this._state))return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=i=>Array.isArray(i)?i.some(e):!!i&&(i.type==="file"||!(!i.content||!Array.isArray(i.content))&&i.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(L)??null}get lastExchange(){return this.chatHistory.findLast(L)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>L(t)&&t.status===A.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Lt(t)||$t(t)||Ft(t))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(t){if(t.length===0)return[];t=this._chatHistorySummarizationModel.preprocessChatHistory(t);const e=[];for(const s of t)if(Lt(s))e.push(Ke(s));else if(Ft(s))e.push(Ke(s));else if($t(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const i=ni(s,1),r={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};e.push(r)}return e}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===A.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,i="";const r=await this._addIdeStateNode(Je({...t,request_id:e,status:A.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(e,r,!0))o.response_text&&(i+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:i,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t.request_id,t.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}_resolveUnresolvedToolUses(t,e,s){var u,m,C;if(t.length===0)return[t,e];const i=t[t.length-1],r=((u=i.response_nodes)==null?void 0:u.filter(_=>_.type===F.TOOL_USE))??[];if(r.length===0)return[t,e];const o=new Set;(m=e.structured_request_nodes)==null||m.forEach(_=>{var v;_.type===G.TOOL_RESULT&&((v=_.tool_result_node)!=null&&v.tool_use_id)&&o.add(_.tool_result_node.tool_use_id)});const h=r.filter(_=>{var b;const v=(b=_.tool_use)==null?void 0:b.tool_use_id;return v&&!o.has(v)});if(h.length===0)return[t,e];const c=h.map((_,v)=>{const b=_.tool_use.tool_use_id;return function(w,p,I,Y){const W=Bs(p,w,Y);let P;if(W!==void 0)P=W;else{let D;switch(p.phase){case E.runnable:D="Tool was cancelled before running.";break;case E.new:D="Cancelled by user.";break;case E.checkingSafety:D="Tool was cancelled during safety check.";break;case E.running:D="Tool was cancelled while running.";break;case E.cancelling:D="Tool cancellation was interrupted.";break;case E.cancelled:D="Cancelled by user.";break;case E.error:D="Tool execution failed.";break;case E.completed:D="Tool completed but result was unavailable.";break;case E.unknown:default:D="Cancelled by user.",p.phase!==E.unknown&&console.error(`Unexpected tool state phase: ${p.phase}`)}P={tool_use_id:w,content:D,is_error:!0}}return{id:I,type:G.TOOL_RESULT,tool_result_node:P}}(b,this.getToolUseState(i.request_id,b),Te(e.structured_request_nodes??[])+v+1,this._chatFlagModel.enableDebugFeatures)});if((C=e.structured_request_nodes)==null?void 0:C.some(_=>_.type===G.TOOL_RESULT))return[t,{...e,structured_request_nodes:[...e.structured_request_nodes??[],...c]}];{const _={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:c,structured_output_nodes:[],status:A.success,hidden:!0};return s||this.addExchangeBeforeLast(_),[t.concat(this.convertHistoryToExchanges([_])),e]}}async*sendUserMessage(t,e,s,i){const r=this._chatFlagModel.enableParallelTools,o=await(r?((h,c,d)=>{const u=ye.get(h)??oi();return ye.has(h)||ye.set(h,u),u.start(c,d)})("sendMessage",t):Promise.resolve({end:()=>{}}));try{for await(const h of this._sendUserMessage(t,e,s,i))yield h}finally{o.end(t)}}async*_sendUserMessage(t,e,s,i){var C;const r=this._specialContextInputModel.chatActiveContext;let o;if(e.chatHistory!==void 0)o=e.chatHistory;else{let _=this.successfulMessages;if(e.chatItemType===It.summaryTitle){const v=_.findIndex(b=>b.chatItemType!==It.agentOnboarding&&we(b));v!==-1&&(_=_.slice(v))}o=this.convertHistoryToExchanges(_)}this._chatFlagModel.enableParallelTools&&([o,e]=this._resolveUnresolvedToolUses(o,e,s));let h=this.personaType;if(e.structured_request_nodes){const _=e.structured_request_nodes.find(v=>v.type===G.CHANGE_PERSONALITY);_&&_.change_personality_node&&(h=_.change_personality_node.personality_type)}let c=[];if(this._chatFlagModel.enableRules&&this._rulesModel){this._rulesModel.requestRules();const _=ee(this._rulesModel.getCachedRules());c=He.filterRulesByContext(_,r.ruleFiles||[])}const d={text:e.request_message,chatHistory:o,silent:s,modelId:e.model_id,context:r,userSpecifiedFiles:r.userSpecifiedFiles,externalSourceIds:(C=r.externalSources)==null?void 0:C.map(_=>_.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:h,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:i,rules:c},u=this._createStreamStateHandlers(t,d,{flags:this._chatFlagModel}),m=this._extensionClient.startChatStreamWithRetry(t,d,{flags:this._chatFlagModel});for await(const _ of m){let v=_;t=_.request_id||t;for(const b of u)v=b.handleChunk(v)??v;yield v}for(const _ of u)yield*_.handleComplete();this.updateExchangeById({structured_request_nodes:e.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}async _addIdeStateNode(t){let e,s=(t.structured_request_nodes??[]).filter(i=>i.type!==G.IDE_STATE);try{e=await this._extensionClient.getChatRequestIdeState()}catch(i){console.error("Failed to add IDE state to exchange:",i)}return e?(s=[...s,{id:Te(s)+1,type:G.IDE_STATE,ide_state_node:e}],{...t,structured_request_nodes:s}):t}}function ni(a,t){const e=($t(a),a.fromTimestamp),s=($t(a),a.toTimestamp),i=$t(a)&&a.revertTarget!==void 0;return{id:t,type:G.CHECKPOINT_REF,checkpoint_ref_node:{request_id:a.request_id||"",from_timestamp:e,to_timestamp:s,source:i?$s.CHECKPOINT_REVERT:void 0}}}function Ke(a){const t=(a.structured_output_nodes??[]).filter(e=>e.type===F.RAW_RESPONSE||e.type===F.TOOL_USE||e.type===F.TOOL_USE_START).map(e=>e.type===F.TOOL_USE_START?{...e,tool_use:{...e.tool_use,input_json:"{}"},type:F.TOOL_USE}:e);return{request_message:a.request_message,response_text:a.response_text??"",request_id:a.request_id||"",request_nodes:a.structured_request_nodes??[],response_nodes:t}}function Te(a){return a.length>0?Math.max(...a.map(t=>t.id)):0}function Je(a){var t;if(a.request_message.length>0&&!((t=a.structured_request_nodes)!=null&&t.some(e=>e.type===G.TEXT))){let e=a.structured_request_nodes??[];return e=[...e,{id:Te(e)+1,type:G.TEXT,text_node:{content:a.request_message}}],{...a,structured_request_nodes:e}}return a}class ai{constructor(t=!0,e=setTimeout){l(this,"_notify",new Set);l(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});l(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});l(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});l(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,i){if(!t&&!i)return()=>{};const r={timeout:t,notify:e,once:s,date:i};return this._notify.add(r),this._schedule(r),()=>{this._clearTimeout(r),this._notify.delete(r)}}}class li{constructor(t=0,e=0,s=new ai,i=ht("busy"),r=ht(!1)){l(this,"unsubNotify");l(this,"unsubMessage");l(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});l(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=i,this.focusAfterIdle=r,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}var hi=jt("<svg><!></svg>"),ci=jt("<svg><!></svg>"),se=(a=>(a.send="send",a.addTask="addTask",a))(se||{});const di={id:"send",label:"Send to Agent",icon:function(a,t){const e=ls(t,["children","$$slots","$$events","$$legacy"]);var s=hi();is(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...e}));var i=rs(s);as(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M57.6 35.8C23.1 20.6-11.3 57.4 6.1 90.9l63 121.2c4.4 8.4 12.6 14.1 22 15.3L266 249.3c3.4.4 6 3.3 6 6.7s-2.6 6.3-6 6.7L91.1 284.6c-9.4 1.2-17.6 6.9-22 15.3l-63 121.2c-17.4 33.5 17 70.2 51.6 55.1l435.2-190.9c25.5-11.2 25.5-47.4 0-58.6z"/>',!0),Vt(a,s)},description:"Send message to agent"},ui={id:"addTask",label:"Add Task",icon:function(a,t){const e=ls(t,["children","$$slots","$$events","$$legacy"]);var s=ci();is(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...e}));var i=rs(s);as(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 512a256 256 0 1 0 0-512 256 256 0 1 0 0 512m-24-168v-64h-64c-13.3 0-24-10.7-24-24s10.7-24 24-24h64v-64c0-13.3 10.7-24 24-24s24 10.7 24 24v64h64c13.3 0 24 10.7 24 24s-10.7 24-24 24h-64v64c0 13.3-10.7 24-24 24s-24-10.7-24-24"/>',!0),Vt(a,s)},description:"Add task with the message content"},Gi=[di,ui];class mi{constructor(){l(this,"_mode",ht(se.send));l(this,"_currentMode",se.send);this._mode.subscribe(t=>{this._currentMode=t})}get mode(){return this._mode}setMode(t){this._mode.set(t)}getCurrentMode(){return this._currentMode}initializeFromState(t){t&&Object.values(se).includes(t)&&this._mode.set(t)}}class gi{constructor(t){l(this,"trackedExperiments",new Set);this.dependencies=t}_getChatMessagePayload(){const t=ee(this.dependencies.chatModeType),e=ee(this.dependencies.agentExecutionMode),s={chatMode:t,agentExecutionMode:t==="localAgent"?e:void 0};return t==="localAgent"&&(s.sendMode=ee(this.dependencies.currentSendMode)),s}trackEvent(t,e){const s={...this._getChatMessagePayload(),...e};this.trackSimpleEvent(t,s)}trackSimpleEvent(t,e){this.dependencies.extensionClient.trackEventWithTypes(t,e)}trackExperimentViewed(t,e,s){if(e===Ms.OFF)return;const i=`${t}:${e}`;this.trackedExperiments.has(i)||(this.trackedExperiments.add(i),this.dependencies.extensionClient.trackExperimentViewed(t,e,s))}}class _i{constructor(t=3e5){l(this,"_cleanItems",new Set);l(this,"_lastProcessedTime",new Map);this.cooldownMs=t}markClean(t){this._cleanItems.add(t),this._lastProcessedTime.set(t,Date.now())}markDirty(t){this._cleanItems.delete(t),this._lastProcessedTime.delete(t)}isClean(t){return this._cleanItems.has(t)}isWithinCooldown(t){const e=this._lastProcessedTime.get(t);return!!e&&Date.now()-e<this.cooldownMs}getLastProcessedTime(t){return this._lastProcessedTime.get(t)||0}cleanup(t){const e=Array.isArray(t)?t:Array.from(t);for(const s of e)this.markDirty(s)}clear(){this._cleanItems.clear(),this._lastProcessedTime.clear()}getStats(){return{cleanCount:this._cleanItems.size,trackedCount:this._lastProcessedTime.size}}}const $=[];for(let a=0;a<256;++a)$.push((a+256).toString(16).slice(1));let Ce;const fi=new Uint8Array(16),Xe={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function pi(a,t,e){var i;if(Xe.randomUUID&&!a)return Xe.randomUUID();const s=(a=a||{}).random??((i=a.rng)==null?void 0:i.call(a))??function(){if(!Ce){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");Ce=crypto.getRandomValues.bind(crypto)}return Ce(fi)}();if(s.length<16)throw new Error("Random bytes length must be >= 16");return s[6]=15&s[6]|64,s[8]=63&s[8]|128,function(r,o=0){return($[r[o+0]]+$[r[o+1]]+$[r[o+2]]+$[r[o+3]]+"-"+$[r[o+4]]+$[r[o+5]]+"-"+$[r[o+6]]+$[r[o+7]]+"-"+$[r[o+8]]+$[r[o+9]]+"-"+$[r[o+10]]+$[r[o+11]]+$[r[o+12]]+$[r[o+13]]+$[r[o+14]]+$[r[o+15]]).toLowerCase()}(s)}class yi{constructor(t,e,s=new _i(3e5)){l(this,"_maxItemsPerIteration",5);this._extensionClient=t,this._flags=e,this._cache=s}async hydrateConversation(t){let e=t;return e=await this._hydrateExchanges(e),e=await this._hydrateToolUseStates(e),this._cache.markDirty(t.id),e}async dehydrateConversation(t){let e=t;return this._flags.enableExchangeStorage&&(e=await this._dehydrateExchanges(e)),this._flags.enableToolUseStateStorage&&(e=await this._dehydrateToolUseStates(e)),e}async dehydrateConversationsIncremental(t,e){if(!this._flags.enableExchangeStorage&&!this._flags.enableToolUseStateStorage)return t;const s=Object.entries(t),i=this._selectConversationsForDehydration(s,e),r={};for(const[o,h]of s)if(i.includes(o))try{const c=await this.dehydrateConversation(h);this._cache.markClean(o),r[o]=o===e?h:c}catch(c){console.warn(`Failed to dehydrate conversation ${o}:`,c),r[o]=h}else r[o]=h;return r}async hydrateCurrentConversation(t,e){if(!e||!t[e])return t;try{const s=await this.hydrateConversation(t[e]);return{...t,[e]:s}}catch(s){return console.warn(`Failed to hydrate conversation ${e}:`,s),t}}markNeedsDehydration(t){this._cache.markDirty(t)}markDehydrated(t){this._cache.markClean(t)}cleanupDeletedConversations(t){this._cache.cleanup(t)}_selectConversationsForDehydration(t,e){var r;const s=[];if(e){const o=(r=t.find(([h])=>h===e))==null?void 0:r[1];o&&!this._cache.isClean(e)&&(o.chatHistory.some(L)?s.push(e):this._cache.markClean(e))}const i=t.filter(([o,h])=>o===e||this._cache.isClean(o)?!1:h.chatHistory.some(L)?!this._cache.isWithinCooldown(o):(this._cache.markClean(o),!1)).map(([o])=>o).sort((o,h)=>this._cache.getLastProcessedTime(o)-this._cache.getLastProcessedTime(h));return[...s,...i].slice(0,this._maxItemsPerIteration)}async _dehydrateExchanges(t){var i;const e=[],s=[];for(const r of t.chatHistory)if(L(r)){const o=r,h=o.request_id||((i=crypto==null?void 0:crypto.randomUUID)==null?void 0:i.call(crypto))||pi(),c={request_message:o.request_message,response_text:o.response_text||"",request_id:h,request_nodes:o.structured_request_nodes,response_nodes:o.structured_output_nodes,uuid:h,conversationId:t.id,status:o.status===A.success?"success":o.status===A.failed?"failed":"sent",timestamp:o.timestamp||new Date().toISOString(),seen_state:o.seen_state===mt.seen?"seen":"unseen"};e.push(c);const d={chatItemType:It.exchangePointer,exchangeUuid:h,timestamp:o.timestamp,request_message:o.request_message,status:o.status,hasResponse:!!o.response_text,isStreaming:o.status===A.sent,seen_state:o.seen_state};s.push(d)}else s.push(r);if(e.length>0)try{await this._extensionClient.saveExchanges(t.id,e)}catch{return t}return{...t,chatHistory:s}}async _dehydrateToolUseStates(t){if(!this._flags.enableToolUseStateStorage||!t.toolUseStates||Object.keys(t.toolUseStates).length===0)return t;try{return await this._extensionClient.saveToolUseStates(t.id,t.toolUseStates),{...t,toolUseStates:{}}}catch(e){return console.warn(`Failed to store tool use states for conversation ${t.id}:`,e),t}}async _hydrateExchanges(t){const e=t.chatHistory.filter(be);if(e.length===0)return t;try{const s=e.map(h=>h.exchangeUuid),i=await this._extensionClient.loadExchanges(t.id,s),r=new Map(i.map(h=>[h.uuid,h])),o=t.chatHistory.map(h=>{if(be(h)){const c=r.get(h.exchangeUuid);if(c)return{request_message:c.request_message,response_text:c.response_text,request_id:c.request_id,structured_request_nodes:c.request_nodes,structured_output_nodes:c.response_nodes,timestamp:c.timestamp,status:c.status==="success"?A.success:c.status==="failed"?A.failed:A.sent,seen_state:c.seen_state==="seen"?mt.seen:mt.unseen}}return h});return{...t,chatHistory:o}}catch(s){return console.warn(`Failed to restore exchanges for conversation ${t.id}:`,s),t}}async _hydrateToolUseStates(t){try{const e=await this._extensionClient.loadConversationToolUseStates(t.id);return{...t,toolUseStates:{...t.toolUseStates,...e}}}catch(e){return console.warn(`Failed to restore tool use states for conversation ${t.id}:`,e),t}}}const te=ht("idle");var vi=(a=>(a.manual="manual",a.auto="auto",a))(vi||{});class Ci{constructor(t,e,s,i={}){l(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});l(this,"extensionClient");l(this,"_chatFlagsModel");l(this,"_currConversationModel");l(this,"_chatModeModel");l(this,"_sendModeModel");l(this,"_flagsLoaded",ht(!1));l(this,"_eventTracker");l(this,"_rulesModel");l(this,"_persistenceController");l(this,"_messageBroker");l(this,"subscribers",new Set);l(this,"idleMessageModel",new li);l(this,"isPanelCollapsed");l(this,"agentExecutionMode");l(this,"sortConversationsBy");l(this,"displayedAnnouncements");l(this,"onLoaded",async()=>{var e,s;const t=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,enableAgentSwarmMode:t.enableAgentSwarmMode??!1,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,smallSyncThreshold:t.smallSyncThreshold??qs,bigSyncThreshold:t.bigSyncThreshold??ks,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Ls,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??ys.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableNewThreadsList:t.enableNewThreadsList??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},agentChatModel:t.agentChatModel??"",enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,enableAgentAutoMode:t.enableAgentAutoMode??!1,enableExchangeStorage:t.enableExchangeStorage??!1,enableToolUseStateStorage:t.enableToolUseStateStorage??!1,clientAnnouncement:t.clientAnnouncement??"",useHistorySummary:t.useHistorySummary??!1,historySummaryParams:t.historySummaryParams??"",conversationHistorySizeThresholdBytes:t.conversationHistorySizeThresholdBytes??0,retryChatStreamTimeouts:t.retryChatStreamTimeouts??!1,enableCommitIndexing:t.enableCommitIndexing??!1,enableMemoryRetrieval:t.enableMemoryRetrieval??!1,isVscodeVersionOutdated:t.isVscodeVersionOutdated??!1,vscodeMinVersion:t.vscodeMinVersion??"",enableAgentTabs:t.enableAgentTabs??!1,enableAgentGitTracker:t.enableAgentGitTracker??!1,remoteAgentsResumeHintAvailableTtlDays:t.remoteAgentsResumeHintAvailableTtlDays??0,enableParallelTools:t.enableParallelTools??!1,memoriesParams:t.memoriesParams??{},modelInfoRegistry:t.modelInfoRegistry??{}}),this._chatFlagsModel.enableAgentAutoMode||this.agentExecutionMode.set("manual"),this._flagsLoaded.set(!0),await this.initializeAsync(this.options.initialConversation),(s=(e=this.options).onLoaded)==null||s.call(e),this.notifySubscribers()});l(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));l(this,"initializeSync",t=>{if(this._state={...this._state,...this._host.getState()},t&&(this._state.conversations={...this._state.conversations,[t.id]:t}),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==_e&&this.currentConversationId!==_e||(delete this._state.conversations[_e],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===_t||H.isValid(s))),this.currentConversationId&&this.currentConversationId!==this.currentConversationModel.id){const e=this.conversations[this.currentConversationId];e&&this.currentConversationModel.setConversation(e)}this.initializeIsShareableState(),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});l(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const i=s.chatHistory.some(r=>Lt(r));t[e]={...s,isShareable:i}}this._state.conversations=t});l(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[i,r]of Object.entries(e))r.isPinned&&s.add(i);this.setState(this._state),this.notifySubscribers()});l(this,"saveImmediate",()=>{this.setState(this._state),this.setState.flush()});l(this,"setState");l(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});l(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));l(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:It.educateFeatures,request_id:crypto.randomUUID(),seen_state:mt.seen})});l(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});l(this,"setCurrentConversation",async(t,e=!0,s)=>{if(t===this.currentConversationId&&(s!=null&&s.noopIfSameConversation))return;let i;t===void 0&&(t=_t);const r=this._state.conversations[t];i=r?await this._persistenceController.hydrateConversation(r):H.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===_t&&(i.id=_t),s!=null&&s.newTaskUuid&&(i.rootTaskUuid=s.newTaskUuid);const o=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(i,!o,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});l(this,"saveConversation",async(t,e)=>{this._persistenceController.markNeedsDehydration(t.id),this.updateChatState({...this._state,currentConversationId:t.id,conversations:{...this._state.conversations,[t.id]:t}}),e&&delete this._state.conversations[_t]});l(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});l(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});l(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;te.set("copying");const s=e==null?void 0:e.chatHistory,i=s.reduce((h,c)=>(Lt(c)&&h.push({request_id:c.request_id||"",request_message:c.request_message,response_text:c.response_text||""}),h),[]);if(i.length===0)throw new Error("No chat history to share");const r=H.getDisplayName(e),o=await this.extensionClient.saveChat(t,i,r);if(o.data){let h=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:h}}}),h}throw new Error("Failed to create URL")});l(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void te.set("idle");navigator.clipboard.writeText(e),te.set("copied")}catch{te.set("failed")}});l(this,"deleteConversations",async(t,e=void 0,s=[],i)=>{const r=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${r>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const o=new Set(t);await this.deleteConversationIds(o)}if(s.length>0&&i)for(const o of s)try{await i.deleteAgent(o,!0)}catch(h){console.error(`Failed to delete remote agent ${o}:`,h)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});l(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});l(this,"deleteConversationIds",async t=>{var i,r,o;const e=[],s=[];for(const h of t){const c=((i=this._state.conversations[h])==null?void 0:i.requestIds)??[];e.push(...c);const d=((r=this._state.conversations[h])==null?void 0:r.toolUseStates)??{};for(const m of Object.keys(d)){const{toolUseId:C}=d[m];C&&s.push(C)}const u=this._state.conversations[h];if(u){for(const m of u.chatHistory)if(L(m)&&m.structured_output_nodes)for(const C of m.structured_output_nodes)C.type===F.TOOL_USE&&((o=C.tool_use)!=null&&o.tool_use_id)&&s.push(C.tool_use.tool_use_id)}}for(const h of Object.values(this._state.conversations))if(t.has(h.id)){for(const d of h.chatHistory)L(d)&&this.deleteImagesInExchange(d);const c=h.draftExchange;c&&this.deleteImagesInExchange(c)}for(const h of t){try{await this.extensionClient.deleteConversationExchanges(h)}catch(c){console.error(`Failed to delete exchanges for conversation ${h}:`,c)}if(this.flags.enableToolUseStateStorage)try{await this.extensionClient.deleteConversationToolUseStates(h)}catch(c){console.error(`Failed to delete tool use states for conversation ${h}:`,c)}}this._persistenceController.cleanupDeletedConversations(t),this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([h])=>!t.has(h)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t),toolUseIds:s})});l(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});l(this,"findImagesInJson",t=>{const e=[],s=i=>{var r,o;if(i.type==="file"&&((r=i.attrs)!=null&&r.src)){const h=(o=i.attrs)==null?void 0:o.src;ns(h)&&e.push(i.attrs.src)}else if((i.type==="doc"||i.type==="paragraph")&&i.content)for(const h of i.content)s(h)};return s(t),e});l(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===G.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));l(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});l(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});l(this,"smartPaste",(t,e,s,i)=>{const r=this._currConversationModel.historyTo(t,!0).filter(o=>Lt(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:r,targetFile:s??void 0,options:i})});l(this,"saveImage",async t=>await this.extensionClient.saveImage(t));l(this,"saveAttachment",async t=>await this.extensionClient.saveAttachment(t));l(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));l(this,"renderImage",async t=>await this.extensionClient.loadImage(t));var c,d;this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=i,this._chatFlagsModel=new Es(i.initialFlags),this._messageBroker=new Ns(this._host),this._rulesModel=new As(this._messageBroker,!1),this.extensionClient=new Fs(this._host,this._asyncMsgSender,this._chatFlagsModel),this._messageBroker.registerConsumer(this._rulesModel),this._currConversationModel=new H(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation,this._rulesModel,{forceAgentConversation:i.forceAgentConversation}),this._sendModeModel=new mi,this._persistenceController=new yi(this.extensionClient,this._chatFlagsModel);const r=((c=i.debounceConfig)==null?void 0:c.wait)??5e3,o=((d=i.debounceConfig)==null?void 0:d.maxWait)??3e4;this.setState=vs(u=>{this._setStateWithPersistence(u)},r,{maxWait:o}),this.initializeSync(i.initialConversation);const h=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=ht(h),this.agentExecutionMode=ht(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=ht(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=ht(this._state.displayedAnnouncements??[]),this._sendModeModel.initializeFromState(this._state.sendMode),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t;const e={extensionClient:this.extensionClient,chatModeType:t.chatModeType,currentSendMode:this._sendModeModel.mode,agentExecutionMode:this.agentExecutionMode};this._eventTracker=new gi(e),this._currConversationModel.setEventTracker(this._eventTracker)}get flagsLoaded(){return this._flagsLoaded}get eventTracker(){return this._eventTracker}get rulesModel(){return this._rulesModel}async initializeAsync(t){const e=(t==null?void 0:t.id)||this.currentConversationId;this._state.conversations=await this._persistenceController.hydrateCurrentConversation(this._state.conversations,e),t?await this.setCurrentConversation(t.id):await this.setCurrentConversation(this.currentConversationId)}async _setStateWithPersistence(t){const e=await this._persistenceController.dehydrateConversationsIncremental(t.conversations??this._state.conversations,this.currentConversationId);this._host.setState({...t,conversations:e})}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}get sendModeModel(){return this._sendModeModel}get chatModeModel(){return this._chatModeModel}orderedConversations(t,e="desc",s){const i=t||this._state.sortConversationsBy||"lastMessageTimestamp";let r=Object.values(this._state.conversations);return s&&(r=r.filter(s)),r.sort((o,h)=>{const c=H.getTime(o,i).getTime(),d=H.getTime(h,i).getTime();return e==="asc"?c-d:d-c})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===_t)return!1;const i=!H.isValid(this.conversations[s]),r=xt(this.conversations[s]);return i&&(t==="agent"&&r||t==="chat"&&!r||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===bt.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.handleSetToThreadType("localAgent","manual"):s.toLowerCase()==="chat"?await this._chatModeModel.handleSetToThreadType("chat"):console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}l(Ci,"NEW_AGENT_KEY",_t);const At=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Qe=new Set,Me=typeof process=="object"&&process?process:{},ds=(a,t,e,s)=>{typeof Me.emitWarning=="function"?Me.emitWarning(a,t,e,s):console.error(`[${e}] ${t}: ${a}`)};let ne=globalThis.AbortController,Ye=globalThis.AbortSignal;var ts;if(ne===void 0){Ye=class{constructor(){l(this,"onabort");l(this,"_onabort",[]);l(this,"reason");l(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},ne=class{constructor(){l(this,"signal",new Ye);t()}abort(e){var s,i;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const r of this.signal._onabort)r(e);(i=(s=this.signal).onabort)==null||i.call(s,e)}}};let a=((ts=Me.env)==null?void 0:ts.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{a&&(a=!1,ds("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const ft=a=>a&&a===Math.floor(a)&&a>0&&isFinite(a),us=a=>ft(a)?a<=Math.pow(2,8)?Uint8Array:a<=Math.pow(2,16)?Uint16Array:a<=Math.pow(2,32)?Uint32Array:a<=Number.MAX_SAFE_INTEGER?ie:null:null;class ie extends Array{constructor(t){super(t),this.fill(0)}}var kt;const wt=class wt{constructor(t,e){l(this,"heap");l(this,"length");if(!n(wt,kt))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=us(t);if(!e)return[];y(wt,kt,!0);const s=new wt(t,e);return y(wt,kt,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};kt=new WeakMap,x(wt,kt,!1);let Ee=wt;var es,ss,et,K,st,it,qt,Ht,U,rt,q,M,S,V,J,j,O,ot,N,nt,at,X,lt,Ct,B,g,Fe,Tt,ut,Gt,Q,ms,Mt,Dt,Wt,pt,yt,Le,re,oe,T,ke,zt,vt,qe;const De=class De{constructor(t){x(this,g);x(this,et);x(this,K);x(this,st);x(this,it);x(this,qt);x(this,Ht);l(this,"ttl");l(this,"ttlResolution");l(this,"ttlAutopurge");l(this,"updateAgeOnGet");l(this,"updateAgeOnHas");l(this,"allowStale");l(this,"noDisposeOnSet");l(this,"noUpdateTTL");l(this,"maxEntrySize");l(this,"sizeCalculation");l(this,"noDeleteOnFetchRejection");l(this,"noDeleteOnStaleGet");l(this,"allowStaleOnFetchAbort");l(this,"allowStaleOnFetchRejection");l(this,"ignoreFetchAbort");x(this,U);x(this,rt);x(this,q);x(this,M);x(this,S);x(this,V);x(this,J);x(this,j);x(this,O);x(this,ot);x(this,N);x(this,nt);x(this,at);x(this,X);x(this,lt);x(this,Ct);x(this,B);x(this,Tt,()=>{});x(this,ut,()=>{});x(this,Gt,()=>{});x(this,Q,()=>!1);x(this,Mt,t=>{});x(this,Dt,(t,e,s)=>{});x(this,Wt,(t,e,s,i)=>{if(s||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});l(this,es,"LRUCache");const{max:e=0,ttl:s,ttlResolution:i=1,ttlAutopurge:r,updateAgeOnGet:o,updateAgeOnHas:h,allowStale:c,dispose:d,disposeAfter:u,noDisposeOnSet:m,noUpdateTTL:C,maxSize:_=0,maxEntrySize:v=0,sizeCalculation:b,fetchMethod:w,memoMethod:p,noDeleteOnFetchRejection:I,noDeleteOnStaleGet:Y,allowStaleOnFetchRejection:W,allowStaleOnFetchAbort:P,ignoreFetchAbort:D}=t;if(e!==0&&!ft(e))throw new TypeError("max option must be a nonnegative integer");const tt=e?us(e):Array;if(!tt)throw new Error("invalid max value: "+e);if(y(this,et,e),y(this,K,_),this.maxEntrySize=v||n(this,K),this.sizeCalculation=b,this.sizeCalculation){if(!n(this,K)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(p!==void 0&&typeof p!="function")throw new TypeError("memoMethod must be a function if defined");if(y(this,Ht,p),w!==void 0&&typeof w!="function")throw new TypeError("fetchMethod must be a function if specified");if(y(this,qt,w),y(this,Ct,!!w),y(this,q,new Map),y(this,M,new Array(e).fill(void 0)),y(this,S,new Array(e).fill(void 0)),y(this,V,new tt(e)),y(this,J,new tt(e)),y(this,j,0),y(this,O,0),y(this,ot,Ee.create(e)),y(this,U,0),y(this,rt,0),typeof d=="function"&&y(this,st,d),typeof u=="function"?(y(this,it,u),y(this,N,[])):(y(this,it,void 0),y(this,N,void 0)),y(this,lt,!!n(this,st)),y(this,B,!!n(this,it)),this.noDisposeOnSet=!!m,this.noUpdateTTL=!!C,this.noDeleteOnFetchRejection=!!I,this.allowStaleOnFetchRejection=!!W,this.allowStaleOnFetchAbort=!!P,this.ignoreFetchAbort=!!D,this.maxEntrySize!==0){if(n(this,K)!==0&&!ft(n(this,K)))throw new TypeError("maxSize must be a positive integer if specified");if(!ft(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");f(this,g,ms).call(this)}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!Y,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!h,this.ttlResolution=ft(i)||i===0?i:1,this.ttlAutopurge=!!r,this.ttl=s||0,this.ttl){if(!ft(this.ttl))throw new TypeError("ttl must be a positive integer if specified");f(this,g,Fe).call(this)}if(n(this,et)===0&&this.ttl===0&&n(this,K)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!n(this,et)&&!n(this,K)){const ct="LRU_CACHE_UNBOUNDED";(St=>!Qe.has(St))(ct)&&(Qe.add(ct),ds("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",ct,De))}}static unsafeExposeInternals(t){return{starts:n(t,at),ttls:n(t,X),sizes:n(t,nt),keyMap:n(t,q),keyList:n(t,M),valList:n(t,S),next:n(t,V),prev:n(t,J),get head(){return n(t,j)},get tail(){return n(t,O)},free:n(t,ot),isBackgroundFetch:e=>{var s;return f(s=t,g,T).call(s,e)},backgroundFetch:(e,s,i,r)=>{var o;return f(o=t,g,oe).call(o,e,s,i,r)},moveToTail:e=>{var s;return f(s=t,g,zt).call(s,e)},indexes:e=>{var s;return f(s=t,g,pt).call(s,e)},rindexes:e=>{var s;return f(s=t,g,yt).call(s,e)},isStale:e=>{var s;return n(s=t,Q).call(s,e)}}}get max(){return n(this,et)}get maxSize(){return n(this,K)}get calculatedSize(){return n(this,rt)}get size(){return n(this,U)}get fetchMethod(){return n(this,qt)}get memoMethod(){return n(this,Ht)}get dispose(){return n(this,st)}get disposeAfter(){return n(this,it)}getRemainingTTL(t){return n(this,q).has(t)?1/0:0}*entries(){for(const t of f(this,g,pt).call(this))n(this,S)[t]===void 0||n(this,M)[t]===void 0||f(this,g,T).call(this,n(this,S)[t])||(yield[n(this,M)[t],n(this,S)[t]])}*rentries(){for(const t of f(this,g,yt).call(this))n(this,S)[t]===void 0||n(this,M)[t]===void 0||f(this,g,T).call(this,n(this,S)[t])||(yield[n(this,M)[t],n(this,S)[t]])}*keys(){for(const t of f(this,g,pt).call(this)){const e=n(this,M)[t];e===void 0||f(this,g,T).call(this,n(this,S)[t])||(yield e)}}*rkeys(){for(const t of f(this,g,yt).call(this)){const e=n(this,M)[t];e===void 0||f(this,g,T).call(this,n(this,S)[t])||(yield e)}}*values(){for(const t of f(this,g,pt).call(this))n(this,S)[t]===void 0||f(this,g,T).call(this,n(this,S)[t])||(yield n(this,S)[t])}*rvalues(){for(const t of f(this,g,yt).call(this))n(this,S)[t]===void 0||f(this,g,T).call(this,n(this,S)[t])||(yield n(this,S)[t])}[(ss=Symbol.iterator,es=Symbol.toStringTag,ss)](){return this.entries()}find(t,e={}){for(const s of f(this,g,pt).call(this)){const i=n(this,S)[s],r=f(this,g,T).call(this,i)?i.__staleWhileFetching:i;if(r!==void 0&&t(r,n(this,M)[s],this))return this.get(n(this,M)[s],e)}}forEach(t,e=this){for(const s of f(this,g,pt).call(this)){const i=n(this,S)[s],r=f(this,g,T).call(this,i)?i.__staleWhileFetching:i;r!==void 0&&t.call(e,r,n(this,M)[s],this)}}rforEach(t,e=this){for(const s of f(this,g,yt).call(this)){const i=n(this,S)[s],r=f(this,g,T).call(this,i)?i.__staleWhileFetching:i;r!==void 0&&t.call(e,r,n(this,M)[s],this)}}purgeStale(){let t=!1;for(const e of f(this,g,yt).call(this,{allowStale:!0}))n(this,Q).call(this,e)&&(f(this,g,vt).call(this,n(this,M)[e],"expire"),t=!0);return t}info(t){const e=n(this,q).get(t);if(e===void 0)return;const s=n(this,S)[e],i=f(this,g,T).call(this,s)?s.__staleWhileFetching:s;if(i===void 0)return;const r={value:i};if(n(this,X)&&n(this,at)){const o=n(this,X)[e],h=n(this,at)[e];if(o&&h){const c=o-(At.now()-h);r.ttl=c,r.start=Date.now()}}return n(this,nt)&&(r.size=n(this,nt)[e]),r}dump(){const t=[];for(const e of f(this,g,pt).call(this,{allowStale:!0})){const s=n(this,M)[e],i=n(this,S)[e],r=f(this,g,T).call(this,i)?i.__staleWhileFetching:i;if(r===void 0||s===void 0)continue;const o={value:r};if(n(this,X)&&n(this,at)){o.ttl=n(this,X)[e];const h=At.now()-n(this,at)[e];o.start=Math.floor(Date.now()-h)}n(this,nt)&&(o.size=n(this,nt)[e]),t.unshift([s,o])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const i=Date.now()-s.start;s.start=At.now()-i}this.set(e,s.value,s)}}set(t,e,s={}){var C,_,v,b,w;if(e===void 0)return this.delete(t),this;const{ttl:i=this.ttl,start:r,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:h=this.sizeCalculation,status:c}=s;let{noUpdateTTL:d=this.noUpdateTTL}=s;const u=n(this,Wt).call(this,t,e,s.size||0,h);if(this.maxEntrySize&&u>this.maxEntrySize)return c&&(c.set="miss",c.maxEntrySizeExceeded=!0),f(this,g,vt).call(this,t,"set"),this;let m=n(this,U)===0?void 0:n(this,q).get(t);if(m===void 0)m=n(this,U)===0?n(this,O):n(this,ot).length!==0?n(this,ot).pop():n(this,U)===n(this,et)?f(this,g,re).call(this,!1):n(this,U),n(this,M)[m]=t,n(this,S)[m]=e,n(this,q).set(t,m),n(this,V)[n(this,O)]=m,n(this,J)[m]=n(this,O),y(this,O,m),Kt(this,U)._++,n(this,Dt).call(this,m,u,c),c&&(c.set="add"),d=!1;else{f(this,g,zt).call(this,m);const p=n(this,S)[m];if(e!==p){if(n(this,Ct)&&f(this,g,T).call(this,p)){p.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:I}=p;I===void 0||o||(n(this,lt)&&((C=n(this,st))==null||C.call(this,I,t,"set")),n(this,B)&&((_=n(this,N))==null||_.push([I,t,"set"])))}else o||(n(this,lt)&&((v=n(this,st))==null||v.call(this,p,t,"set")),n(this,B)&&((b=n(this,N))==null||b.push([p,t,"set"])));if(n(this,Mt).call(this,m),n(this,Dt).call(this,m,u,c),n(this,S)[m]=e,c){c.set="replace";const I=p&&f(this,g,T).call(this,p)?p.__staleWhileFetching:p;I!==void 0&&(c.oldValue=I)}}else c&&(c.set="update")}if(i===0||n(this,X)||f(this,g,Fe).call(this),n(this,X)&&(d||n(this,Gt).call(this,m,i,r),c&&n(this,ut).call(this,c,m)),!o&&n(this,B)&&n(this,N)){const p=n(this,N);let I;for(;I=p==null?void 0:p.shift();)(w=n(this,it))==null||w.call(this,...I)}return this}pop(){var t;try{for(;n(this,U);){const e=n(this,S)[n(this,j)];if(f(this,g,re).call(this,!0),f(this,g,T).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(n(this,B)&&n(this,N)){const e=n(this,N);let s;for(;s=e==null?void 0:e.shift();)(t=n(this,it))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:i}=e,r=n(this,q).get(t);if(r!==void 0){const o=n(this,S)[r];if(f(this,g,T).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!n(this,Q).call(this,r))return s&&n(this,Tt).call(this,r),i&&(i.has="hit",n(this,ut).call(this,i,r)),!0;i&&(i.has="stale",n(this,ut).call(this,i,r))}else i&&(i.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,i=n(this,q).get(t);if(i===void 0||!s&&n(this,Q).call(this,i))return;const r=n(this,S)[i];return f(this,g,T).call(this,r)?r.__staleWhileFetching:r}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:h=this.noDisposeOnSet,size:c=0,sizeCalculation:d=this.sizeCalculation,noUpdateTTL:u=this.noUpdateTTL,noDeleteOnFetchRejection:m=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:C=this.allowStaleOnFetchRejection,ignoreFetchAbort:_=this.ignoreFetchAbort,allowStaleOnFetchAbort:v=this.allowStaleOnFetchAbort,context:b,forceRefresh:w=!1,status:p,signal:I}=e;if(!n(this,Ct))return p&&(p.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,status:p});const Y={allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,ttl:o,noDisposeOnSet:h,size:c,sizeCalculation:d,noUpdateTTL:u,noDeleteOnFetchRejection:m,allowStaleOnFetchRejection:C,allowStaleOnFetchAbort:v,ignoreFetchAbort:_,status:p,signal:I};let W=n(this,q).get(t);if(W===void 0){p&&(p.fetch="miss");const P=f(this,g,oe).call(this,t,W,Y,b);return P.__returned=P}{const P=n(this,S)[W];if(f(this,g,T).call(this,P)){const St=s&&P.__staleWhileFetching!==void 0;return p&&(p.fetch="inflight",St&&(p.returnedStale=!0)),St?P.__staleWhileFetching:P.__returned=P}const D=n(this,Q).call(this,W);if(!w&&!D)return p&&(p.fetch="hit"),f(this,g,zt).call(this,W),i&&n(this,Tt).call(this,W),p&&n(this,ut).call(this,p,W),P;const tt=f(this,g,oe).call(this,t,W,Y,b),ct=tt.__staleWhileFetching!==void 0&&s;return p&&(p.fetch=D?"stale":"refresh",ct&&D&&(p.returnedStale=!0)),ct?tt.__staleWhileFetching:tt.__returned=tt}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=n(this,Ht);if(!s)throw new Error("no memoMethod provided to constructor");const{context:i,forceRefresh:r,...o}=e,h=this.get(t,o);if(!r&&h!==void 0)return h;const c=s(t,h,{options:o,context:i});return this.set(t,c,o),c}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,status:o}=e,h=n(this,q).get(t);if(h!==void 0){const c=n(this,S)[h],d=f(this,g,T).call(this,c);return o&&n(this,ut).call(this,o,h),n(this,Q).call(this,h)?(o&&(o.get="stale"),d?(o&&s&&c.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?c.__staleWhileFetching:void 0):(r||f(this,g,vt).call(this,t,"expire"),o&&s&&(o.returnedStale=!0),s?c:void 0)):(o&&(o.get="hit"),d?c.__staleWhileFetching:(f(this,g,zt).call(this,h),i&&n(this,Tt).call(this,h),c))}o&&(o.get="miss")}delete(t){return f(this,g,vt).call(this,t,"delete")}clear(){return f(this,g,qe).call(this,"delete")}};et=new WeakMap,K=new WeakMap,st=new WeakMap,it=new WeakMap,qt=new WeakMap,Ht=new WeakMap,U=new WeakMap,rt=new WeakMap,q=new WeakMap,M=new WeakMap,S=new WeakMap,V=new WeakMap,J=new WeakMap,j=new WeakMap,O=new WeakMap,ot=new WeakMap,N=new WeakMap,nt=new WeakMap,at=new WeakMap,X=new WeakMap,lt=new WeakMap,Ct=new WeakMap,B=new WeakMap,g=new WeakSet,Fe=function(){const t=new ie(n(this,et)),e=new ie(n(this,et));y(this,X,t),y(this,at,e),y(this,Gt,(r,o,h=At.now())=>{if(e[r]=o!==0?h:0,t[r]=o,o!==0&&this.ttlAutopurge){const c=setTimeout(()=>{n(this,Q).call(this,r)&&f(this,g,vt).call(this,n(this,M)[r],"expire")},o+1);c.unref&&c.unref()}}),y(this,Tt,r=>{e[r]=t[r]!==0?At.now():0}),y(this,ut,(r,o)=>{if(t[o]){const h=t[o],c=e[o];if(!h||!c)return;r.ttl=h,r.start=c,r.now=s||i();const d=r.now-c;r.remainingTTL=h-d}});let s=0;const i=()=>{const r=At.now();if(this.ttlResolution>0){s=r;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return r};this.getRemainingTTL=r=>{const o=n(this,q).get(r);if(o===void 0)return 0;const h=t[o],c=e[o];return!h||!c?1/0:h-((s||i())-c)},y(this,Q,r=>{const o=e[r],h=t[r];return!!h&&!!o&&(s||i())-o>h})},Tt=new WeakMap,ut=new WeakMap,Gt=new WeakMap,Q=new WeakMap,ms=function(){const t=new ie(n(this,et));y(this,rt,0),y(this,nt,t),y(this,Mt,e=>{y(this,rt,n(this,rt)-t[e]),t[e]=0}),y(this,Wt,(e,s,i,r)=>{if(f(this,g,T).call(this,s))return 0;if(!ft(i)){if(!r)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof r!="function")throw new TypeError("sizeCalculation must be a function");if(i=r(s,e),!ft(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return i}),y(this,Dt,(e,s,i)=>{if(t[e]=s,n(this,K)){const r=n(this,K)-t[e];for(;n(this,rt)>r;)f(this,g,re).call(this,!0)}y(this,rt,n(this,rt)+t[e]),i&&(i.entrySize=s,i.totalCalculatedSize=n(this,rt))})},Mt=new WeakMap,Dt=new WeakMap,Wt=new WeakMap,pt=function*({allowStale:t=this.allowStale}={}){if(n(this,U))for(let e=n(this,O);f(this,g,Le).call(this,e)&&(!t&&n(this,Q).call(this,e)||(yield e),e!==n(this,j));)e=n(this,J)[e]},yt=function*({allowStale:t=this.allowStale}={}){if(n(this,U))for(let e=n(this,j);f(this,g,Le).call(this,e)&&(!t&&n(this,Q).call(this,e)||(yield e),e!==n(this,O));)e=n(this,V)[e]},Le=function(t){return t!==void 0&&n(this,q).get(n(this,M)[t])===t},re=function(t){var r,o;const e=n(this,j),s=n(this,M)[e],i=n(this,S)[e];return n(this,Ct)&&f(this,g,T).call(this,i)?i.__abortController.abort(new Error("evicted")):(n(this,lt)||n(this,B))&&(n(this,lt)&&((r=n(this,st))==null||r.call(this,i,s,"evict")),n(this,B)&&((o=n(this,N))==null||o.push([i,s,"evict"]))),n(this,Mt).call(this,e),t&&(n(this,M)[e]=void 0,n(this,S)[e]=void 0,n(this,ot).push(e)),n(this,U)===1?(y(this,j,y(this,O,0)),n(this,ot).length=0):y(this,j,n(this,V)[e]),n(this,q).delete(s),Kt(this,U)._--,e},oe=function(t,e,s,i){const r=e===void 0?void 0:n(this,S)[e];if(f(this,g,T).call(this,r))return r;const o=new ne,{signal:h}=s;h==null||h.addEventListener("abort",()=>o.abort(h.reason),{signal:o.signal});const c={signal:o.signal,options:s,context:i},d=(_,v=!1)=>{const{aborted:b}=o.signal,w=s.ignoreFetchAbort&&_!==void 0;if(s.status&&(b&&!v?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,w&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),b&&!w&&!v)return u(o.signal.reason);const p=m;return n(this,S)[e]===m&&(_===void 0?p.__staleWhileFetching?n(this,S)[e]=p.__staleWhileFetching:f(this,g,vt).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,_,c.options))),_},u=_=>{const{aborted:v}=o.signal,b=v&&s.allowStaleOnFetchAbort,w=b||s.allowStaleOnFetchRejection,p=w||s.noDeleteOnFetchRejection,I=m;if(n(this,S)[e]===m&&(!p||I.__staleWhileFetching===void 0?f(this,g,vt).call(this,t,"fetch"):b||(n(this,S)[e]=I.__staleWhileFetching)),w)return s.status&&I.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),I.__staleWhileFetching;if(I.__returned===I)throw _};s.status&&(s.status.fetchDispatched=!0);const m=new Promise((_,v)=>{var w;const b=(w=n(this,qt))==null?void 0:w.call(this,t,r,c);b&&b instanceof Promise&&b.then(p=>_(p===void 0?void 0:p),v),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(_(void 0),s.allowStaleOnFetchAbort&&(_=p=>d(p,!0)))})}).then(d,_=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=_),u(_))),C=Object.assign(m,{__abortController:o,__staleWhileFetching:r,__returned:void 0});return e===void 0?(this.set(t,C,{...c.options,status:void 0}),e=n(this,q).get(t)):n(this,S)[e]=C,C},T=function(t){if(!n(this,Ct))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof ne},ke=function(t,e){n(this,J)[e]=t,n(this,V)[t]=e},zt=function(t){t!==n(this,O)&&(t===n(this,j)?y(this,j,n(this,V)[t]):f(this,g,ke).call(this,n(this,J)[t],n(this,V)[t]),f(this,g,ke).call(this,n(this,O),t),y(this,O,t))},vt=function(t,e){var i,r,o,h;let s=!1;if(n(this,U)!==0){const c=n(this,q).get(t);if(c!==void 0)if(s=!0,n(this,U)===1)f(this,g,qe).call(this,e);else{n(this,Mt).call(this,c);const d=n(this,S)[c];if(f(this,g,T).call(this,d)?d.__abortController.abort(new Error("deleted")):(n(this,lt)||n(this,B))&&(n(this,lt)&&((i=n(this,st))==null||i.call(this,d,t,e)),n(this,B)&&((r=n(this,N))==null||r.push([d,t,e]))),n(this,q).delete(t),n(this,M)[c]=void 0,n(this,S)[c]=void 0,c===n(this,O))y(this,O,n(this,J)[c]);else if(c===n(this,j))y(this,j,n(this,V)[c]);else{const u=n(this,J)[c];n(this,V)[u]=n(this,V)[c];const m=n(this,V)[c];n(this,J)[m]=n(this,J)[c]}Kt(this,U)._--,n(this,ot).push(c)}}if(n(this,B)&&((o=n(this,N))!=null&&o.length)){const c=n(this,N);let d;for(;d=c==null?void 0:c.shift();)(h=n(this,it))==null||h.call(this,...d)}return s},qe=function(t){var e,s,i;for(const r of f(this,g,yt).call(this,{allowStale:!0})){const o=n(this,S)[r];if(f(this,g,T).call(this,o))o.__abortController.abort(new Error("deleted"));else{const h=n(this,M)[r];n(this,lt)&&((e=n(this,st))==null||e.call(this,o,h,t)),n(this,B)&&((s=n(this,N))==null||s.push([o,h,t]))}}if(n(this,q).clear(),n(this,S).fill(void 0),n(this,M).fill(void 0),n(this,X)&&n(this,at)&&(n(this,X).fill(0),n(this,at).fill(0)),n(this,nt)&&n(this,nt).fill(0),y(this,j,0),y(this,O,0),n(this,ot).length=0,y(this,rt,0),y(this,U,0),n(this,B)&&n(this,N)){const r=n(this,N);let o;for(;o=r==null?void 0:r.shift();)(i=n(this,it))==null||i.call(this,...o)}};let Ae=De;class Wi{constructor(){l(this,"_syncStatus",{status:Hs.done,foldersProgress:[]});l(this,"_syncEnabledState",Ne.initializing);l(this,"_workspaceGuidelines",[]);l(this,"_openUserGuidelinesInput",!1);l(this,"_userGuidelines");l(this,"_contextStore",new Si);l(this,"_prevOpenFiles",[]);l(this,"_disableContext",!1);l(this,"_enableAgentMemories",!1);l(this,"subscribers",new Set);l(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));l(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case bt.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case bt.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case bt.fileRangesSelected:this.updateSelections(e.data);break;case bt.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case bt.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case bt.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});l(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:R.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});l(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});l(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});l(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});l(this,"addFile",t=>{this.addFiles([t])});l(this,"addFiles",t=>{this.updateFiles(t,[])});l(this,"removeFile",t=>{this.removeFiles([t])});l(this,"removeFiles",t=>{this.updateFiles([],t)});l(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});l(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});l(this,"updateFiles",(t,e)=>{const s=o=>({file:o,...me(o)}),i=t.map(s),r=e.map(s);this._contextStore.update(i,r,o=>o.id),this.notifySubscribers()});l(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});l(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});l(this,"setCurrentlyOpenFiles",t=>{const e=t.map(i=>({recentFile:i,...me(i)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,i=>i.id),s.forEach(i=>{const r=this._contextStore.peekKey(i.id);r!=null&&r.recentFile&&(r.file=r.recentFile,delete r.recentFile)}),e.forEach(i=>{const r=this._contextStore.peekKey(i.id);r!=null&&r.file&&(r.recentFile=r.file,delete r.file)}),this.notifySubscribers()});l(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});l(this,"updateUserGuidelines",(t,e)=>{const s=this.userGuidelines,i=t.overLimit||((e==null?void 0:e.overLimit)??!1),r={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:R.active,referenceCount:1,showWarning:i,rulesAndGuidelinesState:e};this._contextStore.update([r],s,o=>{var h;return o.id+String((h=o.userGuidelines)==null?void 0:h.overLimit)}),this.notifySubscribers()});l(this,"onGuidelinesStateUpdate",t=>{var i;this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines,s=this.userGuidelines;if(e||t.rulesAndGuidelines||s.length>0){const r=e||{overLimit:!1,contents:"",lengthLimit:((i=t.rulesAndGuidelines)==null?void 0:i.lengthLimit)??2e3};this.updateUserGuidelines(r,t.rulesAndGuidelines)}this.onSourceFoldersUpdated(this.sourceFolders.map(r=>r.sourceFolder))});l(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(i=>i.workspaceFolder===e.folderRoot);return{...e,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));l(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});l(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});l(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});l(this,"updateSelections",t=>{const e=this._contextStore.values.filter(ze),s=t.map(i=>({selection:i,...me(i)}));this._contextStore.update([],e,i=>i.id),this._contextStore.update(s,[],i=>i.id),this.notifySubscribers()});l(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});l(this,"markInactive",t=>{this.markItemsInactive([t])});l(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,R.inactive)}),this.notifySubscribers()});l(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});l(this,"markActive",t=>{this.markItemsActive([t])});l(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,R.active)}),this.notifySubscribers()});l(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});l(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});l(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});l(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Ds(t)&&!Pe(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Pe)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(ze)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Us)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter($e)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(Rs)}get userGuidelines(){return this._contextStore.values.filter(Ge)}get workspaceGuidelines(){return this._workspaceGuidelines}get agentMemories(){return[{...Os,status:this._enableAgentMemories?R.active:R.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>We(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===R.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===R.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===R.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===R.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===R.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===R.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var c;if(this.syncEnabledState===Ne.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(d=>d.progress!==void 0);if(t.length===0)return;const e=t.reduce((d,u)=>{var m;return d+(((m=u==null?void 0:u.progress)==null?void 0:m.trackedFiles)??0)},0),s=t.reduce((d,u)=>{var m;return d+(((m=u==null?void 0:u.progress)==null?void 0:m.backlogSize)??0)},0),i=Math.max(e,0),r=Math.min(Math.max(s,0),i),o=i-r,h=[];for(const d of t)(c=d==null?void 0:d.progress)!=null&&c.newlyTracked&&h.push(d.folderRoot);return{status:this._syncStatus.status,totalFiles:i,syncedCount:o,backlogSize:r,newlyTrackedFolders:h}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!($e(t)||Ge(t)||os(t)||We(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===R.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===R.inactive)}get isContextDisabled(){return this._disableContext}}class Si{constructor(){l(this,"_cache",new Ae({max:1e3}));l(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));l(this,"clear",()=>{this._cache.clear()});l(this,"update",(t,e,s)=>{t.forEach(i=>this.addInPlace(i,s)),e.forEach(i=>this.removeInPlace(i,s))});l(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});l(this,"addInPlace",(t,e)=>{const s=e(t),i=t.referenceCount??1,r=this._cache.get(s),o=t.status??(r==null?void 0:r.status)??R.active;r?(r.referenceCount+=i,r.status=o,r.pinned=t.pinned??r.pinned,r.showWarning=t.showWarning??r.showWarning,"userGuidelines"in t&&t.userGuidelines&&"userGuidelines"in r&&(r.userGuidelines=t.userGuidelines),"rulesAndGuidelinesState"in t&&t.rulesAndGuidelinesState&&"rulesAndGuidelinesState"in r&&(r.rulesAndGuidelinesState=t.rulesAndGuidelinesState)):this._cache.set(s,{...t,pinned:void 0,referenceCount:i,status:o})});l(this,"removeInPlace",(t,e)=>{const s=e(t),i=this._cache.get(s);i&&(i.referenceCount-=1,i.referenceCount===0&&this._cache.delete(s))});l(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});l(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});l(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});l(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});l(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===R.active?R.inactive:R.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}var bi=jt('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z" fill="currentColor"></path></svg>');function ji(a){var t=bi();Vt(a,t)}var xi=jt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.71 4.29L10.71 1.29L10 1H4L3 2V14L4 15H13L14 14V5L13.71 4.29ZM13 14H4V2H9V6H13V14ZM10 5V2L13 5H10Z" fill="currentColor"></path></svg>');function Vi(a){var t=xi();Vt(a,t)}var wi=jt('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1.5 14H12.5L12.98 13.63L15.61 6.63L15.13 6H14V3.5L13.5 3H7.70996L6.84998 2.15002L6.5 2H1.5L1 2.5V13.5L1.5 14ZM2 3H6.29004L7.15002 3.84998L7.5 4H13V6H8.5L8.15002 6.15002L7.29004 7H3.5L3.03003 7.33997L2.03003 10.42L2 3ZM12.13 13H2.18994L3.85999 8H7.5L7.84998 7.84998L8.70996 7H14.5L12.13 13Z" fill="currentColor"></path></svg>');function Bi(a){var t=wi();Vt(a,t)}export{vi as A,Ci as C,Ws as F,_t as N,Wi as S,ve as T,ji as a,Vi as b,Bi as c,Pi as d,H as e,Ke as f,Js as g,Bs as h,xt as i,Oi as j,$i as k,se as l,R as m,di as n,ui as o,Gi as p,Ni as q,zi as r,Ks as s,Zs as t,hs as u};
