import{x as at,m,y as M,D as st,F as lt,G as q,M as S,I as F,J as j,b as g,z as c,C as s,Q as vt,K as G,o as V,L as nt,u as ot,B as A,A as it,a8 as ht,a as dt,H as ft,a0 as p,a1 as $,O as pt,_ as mt,P as gt}from"./legacy-YP6Kq8lu.js";import{l as J,p as o,h as tt,a as O,s as $t,i as bt}from"./SpinnerAugment-Dpcl1cXc.js";import{b as l}from"./host-BNehKqab.js";import{I as wt,a as xt}from"./IconButtonAugment-CbpcmeFk.js";import{T as zt,a as et}from"./CardAugment-YBzgmAzG.js";import{B as kt}from"./ButtonAugment-DkEdzEZO.js";import{b as Ct}from"./input-C2nR_fsN.js";import{B as Lt}from"./BaseTextInput-Br9yLRnx.js";var Tt=q("<!> <!> <!>",1),Ot=q('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function Nt(K,t){var a;const Y=J(t,["children","$$slots","$$events","$$legacy"]),E=J(Y,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);at(t,!1);const b=m(),w=m(),L=m();let x,z=o(t,"defaultColor",8),I=o(t,"tooltip",24,()=>{}),P=o(t,"stateVariant",24,()=>{}),U=o(t,"onClick",8),H=o(t,"tooltipDuration",8,1500),u=o(t,"icon",8,!1),B=o(t,"stickyColor",8,!0),W=o(t,"persistOnTooltipClose",8,!1),N=o(t,"tooltipNested",24,()=>{}),r=m("neutral"),k=m(z()),R=m(void 0),D=m((a=I())==null?void 0:a.neutral);async function Q(i){var d;i.stopPropagation();try{c(r,await U()(i)??"neutral")}catch(h){console.error(h),c(r,"failure")}c(D,(d=I())==null?void 0:d[s(r)]),clearTimeout(x),x=setTimeout(()=>{var h;(h=s(R))==null||h(),B()||c(r,"neutral")},H())}M(()=>(s(b),s(w),A(E)),()=>{c(b,E.variant),c(w,it(E,["variant"]))}),M(()=>(A(P()),s(r),s(b)),()=>{var i;c(L,((i=P())==null?void 0:i[s(r)])??s(b))}),M(()=>(s(r),A(z())),()=>{s(r)==="success"?c(k,"success"):s(r)==="failure"?c(k,"error"):c(k,z())}),st(),lt();var v=Ot(),T=V(v);const n=vt(()=>(A(et),ot(()=>[et.Hover])));zt(T,{onOpenChange:function(i){var d;W()||i||(clearTimeout(x),x=void 0,c(D,(d=I())==null?void 0:d.neutral),B()||c(r,"neutral"))},get content(){return s(D)},get triggerOn(){return s(n)},get nested(){return N()},get requestClose(){return s(R)},set requestClose(i){c(R,i)},children:(i,d)=>{var h=S(),X=F(h),rt=_=>{wt(_,tt(()=>s(w),{get color(){return s(k)},get variant(){return s(L)},$$events:{click:Q,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,Z)=>{var f=Tt(),C=F(f);O(C,t,"iconLeft",{},null);var y=G(C,2);O(y,t,"default",{},null);var ut=G(y,2);O(ut,t,"iconRight",{},null),g(e,f)},$$slots:{default:!0}}))},ct=_=>{kt(_,tt(()=>s(w),{get color(){return s(k)},get variant(){return s(L)},$$events:{click:Q,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,Z)=>{var f=S(),C=F(f);O(C,t,"default",{},null),g(e,f)},$$slots:{default:!0,iconLeft:(e,Z)=>{var f=S(),C=F(f);O(C,t,"iconLeft",{},null),g(e,f)},iconRight:(e,Z)=>{var f=S(),C=F(f);O(C,t,"iconRight",{},null),g(e,f)}}}))};j(X,_=>{u()?_(rt):_(ct,!1)}),g(i,h)},$$slots:{default:!0},$$legacy:!0}),g(K,v),nt()}var At=q('<label class="c-text-area-label svelte-c1sr7w"> </label>'),Ht=q('<div class="c-text-area-label-container svelte-c1sr7w"><!> <!></div>'),Rt=q("<textarea></textarea>"),_t=q('<div class="c-text-area svelte-c1sr7w"><!> <!></div>');function Qt(K,t){const Y=$t(t),E=J(t,["children","$$slots","$$events","$$legacy"]),b=J(E,["label","variant","size","color","resize","textInput","type","value","id"]);at(t,!1);const w=m(),L=m(),x=m();let z=o(t,"label",24,()=>{}),I=o(t,"variant",8,"surface"),P=o(t,"size",8,2),U=o(t,"color",24,()=>{}),H=o(t,"resize",8,"none"),u=o(t,"textInput",28,()=>{}),B=o(t,"type",8,"default"),W=o(t,"value",12,""),N=o(t,"id",24,()=>{});function r(){if(!u())return;u(u().style.height="auto",!0);const v=.8*window.innerHeight,T=Math.min(u().scrollHeight,v);u(u().style.height=`${T}px`,!0),u(u().style.overflowY=u().scrollHeight>v?"auto":"hidden",!0)}ht(()=>{if(u()){requestAnimationFrame(r);const v=()=>r();return window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v)}}}),M(()=>A(N()),()=>{c(w,N()||`text-field-${Math.random().toString(36).substring(2,11)}`)}),M(()=>(s(L),s(x),A(b)),()=>{c(L,b.class),c(x,it(b,["class"]))}),st(),lt();var k=_t(),R=V(k),D=v=>{var T=Ht(),n=V(T),a=d=>{var h=At(),X=V(h);pt(()=>{mt(h,"for",s(w)),gt(X,z())}),g(d,h)};j(n,d=>{z()&&d(a)});var i=G(n,2);O(i,t,"topRightAction",{},null),g(v,T)};j(R,v=>{A(z()),ot(()=>z()||Y.topRightAction)&&v(D)});var Q=G(R,2);Lt(Q,{get type(){return B()},get variant(){return I()},get size(){return P()},get color(){return U()},children:(v,T)=>{var n=Rt();dt(n,a=>({id:s(w),spellCheck:"false",class:`c-text-area__input c-base-text-input__input ${s(L)}`,...s(x),[ft]:a}),[()=>({"c-textarea--resize-none":H()==="none","c-textarea--resize-both":H()==="both","c-textarea--resize-horizontal":H()==="horizontal","c-textarea--resize-vertical":H()==="vertical"})],"svelte-c1sr7w"),bt(n,a=>u(a),()=>u()),p(()=>Ct(n,W)),xt(n,a=>function(i){requestAnimationFrame(r);const d=()=>r();i.addEventListener("input",d);const h=new ResizeObserver(r);return h.observe(i),{destroy(){i.removeEventListener("input",d),h.disconnect()}}}(a)),p(()=>$("click",n,function(a){l.call(this,t,a)})),p(()=>$("focus",n,function(a){l.call(this,t,a)})),p(()=>$("keydown",n,function(a){l.call(this,t,a)})),p(()=>$("change",n,function(a){l.call(this,t,a)})),p(()=>$("input",n,function(a){l.call(this,t,a)})),p(()=>$("keyup",n,function(a){l.call(this,t,a)})),p(()=>$("blur",n,function(a){l.call(this,t,a)})),p(()=>$("select",n,function(a){l.call(this,t,a)})),p(()=>$("mouseup",n,function(a){l.call(this,t,a)})),g(v,n)},$$slots:{default:!0}}),g(K,k),nt()}export{Nt as S,Qt as T};
