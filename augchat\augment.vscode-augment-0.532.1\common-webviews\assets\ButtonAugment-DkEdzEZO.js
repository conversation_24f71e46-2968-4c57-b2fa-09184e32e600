import{G as u,J as g,u as h,o as c,K as y,O as E,Y as F,b as o,M as H,I as N,C as z,Q as x}from"./legacy-YP6Kq8lu.js";import{s as P,l as C,p as i,h as S,a as m,T as U}from"./SpinnerAugment-Dpcl1cXc.js";import{b as a}from"./host-BNehKqab.js";import{B as V}from"./IconButtonAugment-CbpcmeFk.js";var W=u('<div class="c-button--icon svelte-p25y67"><!></div>'),X=u('<div class="c-button--text svelte-p25y67"><!></div>'),Z=u('<div class="c-button--icon svelte-p25y67"><!></div>'),_=u("<div><!> <!> <!></div>");function it(w,t){const d=P(t),B=C(t,["children","$$slots","$$events","$$legacy"]),L=C(B,["size","variant","color","highContrast","disabled","radius","loading","alignment"]);let r=i(t,"size",8,2),$=i(t,"variant",8,"solid"),R=i(t,"color",8,"neutral"),G=i(t,"highContrast",8,!1),I=i(t,"disabled",8,!1),J=i(t,"radius",8,"medium"),K=i(t,"loading",8,!1),M=i(t,"alignment",8,"center");V(w,S({get size(){return r()},get variant(){return $()},get color(){return R()},get highContrast(){return G()},get disabled(){return I()},get loading(){return K()},get alignment(){return M()},get radius(){return J()}},()=>L,{$$events:{click(e){a.call(this,t,e)},keyup(e){a.call(this,t,e)},keydown(e){a.call(this,t,e)},mousedown(e){a.call(this,t,e)},mouseover(e){a.call(this,t,e)},focus(e){a.call(this,t,e)},mouseleave(e){a.call(this,t,e)},blur(e){a.call(this,t,e)},contextmenu(e){a.call(this,t,e)}},children:(e,k)=>{var v=_(),b=c(v),j=s=>{var l=W(),n=c(l);m(n,t,"iconLeft",{},null),o(s,l)};g(b,s=>{h(()=>d.iconLeft)&&s(j)});var f=y(b,2),A=s=>{var l=X(),n=c(l);const T=x(()=>r()===.5?1:r()),Y=x(()=>$()==="ghost"||r()===.5?"regular":"medium");U(n,{get size(){return z(T)},get weight(){return z(Y)},children:(q,tt)=>{var p=H(),D=N(p);m(D,t,"default",{},null),o(q,p)},$$slots:{default:!0}}),o(s,l)};g(f,s=>{h(()=>d.default)&&s(A)});var O=y(f,2),Q=s=>{var l=Z(),n=c(l);m(n,t,"iconRight",{},null),o(s,l)};g(O,s=>{h(()=>d.iconRight)&&s(Q)}),E(()=>F(v,1,`c-button--content c-button--size-${r()}`,"svelte-p25y67")),o(e,v)},$$slots:{default:!0}}))}export{it as B};
