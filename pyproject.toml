[project]
name = "ragflow"
version = "0.17.1"
description = "[RAGFlow](https://ragflow.io/) is an open-source RAG (Retrieval-Augmented Generation) engine based on deep document understanding. It offers a streamlined RAG workflow for businesses of any scale, combining LLM (Large Language Models) to provide truthful question-answering capabilities, backed by well-founded citations from various complex formatted data."
authors = [
    { name = "Zhic<PERSON> Yu", email = "<EMAIL>" }
]
license = { file = "LICENSE" }
readme = "README.md"
requires-python = ">=3.10,<3.13"
dependencies = [
    "datrie==0.8.2",
    "akshare>=1.15.78,<2.0.0",
    "azure-storage-blob==12.22.0",
    "azure-identity==1.17.1",
    "azure-storage-file-datalake==12.16.0",
    "anthropic==0.34.1",
    "arxiv==2.1.3",
    "aspose-slides>=24.9.0,<25.0.0; platform_machine == 'x86_64' or (sys_platform == 'darwin' and platform_machine == 'arm64')",
    "beartype>=0.18.5,<0.19.0",
    "bio==1.7.1",
    "blinker==1.7.0",
    "boto3==1.34.140",
    "botocore==1.34.140",
    "cachetools==5.3.3",
    "chardet==5.2.0",
    "cn2an==0.5.22",
    "cohere==5.6.2",
    "Crawl4AI==0.3.8",
    "dashscope==1.20.11",
    "deepl==1.18.0",
    "demjson3==3.0.6",
    "discord-py==2.3.2",
    "duckduckgo-search>=7.2.0,<8.0.0",
    "editdistance==0.8.1",
    "elastic-transport==8.12.0",
    "elasticsearch==8.12.1",
    "elasticsearch-dsl==8.12.0",
    "filelock==3.15.4",
    "flask==3.0.3",
    "flask-cors==5.0.0",
    "flask-login==0.6.3",
    "flask-session==0.8.0",
    "google-search-results==2.4.2",
    "groq==0.9.0",
    "hanziconv==0.3.2",
    "html-text==0.6.2",
    "httpx==0.27.0",
    "huggingface-hub>=0.25.0,<0.26.0",
    "infinity-sdk==0.6.0-dev3",
    "infinity-emb>=0.0.66,<0.0.67",
    "itsdangerous==2.1.2",
    "json-repair==0.35.0",
    "markdown==3.6",
    "markdown-to-json==2.1.1",
    "minio==7.2.4",
    "mistralai==0.4.2",
    "nltk==3.9.1",
    "numpy>=1.26.0,<2.0.0",
    "ollama==0.2.1",
    "onnxruntime==1.19.2; sys_platform == 'darwin' or platform_machine != 'x86_64'",
    "onnxruntime-gpu==1.19.2; sys_platform != 'darwin' and platform_machine == 'x86_64'",
    "openai==1.45.0",
    "opencv-python==*********",
    "opencv-python-headless==*********",
    "openpyxl>=3.1.0,<4.0.0",
    "ormsgpack==1.5.0",
    "pandas>=2.2.0,<3.0.0",
    "pdfplumber==0.10.4",
    "peewee==3.17.1",
    "pillow==10.4.0",
    "protobuf==5.27.2",
    "psycopg2-binary==2.9.9",
    "pyclipper==1.3.0.post5",
    "pycryptodomex==3.20.0",
    "pypdf>=5.0.0,<6.0.0",
    "pytest>=8.3.0,<9.0.0",
    "python-dotenv==1.0.1",
    "python-dateutil==2.8.2",
    "python-pptx>=1.0.2,<2.0.0",
    "pywencai==0.12.2",
    "qianfan==0.4.6",
    "ranx==0.3.20",
    "readability-lxml==0.8.1",
    "valkey==6.0.2",
    "requests==2.32.2",
    "replicate==0.31.0",
    "roman-numbers==1.0.2",
    "ruamel-base==1.0.0",
    "scholarly==1.7.11",
    "scikit-learn==1.5.0",
    "selenium==4.22.0",
    "selenium-wire==5.1.0",
    "setuptools>=75.2.0,<76.0.0",
    "shapely==2.0.5",
    "six==1.16.0",
    "strenum==0.4.15",
    "tabulate==0.9.0",
    "tavily-python==0.5.1",
    "tencentcloud-sdk-python==3.0.1215",
    "tika==2.6.0",
    "tiktoken==0.7.0",
    "umap_learn==0.5.6",
    "vertexai==1.64.0",
    "volcengine==1.0.146",
    "voyageai==0.2.3",
    "webdriver-manager==4.0.1",
    "werkzeug==3.0.6",
    "wikipedia==1.4.0",
    "word2number==1.1",
    "xgboost==1.5.0",
    "xpinyin==0.7.6",
    "yfinance==0.1.96",
    "zhipuai==2.0.1",
    "ruamel-yaml>=0.18.6,<0.19.0",
    "google-generativeai>=0.8.1,<0.9.0",
    "python-docx>=1.1.2,<2.0.0",
    "pypdf2>=3.0.1,<4.0.0",
    "graspologic>=3.4.1,<4.0.0",
    "pymysql>=1.1.1,<2.0.0",
    "mini-racer>=0.12.4,<0.13.0",
    "pyodbc>=5.2.0,<6.0.0",
    "pyicu>=2.13.1,<3.0.0",
    "flasgger>=*******,<0.10.0",
    "xxhash>=3.5.0,<4.0.0",
    "trio>=0.29.0",
    "aspose-slides>=24.12.0",
]

[project.optional-dependencies]
full = [
    "bcembedding==0.1.5",
    "fastembed>=0.3.6,<0.4.0; sys_platform == 'darwin' or platform_machine != 'x86_64'",
    "fastembed-gpu>=0.3.6,<0.4.0; sys_platform != 'darwin' and platform_machine == 'x86_64'",
    "flagembedding==1.2.10",
    "torch>=2.5.0,<3.0.0",
    "transformers>=4.35.0,<5.0.0"
]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"
