# Augment聊天记录导出工具使用说明

## 📋 概述

这套工具可以帮助您导出VSCode Augment插件的聊天记录，支持多种导出方式和格式。

## 📁 文件说明

- `Export-AugmentChatHistory.ps1` - PowerShell导出脚本（推荐）
- `export-augment-chat.js` - Node.js导出脚本
- `run-export.bat` - 交互式批处理文件
- `README_导出工具使用说明.md` - 本说明文件

## 🚀 快速开始

### 方法一：使用批处理文件（最简单）

1. 双击运行 `run-export.bat`
2. 根据菜单选择导出方式
3. 等待导出完成

### 方法二：直接运行PowerShell脚本

```powershell
# 在PowerShell中运行
.\Export-AugmentChatHistory.ps1

# 自定义输出路径
.\Export-AugmentChatHistory.ps1 -OutputPath "D:\MyAugmentBackup"

# 只导出工作区数据
.\Export-AugmentChatHistory.ps1 -IncludeGlobal:$false
```

### 方法三：使用Node.js脚本

```bash
# 安装依赖
npm install level

# 运行脚本
node export-augment-chat.js
```

## 🔧 环境要求

### PowerShell脚本要求：
- Windows 10/11
- PowerShell 5.0+
- WSL (Windows Subsystem for Linux)
- Python 3 (在WSL中)
- plyvel库 (脚本会自动安装)

### Node.js脚本要求：
- Node.js 14+
- level库 (脚本会自动安装)

## 📂 导出结果

### 目录结构
```
AugmentExport/
├── export_report.json          # 导出报告
├── global_storage/             # 全局配置
│   ├── augment-global-state/
│   └── augment-user-assets/
└── workspaces/                 # 工作区数据
    ├── [工作区ID1]/
    │   ├── augment-kv-store/   # 原始LevelDB文件
    │   ├── chat_history.json   # 解析后的聊天记录
    │   └── ...
    └── [工作区ID2]/
        └── ...
```

### 聊天记录格式 (chat_history.json)
```json
{
  "exportInfo": {
    "workspaceId": "工作区ID",
    "exportDate": "2025-08-21T12:00:00.000Z",
    "totalEntries": 150,
    "version": "1.0.0"
  },
  "conversations": [
    {
      "key": "conversation_key",
      "value": {
        "messages": [...],
        "timestamp": "...",
        "metadata": {...}
      },
      "metadata": {
        "keyLength": 32,
        "valueLength": 1024,
        "isJson": true
      }
    }
  ]
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. PowerShell执行策略错误
```powershell
# 临时允许脚本执行
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process

# 或者使用参数运行
powershell -ExecutionPolicy Bypass -File Export-AugmentChatHistory.ps1
```

#### 2. WSL未安装
```bash
# 在管理员PowerShell中安装WSL
wsl --install
```

#### 3. Python plyvel库安装失败
```bash
# 在WSL中手动安装
wsl
sudo apt update
sudo apt install python3-pip python3-dev libleveldb-dev
pip3 install plyvel
```

#### 4. Node.js level库安装失败
```bash
# 清理npm缓存后重试
npm cache clean --force
npm install level
```

#### 5. 权限不足
- 以管理员身份运行
- 确保对AppData目录有读取权限

### 调试模式

#### PowerShell调试
```powershell
# 启用详细输出
$VerbosePreference = "Continue"
.\Export-AugmentChatHistory.ps1 -Verbose
```

#### Node.js调试
```bash
# 启用调试模式
DEBUG=* node export-augment-chat.js
```

## 📊 导出报告说明

导出完成后会生成 `export_report.json` 文件，包含：

- 导出时间和路径
- 工作区统计信息
- 每个工作区的处理结果
- 错误信息（如有）

## 🔒 安全注意事项

1. **数据隐私**：聊天记录可能包含敏感信息，请妥善保管导出文件
2. **备份建议**：导出前建议先备份原始数据
3. **权限控制**：确保导出文件的访问权限设置正确

## 📝 使用技巧

### 1. 定期备份
```powershell
# 创建定时任务进行定期备份
$action = New-ScheduledTaskAction -Execute "PowerShell" -Argument "-File C:\Scripts\Export-AugmentChatHistory.ps1"
$trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At 2AM
Register-ScheduledTask -TaskName "AugmentBackup" -Action $action -Trigger $trigger
```

### 2. 批量处理
```powershell
# 导出多个用户的数据
$users = @("User1", "User2", "User3")
foreach ($user in $users) {
    $userPath = "C:\Users\<USER>\AppData\Roaming\Code\User"
    if (Test-Path $userPath) {
        # 修改脚本中的路径并执行
    }
}
```

### 3. 数据分析
导出的JSON文件可以用于：
- 聊天记录统计分析
- 搜索特定对话内容
- 迁移到其他平台

## 🆘 获取帮助

如果遇到问题：

1. 查看导出报告中的错误信息
2. 检查环境要求是否满足
3. 尝试不同的导出方式
4. 查看VSCode和Augment插件的日志

## 📄 许可证

本工具仅供学习和个人使用，请遵守相关法律法规和软件许可协议。
