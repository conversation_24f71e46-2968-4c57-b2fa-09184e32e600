@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   🔥 最新Augment聊天记录 - Markdown预览器
echo ========================================
echo.
echo 📅 导出时间: 2025-08-21 13:37:10
echo 📊 总记录数: 1,429条 (Strings: 681 + LevelDB: 748)
echo 📁 对话文件位置: conversations_markdown_latest\
echo.
echo 📋 最新可用的对话文件:
echo.
echo [1] 🔥 有内容但是看不见 (281条消息) - 最大的对话
echo [2] 📝 文件管理查看结果 弹出框 样式问题 看不见 (2条消息)
echo [3] 🔧 当前项目的前端和后端代码库中，git status显示有大量混乱... (14条消息)
echo [4] 🌐 Request URL http://localhost:8000/api/iot/v1/docum... (40条消息)
echo [5] 🔥 不修改ragflow代码 有解决方法澳门 (333条消息) - 第二大对话
echo [6] 📄 文档分块内容下方的列表有内容但是显示有问题... (17条消息)
echo [7] 📋 查看索引文件 (README.md)
echo [8] 📂 打开conversations_markdown_latest文件夹
echo [9] 🔄 重新导出最新聊天记录
echo [0] 退出
echo.
set /p choice="请选择要打开的对话 (0-9): "

if "%choice%"=="1" (
    echo 正在打开"有内容但是看不见"对话...
    start "" "conversations_markdown_latest\有内容但是看不见_b286fd40.md"
) else if "%choice%"=="2" (
    echo 正在打开文件管理样式问题对话...
    start "" "conversations_markdown_latest\文件管理查看结果_弹出框_样式问题_看不见_257b15d9.md"
) else if "%choice%"=="3" (
    echo 正在打开git status混乱文件对话...
    start "" "conversations_markdown_latest\当前项目的前端和后端代码库中，git_status_显示有大量混乱的文件更改需要整理。具体问题包括：..._7f183491.md"
) else if "%choice%"=="4" (
    echo 正在打开API请求对话...
    start "" "conversations_markdown_latest\Request_URL_http___localhost_8000_api_iot_v1_docum..._c593db49.md"
) else if "%choice%"=="5" (
    echo 正在打开ragflow解决方案对话...
    start "" "conversations_markdown_latest\不修改ragflow代码_有解决方法澳门_93168904.md"
) else if "%choice%"=="6" (
    echo 正在打开文档分块显示问题对话...
    start "" "conversations_markdown_latest\文档分块内容下方的列表有内容但是显示有问题，导致看不见_d84042f4.md"
) else if "%choice%"=="7" (
    echo 正在打开索引文件...
    start "" "conversations_markdown_latest\README.md"
) else if "%choice%"=="8" (
    echo 正在打开文件夹...
    start "" "conversations_markdown_latest"
) else if "%choice%"=="9" (
    echo 🔄 重新导出最新聊天记录...
    echo.
    echo 步骤1: 提取最新聊天记录...
    node extract-active-conversations.js
    echo.
    echo 步骤2: 转换为Markdown格式...
    node convert-to-markdown.js
    echo.
    echo ✅ 导出完成！重新运行此脚本查看最新结果。
    pause
    goto :eof
) else if "%choice%"=="0" (
    echo 再见！
    exit /b 0
) else (
    echo 无效选择，请重新运行脚本。
)

echo.
echo ✅ 文件已在默认Markdown编辑器中打开。
echo.
echo 💡 提示:
echo - 如果没有安装Markdown编辑器，文件将在记事本中打开
echo - 推荐使用 Typora、Mark Text 或 VSCode 预览Markdown文件
echo - 这些是您电脑中所有最新的Augment聊天记录
echo.
echo 📊 数据统计:
echo - 导出时间: 2025-08-21 13:37:10
echo - 总记录数: 1,429条
echo - 对话数量: 6个
echo - 最大对话: 333条消息 (不修改ragflow代码)
echo - 第二大: 281条消息 (有内容但是看不见)
echo.
pause
