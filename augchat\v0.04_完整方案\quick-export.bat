@echo off
chcp 65001 >nul
title 快速导出Augment聊天记录

echo.
echo 🚀 快速导出Augment聊天记录...
echo.

REM 检查依赖
if not exist "node_modules\level" (
    echo 📦 安装依赖库...
    npm install level >nul 2>&1
)

REM 运行导出
echo 📊 正在导出聊天记录...
node augment-chat-exporter.js

if %errorlevel% equ 0 (
    echo.
    echo 🎉 导出完成！
    echo.

    REM 查找最新的导出目录
    for /f "delims=" %%i in ('dir /b /ad conversations_export_* 2^>nul ^| sort /r') do (
        set "latest_dir=%%i"
        goto found_dir
    )

    :found_dir
    if defined latest_dir (
        echo 📂 输出目录: %latest_dir%
        echo.

        REM 自动打开结果
        if exist "%latest_dir%\README.md" (
            echo 📋 正在打开索引文件...
            start "" "%latest_dir%\README.md"
        )

        echo 📂 正在打开输出目录...
        start "" "%latest_dir%"
    ) else (
        echo ❌ 未找到导出目录
    )
) else (
    echo.
    echo ❌ 导出失败，请检查错误信息
)

echo.
pause
