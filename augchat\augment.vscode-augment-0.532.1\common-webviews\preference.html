<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <script type="module" crossorigin src="./assets/preference-Ce1otVrG.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw=="></script>
    <link rel="modulepreload" crossorigin href="./assets/legacy-YP6Kq8lu.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-Dpcl1cXc.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/host-BNehKqab.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-DOexUbEr.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-0Y9u1WCc.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-CbpcmeFk.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/event-modifiers-Bz4QCcZc.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-YBzgmAzG.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-gS_K9w3p.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-BfwvR7Kn.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CaEmYw0i.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-BNqj-rl6.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/input-C2nR_fsN.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-Br9yLRnx.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-CKSGO-M1.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-DfmeyYaq.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-Uytug4gU.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-Dfz0pJHr.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-4vhrZf9p.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-hRm--fCg.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-BqCUC_IY.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-DRrss2z_.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/file-type-utils-D6OEcQY2.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/chat-model-context-DZ2DTs5O.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/tool-types-Chbmg_E2.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-_jaOgw08.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/await-NDiL5Mzl.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-DbhVjGoZ.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-BNum2ZUy.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-DkEdzEZO.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DXi02sx3.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-DBqHwR-Z.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-DFL7wB0Y.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/partner-mcp-utils-Bk5-h15i.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-j5PxZ6X_.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/copy-ChvqXPeP.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-Cl2sCTw_.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-CW5cyp36.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-D5Xb9jVX.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-D8bZBTPs.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-DECje7TL.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-VK90OBn1.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-D1H89BMr.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-ClvxyORV.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-MyOXHVsl.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-bH4F3VXH.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-BIwUn_6v.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/partner-mcp-utils-dXUx3CYB.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-wJ5HqVY-.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/preference-CUbmjS6T.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
