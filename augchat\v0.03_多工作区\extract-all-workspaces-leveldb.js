#!/usr/bin/env node

/**
 * 使用LevelDB方法搜索所有工作区的聊天记录
 * 不依赖WSL，直接使用level库
 */

const fs = require('fs');
const path = require('path');
const level = require('level');

// 所有发现的工作区路径
const ALL_WORKSPACES = [
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\219eaf1da08a5e8387de19f31f58d75e\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\91a2d8828768bfdcc8646e574d310d75\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\2ba6bc3b1b2a1f3ba7a4a8ea1105cd57\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\ccef8695afe889a5115b76456b29a41b\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\fa39d3dd4cc9582898a169ca6ce47e57\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\f69c4dd7acbd82e5d292fd208beae2dd\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\dfb7adde504ea8bee353a40950c45a36\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\d936be34f07e8cf57dc708858dc61051\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\b6d890d3f556d2e6e839579e3049b903\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\93e3fcb5538492ab23bde2738d5b5943\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\841f87d0dcd2557c2c63ae1811040913\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\50d248fde9cb6028e63cc7a5222f9163\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\50c4287b8fb1a115aa9cc1c101639aba\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\43ef5d38dbe60a500811a7825f3e6e70\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\3f0eee39ea191a2cc88c4c6bbd97548d\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\396d6b9c01523ad7cabe8a7d664d7852\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\1c10c6d49ca5c5c837f4aedf23adbde2\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\0a1a719385069c57568eca409e126cc8\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\be0818f388a073cd7f2825038ea6bf6f\\Augment.vscode-augment\\augment-kv-store'
];

// 配置
const CONFIG = {
    outputDir: path.join(__dirname, 'AllWorkspacesConversations'),
    tempDir: path.join(__dirname, 'temp_all_workspaces'),
    searchKeywords: [
        '导出.*聊天记录',
        'convert.*markdown',
        'extract.*conversations',
        '预览.*markdown',
        '不适合预览',
        '一个一个对话',
        'open-conversations',
        'conversations_markdown',
        'augchat',
        '移动.*文件',
        '获取.*最新.*聊天'
    ]
};

// 工具函数
const utils = {
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`✅ 创建目录: ${dirPath}`);
        }
    },

    formatDate() {
        return new Date().toISOString().replace(/[:.]/g, '-').split('.')[0];
    },

    getWorkspaceId(workspacePath) {
        const match = workspacePath.match(/workspaceStorage[\\\/]([^\\\/]+)[\\\/]/);
        return match ? match[1] : 'unknown';
    },

    copyDatabase(sourcePath, targetPath) {
        try {
            if (fs.existsSync(sourcePath)) {
                const files = fs.readdirSync(sourcePath);
                utils.ensureDir(targetPath);
                
                for (const file of files) {
                    const sourceFile = path.join(sourcePath, file);
                    const targetFile = path.join(targetPath, file);
                    fs.copyFileSync(sourceFile, targetFile);
                }
                return true;
            }
        } catch (error) {
            console.warn(`⚠️  复制数据库失败: ${error.message}`);
        }
        return false;
    },

    checkForKeywords(text) {
        if (!text) return false;
        return CONFIG.searchKeywords.some(keyword => {
            const regex = new RegExp(keyword, 'i');
            return regex.test(text);
        });
    }
};

// 使用LevelDB方法提取数据
async function extractWithLevelDB(dbPath, workspaceId) {
    console.log(`🔍 [${workspaceId}] 使用LevelDB方法提取数据...`);
    
    try {
        if (!fs.existsSync(dbPath)) {
            console.log(`⏭️  [${workspaceId}] 路径不存在，跳过`);
            return [];
        }

        // 创建临时数据库副本
        const tempDbPath = path.join(CONFIG.tempDir, workspaceId);
        if (!utils.copyDatabase(dbPath, tempDbPath)) {
            console.log(`⏭️  [${workspaceId}] 无法复制数据库，跳过`);
            return [];
        }

        const conversations = [];
        
        try {
            const db = level(tempDbPath, { valueEncoding: 'json' });
            
            console.log(`📊 [${workspaceId}] 开始扫描数据库...`);
            
            const iterator = db.iterator();
            let count = 0;
            let keywordMatches = 0;
            
            for await (const [key, value] of iterator) {
                count++;
                
                try {
                    const keyStr = key.toString();
                    const valueStr = JSON.stringify(value);
                    
                    // 检查键或值中是否包含关键词
                    const hasKeyword = utils.checkForKeywords(keyStr) || utils.checkForKeywords(valueStr);
                    
                    // 检查是否是对话相关的数据
                    const isConversationData = keyStr.includes('exchange:') || 
                                             keyStr.includes('conversation') ||
                                             (value && (value.conversationId || value.request_message || value.response_text));
                    
                    if (hasKeyword || isConversationData) {
                        conversations.push({
                            workspaceId: workspaceId,
                            key: keyStr,
                            value: value,
                            hasKeyword: hasKeyword,
                            isConversationData: isConversationData,
                            timestamp: new Date().toISOString()
                        });
                        
                        if (hasKeyword) {
                            keywordMatches++;
                            console.log(`🎯 [${workspaceId}] 发现关键词匹配: ${keyStr.substring(0, 100)}...`);
                        }
                    }
                    
                    // 每1000条记录显示进度
                    if (count % 1000 === 0) {
                        console.log(`📊 [${workspaceId}] 已扫描 ${count} 条记录...`);
                    }
                    
                } catch (parseError) {
                    // 跳过解析错误的记录
                }
            }
            
            await db.close();
            console.log(`📊 [${workspaceId}] 扫描完成: 总记录 ${count}, 匹配 ${conversations.length}, 关键词匹配 ${keywordMatches}`);
            
        } catch (dbError) {
            console.warn(`⚠️  [${workspaceId}] 数据库操作失败: ${dbError.message}`);
        }
        
        // 清理临时文件
        try {
            fs.rmSync(tempDbPath, { recursive: true, force: true });
        } catch (cleanupError) {
            console.warn(`⚠️  [${workspaceId}] 清理临时文件失败: ${cleanupError.message}`);
        }
        
        return conversations;
        
    } catch (error) {
        console.error(`❌ [${workspaceId}] LevelDB提取失败: ${error.message}`);
        return [];
    }
}

// 主函数
async function main() {
    console.log('🚀 开始使用LevelDB方法搜索所有工作区的聊天记录...');
    console.log(`📂 发现 ${ALL_WORKSPACES.length} 个工作区`);
    console.log(`🔍 搜索关键词: ${CONFIG.searchKeywords.join(', ')}`);
    
    utils.ensureDir(CONFIG.outputDir);
    utils.ensureDir(CONFIG.tempDir);
    
    const allResults = {
        timestamp: new Date().toISOString(),
        searchKeywords: CONFIG.searchKeywords,
        workspaces: {},
        summary: {
            totalWorkspaces: ALL_WORKSPACES.length,
            processedWorkspaces: 0,
            totalConversations: 0,
            keywordMatches: 0,
            conversationDataMatches: 0
        }
    };
    
    // 处理每个工作区
    for (const workspacePath of ALL_WORKSPACES) {
        const workspaceId = utils.getWorkspaceId(workspacePath);
        console.log(`\n🔍 处理工作区: ${workspaceId}`);
        console.log(`📂 路径: ${workspacePath}`);
        
        try {
            const conversations = await extractWithLevelDB(workspacePath, workspaceId);
            
            const keywordMatches = conversations.filter(c => c.hasKeyword).length;
            const conversationMatches = conversations.filter(c => c.isConversationData).length;
            
            allResults.workspaces[workspaceId] = {
                path: workspacePath,
                conversations: conversations,
                keywordMatches: keywordMatches,
                conversationDataMatches: conversationMatches,
                totalConversations: conversations.length,
                processed: true
            };
            
            allResults.summary.processedWorkspaces++;
            allResults.summary.totalConversations += conversations.length;
            allResults.summary.keywordMatches += keywordMatches;
            allResults.summary.conversationDataMatches += conversationMatches;
            
            if (keywordMatches > 0) {
                console.log(`🎉 [${workspaceId}] 发现 ${keywordMatches} 个关键词匹配！`);
            }
            
        } catch (error) {
            console.error(`❌ [${workspaceId}] 处理失败: ${error.message}`);
            allResults.workspaces[workspaceId] = {
                path: workspacePath,
                error: error.message,
                processed: false
            };
        }
    }
    
    // 保存结果
    const outputPath = path.join(CONFIG.outputDir, `all_workspaces_leveldb_${utils.formatDate()}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2), 'utf8');
    
    console.log(`\n🎉 搜索完成！`);
    console.log(`📄 结果已保存到: ${outputPath}`);
    console.log(`\n📊 搜索摘要:`);
    console.log(`   - 总工作区数: ${allResults.summary.totalWorkspaces}`);
    console.log(`   - 已处理工作区: ${allResults.summary.processedWorkspaces}`);
    console.log(`   - 总匹配记录: ${allResults.summary.totalConversations}`);
    console.log(`   - 关键词匹配: ${allResults.summary.keywordMatches}`);
    console.log(`   - 对话数据匹配: ${allResults.summary.conversationDataMatches}`);
    
    // 显示关键词匹配的工作区
    console.log(`\n🎯 包含关键词的工作区:`);
    for (const [workspaceId, data] of Object.entries(allResults.workspaces)) {
        if (data.keywordMatches > 0) {
            console.log(`   - ${workspaceId}: ${data.keywordMatches} 个关键词匹配`);
        }
    }
    
    // 显示对话数据最多的工作区
    console.log(`\n💬 包含对话数据最多的工作区:`);
    const sortedWorkspaces = Object.entries(allResults.workspaces)
        .filter(([_, data]) => data.conversationDataMatches > 0)
        .sort(([_, a], [__, b]) => b.conversationDataMatches - a.conversationDataMatches)
        .slice(0, 5);
    
    for (const [workspaceId, data] of sortedWorkspaces) {
        console.log(`   - ${workspaceId}: ${data.conversationDataMatches} 个对话记录`);
    }
    
    // 清理临时目录
    try {
        fs.rmSync(CONFIG.tempDir, { recursive: true, force: true });
        console.log(`\n🧹 清理临时文件完成`);
    } catch (cleanupError) {
        console.warn(`⚠️  清理临时文件失败: ${cleanupError.message}`);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { main };
