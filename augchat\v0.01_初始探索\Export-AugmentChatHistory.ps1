# Augment聊天记录导出脚本
# 作者: Augment Assistant
# 日期: 2025-08-21
# 功能: 导出VSCode Augment插件的聊天记录

param(
    [string]$OutputPath = "C:\AugmentExport",
    [switch]$IncludeGlobal = $true,
    [switch]$IncludeWorkspaces = $true,
    [switch]$CreateBackup = $true
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

# 创建输出目录
function New-ExportDirectory {
    param([string]$Path)
    
    if (-not (Test-Path $Path)) {
        New-Item -ItemType Directory -Path $Path -Force | Out-Null
        Write-ColorOutput "✅ 创建导出目录: $Path" "Green"
    }
}

# 获取Augment存储路径
function Get-AugmentPaths {
    $userPath = "$env:APPDATA\Code\User"
    $globalPath = "$userPath\globalStorage\augment.vscode-augment"
    $workspacePath = "$userPath\workspaceStorage"
    
    return @{
        UserPath = $userPath
        GlobalPath = $globalPath
        WorkspacePath = $workspacePath
    }
}

# 查找所有Augment工作区
function Find-AugmentWorkspaces {
    param([string]$WorkspacePath)
    
    $workspaces = @()
    
    if (Test-Path $WorkspacePath) {
        $dirs = Get-ChildItem $WorkspacePath -Directory
        foreach ($dir in $dirs) {
            $augmentPath = Join-Path $dir.FullName "Augment.vscode-augment"
            if (Test-Path $augmentPath) {
                $kvStorePath = Join-Path $augmentPath "augment-kv-store"
                $workspaces += @{
                    Id = $dir.Name
                    Path = $augmentPath
                    KvStorePath = $kvStorePath
                    HasKvStore = (Test-Path $kvStorePath)
                }
            }
        }
    }
    
    return $workspaces
}

# 导出LevelDB数据（使用WSL和Python）
function Export-LevelDBData {
    param(
        [string]$DbPath,
        [string]$OutputFile
    )
    
    # 转换Windows路径为WSL路径
    $wslDbPath = $DbPath -replace "C:\\", "/mnt/c/" -replace "\\", "/"
    $wslOutputPath = $OutputFile -replace "C:\\", "/mnt/c/" -replace "\\", "/"
    
    # 创建Python脚本来读取LevelDB
    $pythonScript = @"
import json
import os
import sys

try:
    import plyvel
except ImportError:
    print("错误: 需要安装plyvel库")
    print("请运行: pip install plyvel")
    sys.exit(1)

db_path = sys.argv[1]
output_path = sys.argv[2]

try:
    db = plyvel.DB(db_path, create_if_missing=False)
    data = []
    
    for key, value in db:
        try:
            # 尝试解码为UTF-8字符串
            key_str = key.decode('utf-8', errors='ignore')
            value_str = value.decode('utf-8', errors='ignore')
            
            # 尝试解析JSON
            try:
                value_json = json.loads(value_str)
            except:
                value_json = value_str
                
            data.append({
                'key': key_str,
                'value': value_json,
                'raw_key': key.hex(),
                'raw_value': value.hex()
            })
        except Exception as e:
            print(f"处理键值对时出错: {e}")
            continue
    
    db.close()
    
    # 保存为JSON文件
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"成功导出 {len(data)} 条记录到 {output_path}")
    
except Exception as e:
    print(f"错误: {e}")
    sys.exit(1)
"@
    
    # 保存Python脚本到临时文件
    $tempScript = "/tmp/export_leveldb.py"
    $pythonScript | wsl bash -c "cat > $tempScript"
    
    # 执行Python脚本
    try {
        $result = wsl python3 $tempScript $wslDbPath $wslOutputPath 2>&1
        Write-ColorOutput "LevelDB导出结果: $result" "Yellow"
        return $true
    }
    catch {
        Write-ColorOutput "❌ LevelDB导出失败: $_" "Red"
        return $false
    }
}

# 导出文件夹结构
function Export-FolderStructure {
    param(
        [string]$SourcePath,
        [string]$DestPath
    )
    
    if (Test-Path $SourcePath) {
        Copy-Item $SourcePath $DestPath -Recurse -Force
        Write-ColorOutput "✅ 已复制: $SourcePath -> $DestPath" "Green"
        return $true
    }
    else {
        Write-ColorOutput "⚠️  路径不存在: $SourcePath" "Yellow"
        return $false
    }
}

# 生成导出报告
function New-ExportReport {
    param(
        [string]$OutputPath,
        [array]$Workspaces,
        [hashtable]$Results
    )
    
    $reportPath = Join-Path $OutputPath "export_report.json"
    
    $report = @{
        ExportDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        ExportPath = $OutputPath
        TotalWorkspaces = $Workspaces.Count
        WorkspacesWithKvStore = ($Workspaces | Where-Object { $_.HasKvStore }).Count
        Results = $Results
        WorkspaceDetails = $Workspaces
    }
    
    $report | ConvertTo-Json -Depth 10 | Out-File $reportPath -Encoding UTF8
    Write-ColorOutput "📊 导出报告已保存: $reportPath" "Cyan"
}

# 主函数
function Main {
    Write-ColorOutput "🚀 开始导出Augment聊天记录..." "Cyan"
    Write-ColorOutput "输出路径: $OutputPath" "White"
    
    # 创建输出目录
    New-ExportDirectory $OutputPath
    
    # 获取路径信息
    $paths = Get-AugmentPaths
    Write-ColorOutput "📁 Augment用户路径: $($paths.UserPath)" "White"
    
    # 查找工作区
    $workspaces = Find-AugmentWorkspaces $paths.WorkspacePath
    Write-ColorOutput "🔍 找到 $($workspaces.Count) 个Augment工作区" "Green"
    
    $results = @{}
    
    # 导出全局配置
    if ($IncludeGlobal -and (Test-Path $paths.GlobalPath)) {
        $globalExportPath = Join-Path $OutputPath "global_storage"
        $results.GlobalStorage = Export-FolderStructure $paths.GlobalPath $globalExportPath
        Write-ColorOutput "📦 全局存储导出完成" "Green"
    }
    
    # 导出工作区数据
    if ($IncludeWorkspaces) {
        $workspaceExportPath = Join-Path $OutputPath "workspaces"
        New-ExportDirectory $workspaceExportPath
        
        foreach ($workspace in $workspaces) {
            Write-ColorOutput "🔄 处理工作区: $($workspace.Id)" "Yellow"
            
            $wsExportPath = Join-Path $workspaceExportPath $workspace.Id
            New-ExportDirectory $wsExportPath
            
            # 复制整个工作区文件夹
            $folderResult = Export-FolderStructure $workspace.Path $wsExportPath
            
            # 如果有KV存储，尝试导出为JSON
            $kvResult = $false
            if ($workspace.HasKvStore) {
                $kvJsonPath = Join-Path $wsExportPath "chat_history.json"
                Write-ColorOutput "💾 尝试导出LevelDB数据..." "Yellow"
                $kvResult = Export-LevelDBData $workspace.KvStorePath $kvJsonPath
            }
            
            $results[$workspace.Id] = @{
                FolderCopied = $folderResult
                KvStoreExported = $kvResult
                HasKvStore = $workspace.HasKvStore
            }
        }
    }
    
    # 生成报告
    New-ExportReport $OutputPath $workspaces $results
    
    Write-ColorOutput "🎉 导出完成！" "Green"
    Write-ColorOutput "📂 请查看导出目录: $OutputPath" "Cyan"
}

# 检查依赖
function Test-Dependencies {
    # 检查WSL
    try {
        $wslResult = wsl --list --quiet 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "❌ WSL未安装或未启用" "Red"
            return $false
        }
    }
    catch {
        Write-ColorOutput "❌ 无法访问WSL" "Red"
        return $false
    }
    
    # 检查Python和plyvel
    try {
        $pythonCheck = wsl python3 -c "import plyvel; print('plyvel available')" 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "⚠️  Python plyvel库未安装，将尝试安装..." "Yellow"
            wsl pip3 install plyvel
        }
    }
    catch {
        Write-ColorOutput "⚠️  Python环境检查失败，LevelDB导出可能不可用" "Yellow"
    }
    
    return $true
}

# 脚本入口点
if (Test-Dependencies) {
    Main
} else {
    Write-ColorOutput "❌ 依赖检查失败，请确保WSL和Python环境正确配置" "Red"
    exit 1
}
