#!/usr/bin/env node

/**
 * 分析当前活跃的Augment对话
 * 专门用于找到当前正在进行的聊天记录存储位置
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 尝试导入level库
let Level;
try {
    const levelModule = require('level');
    Level = levelModule.Level || levelModule.default || levelModule;
} catch (error) {
    console.error('❌ 错误: 需要安装level库');
    console.error('请运行: npm install level');
    process.exit(1);
}

// 配置
const CONFIG = {
    userDataPath: path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User'),
    outputDir: path.join(process.cwd(), 'ConversationAnalysis'),
    searchKeywords: [
        'conversation', 'chat', 'message', 'dialog', 'user', 'assistant',
        'export', 'augment', 'tool', 'script', 'leveldb', 'vscode'
    ]
};

// 工具函数
const utils = {
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`✅ 创建目录: ${dirPath}`);
        }
    },

    formatDate(date = new Date()) {
        return date.toISOString().replace(/[:.]/g, '-').slice(0, 19);
    },

    safeJsonParse(str) {
        try {
            return JSON.parse(str);
        } catch {
            return str;
        }
    }
};

// 查找最近修改的工作区
function findRecentWorkspaces() {
    const workspaceStoragePath = path.join(CONFIG.userDataPath, 'workspaceStorage');
    const workspaces = [];

    if (!fs.existsSync(workspaceStoragePath)) {
        console.log('⚠️  工作区存储路径不存在');
        return workspaces;
    }

    const dirs = fs.readdirSync(workspaceStoragePath, { withFileTypes: true });
    
    for (const dir of dirs) {
        if (dir.isDirectory()) {
            const augmentPath = path.join(workspaceStoragePath, dir.name, 'Augment.vscode-augment');
            const kvStorePath = path.join(augmentPath, 'augment-kv-store');
            
            if (fs.existsSync(augmentPath)) {
                const stats = fs.statSync(augmentPath);
                workspaces.push({
                    id: dir.name,
                    path: augmentPath,
                    kvStorePath: kvStorePath,
                    hasKvStore: fs.existsSync(kvStorePath),
                    lastModified: stats.mtime,
                    size: getDirectorySize(augmentPath)
                });
            }
        }
    }

    // 按最后修改时间排序
    workspaces.sort((a, b) => b.lastModified - a.lastModified);
    return workspaces;
}

// 获取目录大小
function getDirectorySize(dirPath) {
    let totalSize = 0;
    try {
        const files = fs.readdirSync(dirPath, { withFileTypes: true });
        for (const file of files) {
            const filePath = path.join(dirPath, file.name);
            if (file.isDirectory()) {
                totalSize += getDirectorySize(filePath);
            } else {
                const stats = fs.statSync(filePath);
                totalSize += stats.size;
            }
        }
    } catch (error) {
        // 忽略权限错误
    }
    return totalSize;
}

// 深度分析LevelDB数据库
async function deepAnalyzeDatabase(dbPath, workspaceId) {
    console.log(`🔍 深度分析工作区 ${workspaceId} 的数据库...`);
    
    let db;
    try {
        db = new Level(dbPath, { 
            createIfMissing: false,
            errorIfExists: false,
            readOnly: true
        });
        
        const analysis = {
            workspaceId: workspaceId,
            dbPath: dbPath,
            totalEntries: 0,
            keyPatterns: {},
            valuePatterns: {},
            conversationCandidates: [],
            recentEntries: [],
            largeEntries: [],
            suspiciousEntries: []
        };
        
        console.log(`📊 开始扫描数据库...`);
        
        for await (const [key, value] of db.iterator()) {
            analysis.totalEntries++;
            
            const keyStr = key.toString('utf8');
            const keyHex = key.toString('hex');
            const valueStr = value.toString('utf8');
            const valueSize = value.length;
            
            // 分析键模式
            const keyPattern = extractKeyPattern(keyStr);
            analysis.keyPatterns[keyPattern] = (analysis.keyPatterns[keyPattern] || 0) + 1;
            
            // 分析值模式
            const valuePattern = extractValuePattern(valueStr);
            analysis.valuePatterns[valuePattern] = (analysis.valuePatterns[valuePattern] || 0) + 1;
            
            // 检查是否包含搜索关键词
            const containsKeywords = CONFIG.searchKeywords.some(keyword => 
                keyStr.toLowerCase().includes(keyword.toLowerCase()) ||
                valueStr.toLowerCase().includes(keyword.toLowerCase())
            );
            
            const entry = {
                key: keyStr,
                keyHex: keyHex,
                valuePreview: valueStr.substring(0, 200),
                valueSize: valueSize,
                containsKeywords: containsKeywords,
                keyPattern: keyPattern,
                valuePattern: valuePattern
            };
            
            // 收集可能的对话数据
            if (containsKeywords) {
                analysis.conversationCandidates.push(entry);
                console.log(`🎯 发现关键词匹配: ${keyStr}`);
            }
            
            // 收集大型条目
            if (valueSize > 1000) {
                analysis.largeEntries.push(entry);
            }
            
            // 收集可疑条目（可能包含聊天数据）
            if (valueStr.includes('"') && (valueStr.includes('message') || valueStr.includes('content'))) {
                analysis.suspiciousEntries.push(entry);
            }
            
            // 限制条目数量以避免内存问题
            if (analysis.totalEntries % 100 === 0) {
                console.log(`   已处理 ${analysis.totalEntries} 条记录...`);
            }
        }
        
        await db.close();
        
        console.log(`✅ 数据库分析完成:`);
        console.log(`   - 总条目数: ${analysis.totalEntries}`);
        console.log(`   - 关键词匹配: ${analysis.conversationCandidates.length}`);
        console.log(`   - 大型条目: ${analysis.largeEntries.length}`);
        console.log(`   - 可疑条目: ${analysis.suspiciousEntries.length}`);
        
        return analysis;
        
    } catch (error) {
        console.error(`❌ 分析数据库失败: ${error.message}`);
        if (db) {
            try {
                await db.close();
            } catch (closeError) {
                console.warn(`关闭数据库时出错: ${closeError.message}`);
            }
        }
        return null;
    }
}

// 提取键模式
function extractKeyPattern(keyStr) {
    if (/^[0-9a-f-]{36}$/.test(keyStr)) return 'UUID';
    if (/^\d+$/.test(keyStr)) return 'NUMERIC';
    if (keyStr.includes('conversation')) return 'CONVERSATION';
    if (keyStr.includes('message')) return 'MESSAGE';
    if (keyStr.includes('chat')) return 'CHAT';
    if (keyStr.includes('session')) return 'SESSION';
    if (keyStr.includes('config')) return 'CONFIG';
    if (keyStr.includes('state')) return 'STATE';
    if (keyStr.includes('cache')) return 'CACHE';
    if (keyStr.length > 50) return 'LONG_KEY';
    return 'OTHER';
}

// 提取值模式
function extractValuePattern(valueStr) {
    if (valueStr.startsWith('{') && valueStr.endsWith('}')) return 'JSON_OBJECT';
    if (valueStr.startsWith('[') && valueStr.endsWith(']')) return 'JSON_ARRAY';
    if (valueStr.includes('"messages"')) return 'MESSAGES_JSON';
    if (valueStr.includes('"content"')) return 'CONTENT_JSON';
    if (valueStr.includes('"user"') || valueStr.includes('"assistant"')) return 'ROLE_JSON';
    if (/^\d+$/.test(valueStr)) return 'NUMERIC_STRING';
    if (valueStr.length > 1000) return 'LARGE_TEXT';
    if (valueStr.length === 0) return 'EMPTY';
    return 'TEXT';
}

// 主函数
async function main() {
    console.log('🔍 开始分析当前Augment对话...');
    
    utils.ensureDir(CONFIG.outputDir);
    
    // 查找最近的工作区
    const workspaces = findRecentWorkspaces();
    console.log(`📁 找到 ${workspaces.length} 个工作区`);
    
    if (workspaces.length === 0) {
        console.log('❌ 未找到任何Augment工作区');
        return;
    }
    
    // 显示最近的工作区
    console.log('\n📊 最近修改的工作区:');
    workspaces.slice(0, 5).forEach((ws, index) => {
        console.log(`${index + 1}. ${ws.id}`);
        console.log(`   最后修改: ${ws.lastModified.toISOString()}`);
        console.log(`   大小: ${(ws.size / 1024).toFixed(2)} KB`);
        console.log(`   有聊天数据库: ${ws.hasKvStore ? '是' : '否'}`);
        console.log('');
    });
    
    // 分析最近的几个有数据库的工作区
    const analysisResults = [];
    const workspacesToAnalyze = workspaces.filter(ws => ws.hasKvStore).slice(0, 3);
    
    for (const workspace of workspacesToAnalyze) {
        const analysis = await deepAnalyzeDatabase(workspace.kvStorePath, workspace.id);
        if (analysis) {
            analysisResults.push(analysis);
        }
    }
    
    // 保存分析结果
    const reportPath = path.join(CONFIG.outputDir, `conversation_analysis_${utils.formatDate()}.json`);
    const report = {
        analysisDate: new Date().toISOString(),
        workspaces: workspaces,
        detailedAnalysis: analysisResults,
        summary: {
            totalWorkspaces: workspaces.length,
            analyzedWorkspaces: analysisResults.length,
            totalConversationCandidates: analysisResults.reduce((sum, a) => sum + a.conversationCandidates.length, 0),
            totalLargeEntries: analysisResults.reduce((sum, a) => sum + a.largeEntries.length, 0)
        }
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
    
    console.log(`\n📊 分析完成！`);
    console.log(`📄 详细报告已保存到: ${reportPath}`);
    console.log(`\n🎯 发现的可能聊天数据:`);
    
    analysisResults.forEach(analysis => {
        if (analysis.conversationCandidates.length > 0) {
            console.log(`\n工作区 ${analysis.workspaceId}:`);
            analysis.conversationCandidates.slice(0, 3).forEach(candidate => {
                console.log(`  - 键: ${candidate.key}`);
                console.log(`    值预览: ${candidate.valuePreview}...`);
                console.log(`    大小: ${candidate.valueSize} 字节`);
            });
        }
    });
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { main, deepAnalyzeDatabase, findRecentWorkspaces };
