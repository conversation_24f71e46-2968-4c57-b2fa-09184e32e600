import{a as _e,b as Ft,g as Nt,d as Ae,c as Ne,e as ve}from"./chunk-ASOPGD6M-dgmd0z3u.js";import{I as ke}from"./chunk-KFBOBJHC-CBE3Zr4f.js";import{_ as d,g as Me,s as Oe,b as te,p as De,o as Se,a as Re,c as rt,d as Rt,l as U,j as ee,e as Ce,f as Be,k as P,i as vt,x as $e,u as Y,a2 as lt,a3 as Et,a4 as ae,a5 as Ye,a6 as se,F as Ct}from"./AugmentMessage-DECje7TL.js";import"./legacy-YP6Kq8lu.js";import"./index-DOexUbEr.js";import"./SpinnerAugment-Dpcl1cXc.js";import"./CalloutAugment-0Y9u1WCc.js";import"./CardAugment-YBzgmAzG.js";import"./IconButtonAugment-CbpcmeFk.js";import"./host-BNehKqab.js";import"./event-modifiers-Bz4QCcZc.js";import"./index-CKSGO-M1.js";import"./async-messaging-gS_K9w3p.js";import"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-BNqj-rl6.js";import"./input-C2nR_fsN.js";import"./BaseTextInput-Br9yLRnx.js";import"./lodash-DfmeyYaq.js";import"./svelte-component-Uytug4gU.js";import"./Filespan-Dfz0pJHr.js";import"./index-4vhrZf9p.js";import"./diff-operations-BqCUC_IY.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-hRm--fCg.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-_jaOgw08.js";import"./message-broker-DRrss2z_.js";import"./types-CGlLNakm.js";import"./file-type-utils-D6OEcQY2.js";import"./chat-model-context-DZ2DTs5O.js";import"./tool-types-Chbmg_E2.js";import"./await-NDiL5Mzl.js";import"./OpenFileButton-DBqHwR-Z.js";import"./index-B528snJk.js";import"./remote-agents-client-DbhVjGoZ.js";import"./ra-diff-ops-model-BNum2ZUy.js";import"./TextAreaAugment-DXi02sx3.js";import"./ButtonAugment-DkEdzEZO.js";import"./CollapseButtonAugment-DFL7wB0Y.js";import"./partner-mcp-utils-Bk5-h15i.js";import"./MaterialIcon-j5PxZ6X_.js";import"./CopyButton-Cl2sCTw_.js";import"./copy-ChvqXPeP.js";import"./ellipsis-CW5cyp36.js";import"./LanguageIcon-D5Xb9jVX.js";import"./augment-logo-D8bZBTPs.js";var Bt=function(){var t=d(function(w,N,A,T){for(A=A||{},T=w.length;T--;A[w[T]]=N);return A},"o"),e=[1,2],i=[1,3],a=[1,4],n=[2,4],r=[1,9],c=[1,11],h=[1,13],g=[1,14],s=[1,16],x=[1,17],f=[1,18],u=[1,24],y=[1,25],E=[1,26],L=[1,27],v=[1,28],B=[1,29],O=[1,30],$=[1,31],R=[1,32],W=[1,33],H=[1,34],K=[1,35],Q=[1,36],q=[1,37],z=[1,38],F=[1,39],D=[1,41],X=[1,42],j=[1,43],G=[1,44],tt=[1,45],M=[1,46],b=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],_=[4,5,16,50,52,53],J=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],nt=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],k=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],Ht=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],it=[68,69,70],dt=[1,122],Ot={trace:d(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:d(function(w,N,A,T,S,l,Tt){var p=l.length-1;switch(S){case 3:return T.apply(l[p]),l[p];case 4:case 9:case 8:case 13:this.$=[];break;case 5:case 10:l[p-1].push(l[p]),this.$=l[p-1];break;case 6:case 7:case 11:case 12:case 62:this.$=l[p];break;case 15:l[p].type="createParticipant",this.$=l[p];break;case 16:l[p-1].unshift({type:"boxStart",boxData:T.parseBoxData(l[p-2])}),l[p-1].push({type:"boxEnd",boxText:l[p-2]}),this.$=l[p-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(l[p-2]),sequenceIndexStep:Number(l[p-1]),sequenceVisible:!0,signalType:T.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(l[p-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:T.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:T.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:T.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:T.LINETYPE.ACTIVE_START,actor:l[p-1].actor};break;case 23:this.$={type:"activeEnd",signalType:T.LINETYPE.ACTIVE_END,actor:l[p-1].actor};break;case 29:T.setDiagramTitle(l[p].substring(6)),this.$=l[p].substring(6);break;case 30:T.setDiagramTitle(l[p].substring(7)),this.$=l[p].substring(7);break;case 31:this.$=l[p].trim(),T.setAccTitle(this.$);break;case 32:case 33:this.$=l[p].trim(),T.setAccDescription(this.$);break;case 34:l[p-1].unshift({type:"loopStart",loopText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.LOOP_START}),l[p-1].push({type:"loopEnd",loopText:l[p-2],signalType:T.LINETYPE.LOOP_END}),this.$=l[p-1];break;case 35:l[p-1].unshift({type:"rectStart",color:T.parseMessage(l[p-2]),signalType:T.LINETYPE.RECT_START}),l[p-1].push({type:"rectEnd",color:T.parseMessage(l[p-2]),signalType:T.LINETYPE.RECT_END}),this.$=l[p-1];break;case 36:l[p-1].unshift({type:"optStart",optText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.OPT_START}),l[p-1].push({type:"optEnd",optText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.OPT_END}),this.$=l[p-1];break;case 37:l[p-1].unshift({type:"altStart",altText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.ALT_START}),l[p-1].push({type:"altEnd",signalType:T.LINETYPE.ALT_END}),this.$=l[p-1];break;case 38:l[p-1].unshift({type:"parStart",parText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.PAR_START}),l[p-1].push({type:"parEnd",signalType:T.LINETYPE.PAR_END}),this.$=l[p-1];break;case 39:l[p-1].unshift({type:"parStart",parText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.PAR_OVER_START}),l[p-1].push({type:"parEnd",signalType:T.LINETYPE.PAR_END}),this.$=l[p-1];break;case 40:l[p-1].unshift({type:"criticalStart",criticalText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.CRITICAL_START}),l[p-1].push({type:"criticalEnd",signalType:T.LINETYPE.CRITICAL_END}),this.$=l[p-1];break;case 41:l[p-1].unshift({type:"breakStart",breakText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.BREAK_START}),l[p-1].push({type:"breakEnd",optText:T.parseMessage(l[p-2]),signalType:T.LINETYPE.BREAK_END}),this.$=l[p-1];break;case 43:this.$=l[p-3].concat([{type:"option",optionText:T.parseMessage(l[p-1]),signalType:T.LINETYPE.CRITICAL_OPTION},l[p]]);break;case 45:this.$=l[p-3].concat([{type:"and",parText:T.parseMessage(l[p-1]),signalType:T.LINETYPE.PAR_AND},l[p]]);break;case 47:this.$=l[p-3].concat([{type:"else",altText:T.parseMessage(l[p-1]),signalType:T.LINETYPE.ALT_ELSE},l[p]]);break;case 48:l[p-3].draw="participant",l[p-3].type="addParticipant",l[p-3].description=T.parseMessage(l[p-1]),this.$=l[p-3];break;case 49:l[p-1].draw="participant",l[p-1].type="addParticipant",this.$=l[p-1];break;case 50:l[p-3].draw="actor",l[p-3].type="addParticipant",l[p-3].description=T.parseMessage(l[p-1]),this.$=l[p-3];break;case 51:l[p-1].draw="actor",l[p-1].type="addParticipant",this.$=l[p-1];break;case 52:l[p-1].type="destroyParticipant",this.$=l[p-1];break;case 53:this.$=[l[p-1],{type:"addNote",placement:l[p-2],actor:l[p-1].actor,text:l[p]}];break;case 54:l[p-2]=[].concat(l[p-1],l[p-1]).slice(0,2),l[p-2][0]=l[p-2][0].actor,l[p-2][1]=l[p-2][1].actor,this.$=[l[p-1],{type:"addNote",placement:T.PLACEMENT.OVER,actor:l[p-2].slice(0,2),text:l[p]}];break;case 55:this.$=[l[p-1],{type:"addLinks",actor:l[p-1].actor,text:l[p]}];break;case 56:this.$=[l[p-1],{type:"addALink",actor:l[p-1].actor,text:l[p]}];break;case 57:this.$=[l[p-1],{type:"addProperties",actor:l[p-1].actor,text:l[p]}];break;case 58:this.$=[l[p-1],{type:"addDetails",actor:l[p-1].actor,text:l[p]}];break;case 61:this.$=[l[p-2],l[p]];break;case 63:this.$=T.PLACEMENT.LEFTOF;break;case 64:this.$=T.PLACEMENT.RIGHTOF;break;case 65:this.$=[l[p-4],l[p-1],{type:"addMessage",from:l[p-4].actor,to:l[p-1].actor,signalType:l[p-3],msg:l[p],activate:!0},{type:"activeStart",signalType:T.LINETYPE.ACTIVE_START,actor:l[p-1].actor}];break;case 66:this.$=[l[p-4],l[p-1],{type:"addMessage",from:l[p-4].actor,to:l[p-1].actor,signalType:l[p-3],msg:l[p]},{type:"activeEnd",signalType:T.LINETYPE.ACTIVE_END,actor:l[p-4].actor}];break;case 67:this.$=[l[p-3],l[p-1],{type:"addMessage",from:l[p-3].actor,to:l[p-1].actor,signalType:l[p-2],msg:l[p]}];break;case 68:this.$={type:"addParticipant",actor:l[p]};break;case 69:this.$=T.LINETYPE.SOLID_OPEN;break;case 70:this.$=T.LINETYPE.DOTTED_OPEN;break;case 71:this.$=T.LINETYPE.SOLID;break;case 72:this.$=T.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=T.LINETYPE.DOTTED;break;case 74:this.$=T.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=T.LINETYPE.SOLID_CROSS;break;case 76:this.$=T.LINETYPE.DOTTED_CROSS;break;case 77:this.$=T.LINETYPE.SOLID_POINT;break;case 78:this.$=T.LINETYPE.DOTTED_POINT;break;case 79:this.$=T.parseMessage(l[p].trim().substring(1))}},"anonymous"),table:[{3:1,4:e,5:i,6:a},{1:[3]},{3:5,4:e,5:i,6:a},{3:6,4:e,5:i,6:a},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],n,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:r,5:c,8:8,9:10,12:12,13:h,14:g,17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},t(b,[2,5]),{9:47,12:12,13:h,14:g,17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},t(b,[2,7]),t(b,[2,8]),t(b,[2,14]),{12:48,50:q,52:z,53:F},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:M},{22:55,70:M},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(b,[2,29]),t(b,[2,30]),{32:[1,61]},{34:[1,62]},t(b,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:M},{22:72,70:M},{22:73,70:M},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:M},{22:90,70:M},{22:91,70:M},{22:92,70:M},t([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),t(b,[2,6]),t(b,[2,15]),t(_,[2,9],{10:93}),t(b,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},t(b,[2,21]),{5:[1,97]},{5:[1,98]},t(b,[2,24]),t(b,[2,25]),t(b,[2,26]),t(b,[2,27]),t(b,[2,28]),t(b,[2,31]),t(b,[2,32]),t(J,n,{7:99}),t(J,n,{7:100}),t(J,n,{7:101}),t(nt,n,{40:102,7:103}),t(k,n,{42:104,7:105}),t(k,n,{7:105,42:106}),t(Ht,n,{45:107,7:108}),t(J,n,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:M},t(it,[2,69]),t(it,[2,70]),t(it,[2,71]),t(it,[2,72]),t(it,[2,73]),t(it,[2,74]),t(it,[2,75]),t(it,[2,76]),t(it,[2,77]),t(it,[2,78]),{22:118,70:M},{22:120,58:119,70:M},{70:[2,63]},{70:[2,64]},{56:121,81:dt},{56:123,81:dt},{56:124,81:dt},{56:125,81:dt},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:q,52:z,53:F},{5:[1,131]},t(b,[2,19]),t(b,[2,20]),t(b,[2,22]),t(b,[2,23]),{4:r,5:c,8:8,9:10,12:12,13:h,14:g,16:[1,132],17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},{4:r,5:c,8:8,9:10,12:12,13:h,14:g,16:[1,133],17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},{4:r,5:c,8:8,9:10,12:12,13:h,14:g,16:[1,134],17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},{16:[1,135]},{4:r,5:c,8:8,9:10,12:12,13:h,14:g,16:[2,46],17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,49:[1,136],50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},{16:[1,137]},{4:r,5:c,8:8,9:10,12:12,13:h,14:g,16:[2,44],17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,48:[1,138],50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},{16:[1,139]},{16:[1,140]},{4:r,5:c,8:8,9:10,12:12,13:h,14:g,16:[2,42],17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,47:[1,141],50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},{4:r,5:c,8:8,9:10,12:12,13:h,14:g,16:[1,142],17:15,18:s,21:x,22:40,23:f,24:19,25:20,26:21,27:22,28:23,29:u,30:y,31:E,33:L,35:v,36:B,37:O,38:$,39:R,41:W,43:H,44:K,46:Q,50:q,52:z,53:F,54:D,59:X,60:j,61:G,62:tt,70:M},{15:[1,143]},t(b,[2,49]),{15:[1,144]},t(b,[2,51]),t(b,[2,52]),{22:145,70:M},{22:146,70:M},{56:147,81:dt},{56:148,81:dt},{56:149,81:dt},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(b,[2,16]),t(_,[2,10]),{12:151,50:q,52:z,53:F},t(_,[2,12]),t(_,[2,13]),t(b,[2,18]),t(b,[2,34]),t(b,[2,35]),t(b,[2,36]),t(b,[2,37]),{15:[1,152]},t(b,[2,38]),{15:[1,153]},t(b,[2,39]),t(b,[2,40]),{15:[1,154]},t(b,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:dt},{56:158,81:dt},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:M},t(_,[2,11]),t(nt,n,{7:103,40:160}),t(k,n,{7:105,42:161}),t(Ht,n,{7:108,45:162}),t(b,[2,48]),t(b,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:d(function(w,N){if(!N.recoverable){var A=new Error(w);throw A.hash=N,A}this.trace(w)},"parseError"),parse:d(function(w){var N=this,A=[0],T=[],S=[null],l=[],Tt=this.table,p="",Lt=0,jt=0,Le=l.slice.call(arguments,1),V=Object.create(this.lexer),pt={yy:{}};for(var Dt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Dt)&&(pt.yy[Dt]=this.yy[Dt]);V.setInput(w,pt.yy),pt.yy.lexer=V,pt.yy.parser=this,V.yylloc===void 0&&(V.yylloc={});var St=V.yylloc;l.push(St);var Pe=V.options&&V.options.ranges;function Ut(){var at;return typeof(at=T.pop()||V.lex()||1)!="number"&&(at instanceof Array&&(at=(T=at).pop()),at=N.symbols_[at]||at),at}typeof pt.yy.parseError=="function"?this.parseError=pt.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,d(function(at){A.length=A.length-2*at,S.length=S.length-at,l.length=l.length-at},"popStack"),d(Ut,"lex");for(var Z,gt,et,Kt,Pt,ht,Xt,_t,mt={};;){if(gt=A[A.length-1],this.defaultActions[gt]?et=this.defaultActions[gt]:(Z==null&&(Z=Ut()),et=Tt[gt]&&Tt[gt][Z]),et===void 0||!et.length||!et[0]){var Gt="";for(Pt in _t=[],Tt[gt])this.terminals_[Pt]&&Pt>2&&_t.push("'"+this.terminals_[Pt]+"'");Gt=V.showPosition?"Parse error on line "+(Lt+1)+`:
`+V.showPosition()+`
Expecting `+_t.join(", ")+", got '"+(this.terminals_[Z]||Z)+"'":"Parse error on line "+(Lt+1)+": Unexpected "+(Z==1?"end of input":"'"+(this.terminals_[Z]||Z)+"'"),this.parseError(Gt,{text:V.match,token:this.terminals_[Z]||Z,line:V.yylineno,loc:St,expected:_t})}if(et[0]instanceof Array&&et.length>1)throw new Error("Parse Error: multiple actions possible at state: "+gt+", token: "+Z);switch(et[0]){case 1:A.push(Z),S.push(V.yytext),l.push(V.yylloc),A.push(et[1]),Z=null,jt=V.yyleng,p=V.yytext,Lt=V.yylineno,St=V.yylloc;break;case 2:if(ht=this.productions_[et[1]][1],mt.$=S[S.length-ht],mt._$={first_line:l[l.length-(ht||1)].first_line,last_line:l[l.length-1].last_line,first_column:l[l.length-(ht||1)].first_column,last_column:l[l.length-1].last_column},Pe&&(mt._$.range=[l[l.length-(ht||1)].range[0],l[l.length-1].range[1]]),(Kt=this.performAction.apply(mt,[p,jt,Lt,pt.yy,et[1],S,l].concat(Le)))!==void 0)return Kt;ht&&(A=A.slice(0,-1*ht*2),S=S.slice(0,-1*ht),l=l.slice(0,-1*ht)),A.push(this.productions_[et[1]][0]),S.push(mt.$),l.push(mt._$),Xt=Tt[A[A.length-2]][A[A.length-1]],A.push(Xt);break;case 3:return!0}}return!0},"parse")},Ie=function(){return{EOF:1,parseError:d(function(w,N){if(!this.yy.parser)throw new Error(w);this.yy.parser.parseError(w,N)},"parseError"),setInput:d(function(w,N){return this.yy=N||this.yy||{},this._input=w,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:d(function(){var w=this._input[0];return this.yytext+=w,this.yyleng++,this.offset++,this.match+=w,this.matched+=w,w.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),w},"input"),unput:d(function(w){var N=w.length,A=w.split(/(?:\r\n?|\n)/g);this._input=w+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-N),this.offset-=N;var T=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),A.length-1&&(this.yylineno-=A.length-1);var S=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:A?(A.length===T.length?this.yylloc.first_column:0)+T[T.length-A.length].length-A[0].length:this.yylloc.first_column-N},this.options.ranges&&(this.yylloc.range=[S[0],S[0]+this.yyleng-N]),this.yyleng=this.yytext.length,this},"unput"),more:d(function(){return this._more=!0,this},"more"),reject:d(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:d(function(w){this.unput(this.match.slice(w))},"less"),pastInput:d(function(){var w=this.matched.substr(0,this.matched.length-this.match.length);return(w.length>20?"...":"")+w.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:d(function(){var w=this.match;return w.length<20&&(w+=this._input.substr(0,20-w.length)),(w.substr(0,20)+(w.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:d(function(){var w=this.pastInput(),N=new Array(w.length+1).join("-");return w+this.upcomingInput()+`
`+N+"^"},"showPosition"),test_match:d(function(w,N){var A,T,S;if(this.options.backtrack_lexer&&(S={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(S.yylloc.range=this.yylloc.range.slice(0))),(T=w[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=T.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:T?T[T.length-1].length-T[T.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+w[0].length},this.yytext+=w[0],this.match+=w[0],this.matches=w,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(w[0].length),this.matched+=w[0],A=this.performAction.call(this,this.yy,this,N,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),A)return A;if(this._backtrack){for(var l in S)this[l]=S[l];return!1}return!1},"test_match"),next:d(function(){if(this.done)return this.EOF;var w,N,A,T;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var S=this._currentRules(),l=0;l<S.length;l++)if((A=this._input.match(this.rules[S[l]]))&&(!N||A[0].length>N[0].length)){if(N=A,T=l,this.options.backtrack_lexer){if((w=this.test_match(A,S[l]))!==!1)return w;if(this._backtrack){N=!1;continue}return!1}if(!this.options.flex)break}return N?(w=this.test_match(N,S[T]))!==!1&&w:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:d(function(){var w=this.next();return w||this.lex()},"lex"),begin:d(function(w){this.conditionStack.push(w)},"begin"),popState:d(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:d(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:d(function(w){return(w=this.conditionStack.length-1-Math.abs(w||0))>=0?this.conditionStack[w]:"INITIAL"},"topState"),pushState:d(function(w){this.begin(w)},"pushState"),stateStackSize:d(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:d(function(w,N,A,T){switch(A){case 0:case 51:case 66:return 5;case 1:case 2:case 3:case 4:case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;case 8:return this.begin("ID"),50;case 9:return this.begin("ID"),52;case 10:return 13;case 11:return this.begin("ID"),53;case 12:return N.yytext=N.yytext.trim(),this.begin("ALIAS"),70;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;case 14:return this.popState(),this.popState(),5;case 15:return this.begin("LINE"),36;case 16:return this.begin("LINE"),37;case 17:return this.begin("LINE"),38;case 18:return this.begin("LINE"),39;case 19:return this.begin("LINE"),49;case 20:return this.begin("LINE"),41;case 21:return this.begin("LINE"),43;case 22:return this.begin("LINE"),48;case 23:return this.begin("LINE"),44;case 24:return this.begin("LINE"),47;case 25:return this.begin("LINE"),46;case 26:return this.popState(),15;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;case 37:return this.begin("ID"),23;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;case 41:return this.popState(),"acc_title_value";case 42:return this.begin("acc_descr"),33;case 43:return this.popState(),"acc_descr_value";case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 52:return N.yytext=N.yytext.trim(),70;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 68;case 65:return 69;case 67:return"INVALID"}},"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:!0}}}}();function It(){this.yy={}}return Ot.lexer=Ie,d(It,"Parser"),It.prototype=Ot,Ot.Parser=It,new It}();Bt.parser=Bt;var Ve=Bt,I=new ke(()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})),Fe=d(function(t){I.records.boxes.push({name:t.text,wrap:t.wrap??ut(),fill:t.color,actorKeys:[]}),I.records.currentBox=I.records.boxes.slice(-1)[0]},"addBox"),$t=d(function(t,e,i,a){let n=I.records.currentBox;const r=I.records.actors.get(t);if(r){if(I.records.currentBox&&r.box&&I.records.currentBox!==r.box)throw new Error(`A same participant should only be defined in one Box: ${r.name} can't be in '${r.box.name}' and in '${I.records.currentBox.name}' at the same time.`);if(n=r.box?r.box:I.records.currentBox,r.box=n,r&&e===r.name&&i==null)return}if((i==null?void 0:i.text)==null&&(i={text:e,type:a}),a!=null&&i.text!=null||(i={text:e,type:a}),I.records.actors.set(t,{box:n,name:e,description:i.text,wrap:i.wrap??ut(),prevActor:I.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:a??"participant"}),I.records.prevActor){const c=I.records.actors.get(I.records.prevActor);c&&(c.nextActor=t)}I.records.currentBox&&I.records.currentBox.actorKeys.push(t),I.records.prevActor=t},"addActor"),We=d(t=>{let e,i=0;if(!t)return 0;for(e=0;e<I.records.messages.length;e++)I.records.messages[e].type===ft.ACTIVE_START&&I.records.messages[e].from===t&&i++,I.records.messages[e].type===ft.ACTIVE_END&&I.records.messages[e].from===t&&i--;return i},"activationCount"),qe=d(function(t,e,i,a){I.records.messages.push({from:t,to:e,message:i.text,wrap:i.wrap??ut(),answer:a})},"addMessage"),st=d(function(t,e,i,a,n=!1){if(a===ft.ACTIVE_END&&We(t??"")<1){const r=new Error("Trying to inactivate an inactive participant ("+t+")");throw r.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},r}return I.records.messages.push({from:t,to:e,message:(i==null?void 0:i.text)??"",wrap:(i==null?void 0:i.wrap)??ut(),type:a,activate:n}),!0},"addSignal"),ze=d(function(){return I.records.boxes.length>0},"hasAtLeastOneBox"),He=d(function(){return I.records.boxes.some(t=>t.name)},"hasAtLeastOneBoxWithTitle"),je=d(function(){return I.records.messages},"getMessages"),Ue=d(function(){return I.records.boxes},"getBoxes"),Ke=d(function(){return I.records.actors},"getActors"),Xe=d(function(){return I.records.createdActors},"getCreatedActors"),Ge=d(function(){return I.records.destroyedActors},"getDestroyedActors"),wt=d(function(t){return I.records.actors.get(t)},"getActor"),Je=d(function(){return[...I.records.actors.keys()]},"getActorKeys"),Ze=d(function(){I.records.sequenceNumbersEnabled=!0},"enableSequenceNumbers"),Qe=d(function(){I.records.sequenceNumbersEnabled=!1},"disableSequenceNumbers"),t0=d(()=>I.records.sequenceNumbersEnabled,"showSequenceNumbers"),e0=d(function(t){I.records.wrapEnabled=t},"setWrap"),re=d(t=>{if(t===void 0)return{};t=t.trim();const e=/^:?wrap:/.exec(t)!==null||/^:?nowrap:/.exec(t)===null&&void 0;return{cleanedText:(e===void 0?t:t.replace(/^:?(?:no)?wrap:/,"")).trim(),wrap:e}},"extractWrap"),ut=d(()=>{var t;return I.records.wrapEnabled!==void 0?I.records.wrapEnabled:((t=rt().sequence)==null?void 0:t.wrap)??!1},"autoWrap"),a0=d(function(){I.reset(),$e()},"clear"),s0=d(function(t){const e=t.trim(),{wrap:i,cleanedText:a}=re(e),n={text:a,wrap:i};return U.debug(`parseMessage: ${JSON.stringify(n)}`),n},"parseMessage"),r0=d(function(t){const e=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t);let i=e!=null&&e[1]?e[1].trim():"transparent",a=e!=null&&e[2]?e[2].trim():void 0;if(window!=null&&window.CSS)window.CSS.supports("color",i)||(i="transparent",a=t.trim());else{const c=new Option().style;c.color=i,c.color!==i&&(i="transparent",a=t.trim())}const{wrap:n,cleanedText:r}=re(a);return{text:r?vt(r,rt()):void 0,color:i,wrap:n}},"parseBoxData"),ft={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},ne=d(function(t,e,i){const a={actor:t,placement:e,message:i.text,wrap:i.wrap??ut()},n=[].concat(t,t);I.records.notes.push(a),I.records.messages.push({from:n[0],to:n[1],message:i.text,wrap:i.wrap??ut(),type:ft.NOTE,placement:e})},"addNote"),oe=d(function(t,e){const i=wt(t);try{let a=vt(e.text,rt());a=a.replace(/&amp;/g,"&"),a=a.replace(/&equals;/g,"="),kt(i,JSON.parse(a))}catch(a){U.error("error while parsing actor link text",a)}},"addLinks"),n0=d(function(t,e){const i=wt(t);try{const a={};let n=vt(e.text,rt());const r=n.indexOf("@");n=n.replace(/&amp;/g,"&"),n=n.replace(/&equals;/g,"=");const c=n.slice(0,r-1).trim(),h=n.slice(r+1).trim();a[c]=h,kt(i,a)}catch(a){U.error("error while parsing actor link text",a)}},"addALink");function kt(t,e){if(t.links==null)t.links=e;else for(const i in e)t.links[i]=e[i]}d(kt,"insertLinks");var ie=d(function(t,e){const i=wt(t);try{const a=vt(e.text,rt());Wt(i,JSON.parse(a))}catch(a){U.error("error while parsing actor properties text",a)}},"addProperties");function Wt(t,e){if(t.properties==null)t.properties=e;else for(const i in e)t.properties[i]=e[i]}function ce(){I.records.currentBox=void 0}d(Wt,"insertProperties"),d(ce,"boxEnd");var le=d(function(t,e){const i=wt(t),a=document.getElementById(e.text);try{const n=a.innerHTML,r=JSON.parse(n);r.properties&&Wt(i,r.properties),r.links&&kt(i,r.links)}catch(n){U.error("error while parsing actor details text",n)}},"addDetails"),o0=d(function(t,e){if((t==null?void 0:t.properties)!==void 0)return t.properties[e]},"getActorProperty"),de=d(function(t){if(Array.isArray(t))t.forEach(function(e){de(e)});else switch(t.type){case"sequenceIndex":I.records.messages.push({from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":$t(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(I.records.actors.has(t.actor))throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");I.records.lastCreated=t.actor,$t(t.actor,t.actor,t.description,t.draw),I.records.createdActors.set(t.actor,I.records.messages.length);break;case"destroyParticipant":I.records.lastDestroyed=t.actor,I.records.destroyedActors.set(t.actor,I.records.messages.length);break;case"activeStart":case"activeEnd":st(t.actor,void 0,void 0,t.signalType);break;case"addNote":ne(t.actor,t.placement,t.text);break;case"addLinks":oe(t.actor,t.text);break;case"addALink":n0(t.actor,t.text);break;case"addProperties":ie(t.actor,t.text);break;case"addDetails":le(t.actor,t.text);break;case"addMessage":if(I.records.lastCreated){if(t.to!==I.records.lastCreated)throw new Error("The created participant "+I.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");I.records.lastCreated=void 0}else if(I.records.lastDestroyed){if(t.to!==I.records.lastDestroyed&&t.from!==I.records.lastDestroyed)throw new Error("The destroyed participant "+I.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");I.records.lastDestroyed=void 0}st(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":Fe(t.boxData);break;case"boxEnd":ce();break;case"loopStart":st(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":case"rectEnd":case"optEnd":case"altEnd":case"parEnd":case"criticalEnd":case"breakEnd":st(void 0,void 0,void 0,t.signalType);break;case"rectStart":st(void 0,void 0,t.color,t.signalType);break;case"optStart":st(void 0,void 0,t.optText,t.signalType);break;case"altStart":case"else":st(void 0,void 0,t.altText,t.signalType);break;case"setAccTitle":te(t.text);break;case"parStart":case"and":st(void 0,void 0,t.parText,t.signalType);break;case"criticalStart":st(void 0,void 0,t.criticalText,t.signalType);break;case"option":st(void 0,void 0,t.optionText,t.signalType);break;case"breakStart":st(void 0,void 0,t.breakText,t.signalType)}},"apply"),Jt={addActor:$t,addMessage:qe,addSignal:st,addLinks:oe,addDetails:le,addProperties:ie,autoWrap:ut,setWrap:e0,enableSequenceNumbers:Ze,disableSequenceNumbers:Qe,showSequenceNumbers:t0,getMessages:je,getActors:Ke,getCreatedActors:Xe,getDestroyedActors:Ge,getActor:wt,getActorKeys:Je,getActorProperty:o0,getAccTitle:Re,getBoxes:Ue,getDiagramTitle:Se,setDiagramTitle:De,getConfig:d(()=>rt().sequence,"getConfig"),clear:a0,parseMessage:s0,parseBoxData:r0,LINETYPE:ft,ARROWTYPE:{FILLED:0,OPEN:1},PLACEMENT:{LEFTOF:0,RIGHTOF:1,OVER:2},addNote:ne,setAccTitle:te,apply:de,setAccDescription:Oe,getAccDescription:Me,hasAtLeastOneBox:ze,hasAtLeastOneBoxWithTitle:He},i0=d(t=>`.actor {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }

  text.actor > tspan {
    fill: ${t.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${t.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${t.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${t.signalColor};
  }

  #arrowhead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .sequenceNumber {
    fill: ${t.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${t.signalColor};
  }

  #crosshead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .messageText {
    fill: ${t.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${t.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${t.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${t.noteBorderColor};
    fill: ${t.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${t.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation1 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation2 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${t.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
    stroke-width: 2px;
  }
`,"getStyles"),he="actor-top",pe="actor-bottom",Zt="actor-man",qt=d(function(t,e){return Ae(t,e)},"drawRect"),c0=d(function(t,e,i,a,n){if(e.links===void 0||e.links===null||Object.keys(e.links).length===0)return{height:0,width:0};const r=e.links,c=e.actorCnt,h=e.rectData;var g="none";n&&(g="block !important");const s=t.append("g");s.attr("id","actor"+c+"_popup"),s.attr("class","actorPopupMenu"),s.attr("display",g);var x="";h.class!==void 0&&(x=" "+h.class);let f=h.width>i?h.width:i;const u=s.append("rect");if(u.attr("class","actorPopupMenuPanel"+x),u.attr("x",h.x),u.attr("y",h.height),u.attr("fill",h.fill),u.attr("stroke",h.stroke),u.attr("width",f),u.attr("height",h.height),u.attr("rx",h.rx),u.attr("ry",h.ry),r!=null){var y=20;for(let v in r){var E=s.append("a"),L=ee(r[v]);E.attr("xlink:href",L),E.attr("target","_blank"),P0(a)(v,E,h.x+10,h.height+y,f,20,{class:"actor"},a),y+=30}}return u.attr("height",y),{height:h.height+y,width:f}},"drawPopup"),l0=d(function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"},"popupMenuToggle"),At=d(async function(t,e,i=null){let a=t.append("foreignObject");const n=await se(e.text,Ct()),r=a.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(n).node().getBoundingClientRect();if(a.attr("height",Math.round(r.height)).attr("width",Math.round(r.width)),e.class==="noteText"){const c=t.node().firstChild;c.setAttribute("height",r.height+2*e.textMargin);const h=c.getBBox();a.attr("x",Math.round(h.x+h.width/2-r.width/2)).attr("y",Math.round(h.y+h.height/2-r.height/2))}else if(i){let{startx:c,stopx:h,starty:g}=i;if(c>h){const s=c;c=h,h=s}a.attr("x",Math.round(c+Math.abs(c-h)/2-r.width/2)),e.class==="loopText"?a.attr("y",Math.round(g)):a.attr("y",Math.round(g-r.height))}return[a]},"drawKatex"),bt=d(function(t,e){let i=0,a=0;const n=e.text.split(P.lineBreakRegex),[r,c]=ae(e.fontSize);let h=[],g=0,s=d(()=>e.y,"yfunc");if(e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0)switch(e.valign){case"top":case"start":s=d(()=>Math.round(e.y+e.textMargin),"yfunc");break;case"middle":case"center":s=d(()=>Math.round(e.y+(i+a+e.textMargin)/2),"yfunc");break;case"bottom":case"end":s=d(()=>Math.round(e.y+(i+a+2*e.textMargin)-e.textMargin),"yfunc")}if(e.anchor!==void 0&&e.textMargin!==void 0&&e.width!==void 0)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle"}for(let[x,f]of n.entries()){e.textMargin!==void 0&&e.textMargin===0&&r!==void 0&&(g=x*r);const u=t.append("text");u.attr("x",e.x),u.attr("y",s()),e.anchor!==void 0&&u.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),e.fontFamily!==void 0&&u.style("font-family",e.fontFamily),c!==void 0&&u.style("font-size",c),e.fontWeight!==void 0&&u.style("font-weight",e.fontWeight),e.fill!==void 0&&u.attr("fill",e.fill),e.class!==void 0&&u.attr("class",e.class),e.dy!==void 0?u.attr("dy",e.dy):g!==0&&u.attr("dy",g);const y=f||Ye;if(e.tspan){const E=u.append("tspan");E.attr("x",e.x),e.fill!==void 0&&E.attr("fill",e.fill),E.text(y)}else u.text(y);e.valign!==void 0&&e.textMargin!==void 0&&e.textMargin>0&&(a+=(u._groups||u)[0][0].getBBox().height,i=a),h.push(u)}return h},"drawText"),ge=d(function(t,e){function i(n,r,c,h,g){return n+","+r+" "+(n+c)+","+r+" "+(n+c)+","+(r+h-g)+" "+(n+c-1.2*g)+","+(r+h)+" "+n+","+(r+h)}d(i,"genPoints");const a=t.append("polygon");return a.attr("points",i(e.x,e.y,e.width,e.height,7)),a.attr("class","labelBox"),e.y=e.y+e.height/2,bt(t,e),a},"drawLabel"),ct=-1,ue=d((t,e,i,a)=>{t.select&&i.forEach(n=>{const r=e.get(n),c=t.select("#actor"+r.actorCnt);!a.mirrorActors&&r.stopy?c.attr("y2",r.stopy+r.height/2):a.mirrorActors&&c.attr("y2",r.stopy)})},"fixLifeLineHeights"),d0=d(function(t,e,i,a){var y,E;const n=a?e.stopy:e.starty,r=e.x+e.width/2,c=n+e.height,h=t.append("g").lower();var g=h;a||(ct++,Object.keys(e.links||{}).length&&!i.forceMenus&&g.attr("onclick",l0(`actor${ct}_popup`)).attr("cursor","pointer"),g.append("line").attr("id","actor"+ct).attr("x1",r).attr("y1",c).attr("x2",r).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),g=h.append("g"),e.actorCnt=ct,e.links!=null&&g.attr("id","root-"+ct));const s=Nt();var x="actor";(y=e.properties)!=null&&y.class?x=e.properties.class:s.fill="#eaeaea",x+=a?` ${pe}`:` ${he}`,s.x=e.x,s.y=n,s.width=e.width,s.height=e.height,s.class=x,s.rx=3,s.ry=3,s.name=e.name;const f=qt(g,s);if(e.rectData=s,(E=e.properties)==null?void 0:E.icon){const L=e.properties.icon.trim();L.charAt(0)==="@"?Ne(g,s.x+s.width-20,s.y+10,L.substr(1)):ve(g,s.x+s.width-20,s.y+10,L)}zt(i,lt(e.description))(e.description,g,s.x,s.y,s.width,s.height,{class:"actor actor-box"},i);let u=e.height;if(f.node){const L=f.node().getBBox();e.height=L.height,u=L.height}return u},"drawActorTypeParticipant"),h0=d(function(t,e,i,a){const n=a?e.stopy:e.starty,r=e.x+e.width/2,c=n+80,h=t.append("g").lower();a||(ct++,h.append("line").attr("id","actor"+ct).attr("x1",r).attr("y1",c).attr("x2",r).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),e.actorCnt=ct);const g=t.append("g");let s=Zt;s+=a?` ${pe}`:` ${he}`,g.attr("class",s),g.attr("name",e.name);const x=Nt();x.x=e.x,x.y=n,x.fill="#eaeaea",x.width=e.width,x.height=e.height,x.class="actor",x.rx=3,x.ry=3,g.append("line").attr("id","actor-man-torso"+ct).attr("x1",r).attr("y1",n+25).attr("x2",r).attr("y2",n+45),g.append("line").attr("id","actor-man-arms"+ct).attr("x1",r-18).attr("y1",n+33).attr("x2",r+18).attr("y2",n+33),g.append("line").attr("x1",r-18).attr("y1",n+60).attr("x2",r).attr("y2",n+45),g.append("line").attr("x1",r).attr("y1",n+45).attr("x2",r+18-2).attr("y2",n+60);const f=g.append("circle");f.attr("cx",e.x+e.width/2),f.attr("cy",n+10),f.attr("r",15),f.attr("width",e.width),f.attr("height",e.height);const u=g.node().getBBox();return e.height=u.height,zt(i,lt(e.description))(e.description,g,x.x,x.y+35,x.width,x.height,{class:`actor ${Zt}`},i),e.height},"drawActorTypeActor"),p0=d(async function(t,e,i,a){switch(e.type){case"actor":return await h0(t,e,i,a);case"participant":return await d0(t,e,i,a)}},"drawActor"),g0=d(function(t,e,i){const a=t.append("g");xe(a,e),e.name&&zt(i)(e.name,a,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},i),a.lower()},"drawBox"),u0=d(function(t){return t.append("g")},"anchorElement"),x0=d(function(t,e,i,a,n){const r=Nt(),c=e.anchored;r.x=e.startx,r.y=e.starty,r.class="activation"+n%3,r.width=e.stopx-e.startx,r.height=i-e.starty,qt(c,r)},"drawActivation"),m0=d(async function(t,e,i,a){const{boxMargin:n,boxTextMargin:r,labelBoxHeight:c,labelBoxWidth:h,messageFontFamily:g,messageFontSize:s,messageFontWeight:x}=a,f=t.append("g"),u=d(function(L,v,B,O){return f.append("line").attr("x1",L).attr("y1",v).attr("x2",B).attr("y2",O).attr("class","loopLine")},"drawLoopLine");u(e.startx,e.starty,e.stopx,e.starty),u(e.stopx,e.starty,e.stopx,e.stopy),u(e.startx,e.stopy,e.stopx,e.stopy),u(e.startx,e.starty,e.startx,e.stopy),e.sections!==void 0&&e.sections.forEach(function(L){u(e.startx,L.y,e.stopx,L.y).style("stroke-dasharray","3, 3")});let y=Ft();y.text=i,y.x=e.startx,y.y=e.starty,y.fontFamily=g,y.fontSize=s,y.fontWeight=x,y.anchor="middle",y.valign="middle",y.tspan=!1,y.width=h||50,y.height=c||20,y.textMargin=r,y.class="labelText",ge(f,y),y=me(),y.text=e.title,y.x=e.startx+h/2+(e.stopx-e.startx)/2,y.y=e.starty+n+r,y.anchor="middle",y.valign="middle",y.textMargin=r,y.class="loopText",y.fontFamily=g,y.fontSize=s,y.fontWeight=x,y.wrap=!0;let E=lt(y.text)?await At(f,y,e):bt(f,y);if(e.sectionTitles!==void 0){for(const[L,v]of Object.entries(e.sectionTitles))if(v.message){y.text=v.message,y.x=e.startx+(e.stopx-e.startx)/2,y.y=e.sections[L].y+n+r,y.class="loopText",y.anchor="middle",y.valign="middle",y.tspan=!1,y.fontFamily=g,y.fontSize=s,y.fontWeight=x,y.wrap=e.wrap,lt(y.text)?(e.starty=e.sections[L].y,await At(f,y,e)):bt(f,y);let B=Math.round(E.map(O=>(O._groups||O)[0][0].getBBox().height).reduce((O,$)=>O+$));e.sections[L].height+=B-(n+r)}}return e.height=Math.round(e.stopy-e.starty),f},"drawLoop"),xe=d(function(t,e){_e(t,e)},"drawBackgroundRect"),y0=d(function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),b0=d(function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),T0=d(function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),f0=d(function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")},"insertArrowHead"),E0=d(function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),w0=d(function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertSequenceNumber"),I0=d(function(t){t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},"insertArrowCrossHead"),me=d(function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},"getTextObj"),L0=d(function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),zt=function(){function t(r,c,h,g,s,x,f){n(c.append("text").attr("x",h+s/2).attr("y",g+x/2+5).style("text-anchor","middle").text(r),f)}function e(r,c,h,g,s,x,f,u){const{actorFontSize:y,actorFontFamily:E,actorFontWeight:L}=u,[v,B]=ae(y),O=r.split(P.lineBreakRegex);for(let $=0;$<O.length;$++){const R=$*v-v*(O.length-1)/2,W=c.append("text").attr("x",h+s/2).attr("y",g).style("text-anchor","middle").style("font-size",B).style("font-weight",L).style("font-family",E);W.append("tspan").attr("x",h+s/2).attr("dy",R).text(O[$]),W.attr("y",g+x/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),n(W,f)}}function i(r,c,h,g,s,x,f,u){const y=c.append("switch"),E=y.append("foreignObject").attr("x",h).attr("y",g).attr("width",s).attr("height",x).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");E.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(r),e(r,y,h,g,s,x,f,u),n(E,f)}async function a(r,c,h,g,s,x,f,u){const y=await Et(r,Ct()),E=c.append("switch"),L=E.append("foreignObject").attr("x",h+s/2-y.width/2).attr("y",g+x/2-y.height/2).attr("width",y.width).attr("height",y.height).append("xhtml:div").style("height","100%").style("width","100%");L.append("div").style("text-align","center").style("vertical-align","middle").html(await se(r,Ct())),e(r,E,h,g,s,x,f,u),n(L,f)}function n(r,c){for(const h in c)c.hasOwnProperty(h)&&r.attr(h,c[h])}return d(t,"byText"),d(e,"byTspan"),d(i,"byFo"),d(a,"byKatex"),d(n,"_setTextAttrs"),function(r,c=!1){return c?a:r.textPlacement==="fo"?i:r.textPlacement==="old"?t:e}}(),P0=function(){function t(n,r,c,h,g,s,x){a(r.append("text").attr("x",c).attr("y",h).style("text-anchor","start").text(n),x)}function e(n,r,c,h,g,s,x,f){const{actorFontSize:u,actorFontFamily:y,actorFontWeight:E}=f,L=n.split(P.lineBreakRegex);for(let v=0;v<L.length;v++){const B=v*u-u*(L.length-1)/2,O=r.append("text").attr("x",c).attr("y",h).style("text-anchor","start").style("font-size",u).style("font-weight",E).style("font-family",y);O.append("tspan").attr("x",c).attr("dy",B).text(L[v]),O.attr("y",h+s/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),a(O,x)}}function i(n,r,c,h,g,s,x,f){const u=r.append("switch"),y=u.append("foreignObject").attr("x",c).attr("y",h).attr("width",g).attr("height",s).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");y.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(n),e(n,u,c,h,0,s,x,f),a(y,x)}function a(n,r){for(const c in r)r.hasOwnProperty(c)&&n.attr(c,r[c])}return d(t,"byText"),d(e,"byTspan"),d(i,"byFo"),d(a,"_setTextAttrs"),function(n){return n.textPlacement==="fo"?i:n.textPlacement==="old"?t:e}}(),C={drawRect:qt,drawText:bt,drawLabel:ge,drawActor:p0,drawBox:g0,drawPopup:c0,anchorElement:u0,drawActivation:x0,drawLoop:m0,drawBackgroundRect:xe,insertArrowHead:f0,insertArrowFilledHead:E0,insertSequenceNumber:w0,insertArrowCrossHead:I0,insertDatabaseIcon:y0,insertComputerIcon:b0,insertClockIcon:T0,getTextObj:me,getNoteRect:L0,fixLifeLineHeights:ue,sanitizeUrl:ee},o={},m={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:d(function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map(t=>t.height||0))+(this.loops.length===0?0:this.loops.map(t=>t.height||0).reduce((t,e)=>t+e))+(this.messages.length===0?0:this.messages.map(t=>t.height||0).reduce((t,e)=>t+e))+(this.notes.length===0?0:this.notes.map(t=>t.height||0).reduce((t,e)=>t+e))},"getHeight"),clear:d(function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},"clear"),addBox:d(function(t){this.boxes.push(t)},"addBox"),addActor:d(function(t){this.actors.push(t)},"addActor"),addLoop:d(function(t){this.loops.push(t)},"addLoop"),addMessage:d(function(t){this.messages.push(t)},"addMessage"),addNote:d(function(t){this.notes.push(t)},"addNote"),lastActor:d(function(){return this.actors[this.actors.length-1]},"lastActor"),lastLoop:d(function(){return this.loops[this.loops.length-1]},"lastLoop"),lastMessage:d(function(){return this.messages[this.messages.length-1]},"lastMessage"),lastNote:d(function(){return this.notes[this.notes.length-1]},"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:d(function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,Te(rt())},"init"),updateVal:d(function(t,e,i,a){t[e]===void 0?t[e]=i:t[e]=a(i,t[e])},"updateVal"),updateBounds:d(function(t,e,i,a){const n=this;let r=0;function c(h){return d(function(g){r++;const s=n.sequenceItems.length-r+1;n.updateVal(g,"starty",e-s*o.boxMargin,Math.min),n.updateVal(g,"stopy",a+s*o.boxMargin,Math.max),n.updateVal(m.data,"startx",t-s*o.boxMargin,Math.min),n.updateVal(m.data,"stopx",i+s*o.boxMargin,Math.max),h!=="activation"&&(n.updateVal(g,"startx",t-s*o.boxMargin,Math.min),n.updateVal(g,"stopx",i+s*o.boxMargin,Math.max),n.updateVal(m.data,"starty",e-s*o.boxMargin,Math.min),n.updateVal(m.data,"stopy",a+s*o.boxMargin,Math.max))},"updateItemBounds")}d(c,"updateFn"),this.sequenceItems.forEach(c()),this.activations.forEach(c("activation"))},"updateBounds"),insert:d(function(t,e,i,a){const n=P.getMin(t,i),r=P.getMax(t,i),c=P.getMin(e,a),h=P.getMax(e,a);this.updateVal(m.data,"startx",n,Math.min),this.updateVal(m.data,"starty",c,Math.min),this.updateVal(m.data,"stopx",r,Math.max),this.updateVal(m.data,"stopy",h,Math.max),this.updateBounds(n,c,r,h)},"insert"),newActivation:d(function(t,e,i){const a=i.get(t.from),n=Mt(t.from).length||0,r=a.x+a.width/2+(n-1)*o.activationWidth/2;this.activations.push({startx:r,starty:this.verticalPos+2,stopx:r+o.activationWidth,stopy:void 0,actor:t.from,anchored:C.anchorElement(e)})},"newActivation"),endActivation:d(function(t){const e=this.activations.map(function(i){return i.actor}).lastIndexOf(t.from);return this.activations.splice(e,1)[0]},"endActivation"),createLoop:d(function(t={message:void 0,wrap:!1,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},"createLoop"),newLoop:d(function(t={message:void 0,wrap:!1,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},"newLoop"),endLoop:d(function(){return this.sequenceItems.pop()},"endLoop"),isLoopOverlap:d(function(){return!!this.sequenceItems.length&&this.sequenceItems[this.sequenceItems.length-1].overlap},"isLoopOverlap"),addSectionToLoop:d(function(t){const e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:m.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)},"addSectionToLoop"),saveVerticalPos:d(function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},"saveVerticalPos"),resetVerticalPos:d(function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},"resetVerticalPos"),bumpVerticalPos:d(function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=P.getMax(this.data.stopy,this.verticalPos)},"bumpVerticalPos"),getVerticalPos:d(function(){return this.verticalPos},"getVerticalPos"),getBounds:d(function(){return{bounds:this.data,models:this.models}},"getBounds")},_0=d(async function(t,e){m.bumpVerticalPos(o.boxMargin),e.height=o.boxMargin,e.starty=m.getVerticalPos();const i=Nt();i.x=e.startx,i.y=e.starty,i.width=e.width||o.width,i.class="note";const a=t.append("g"),n=C.drawRect(a,i),r=Ft();r.x=e.startx,r.y=e.starty,r.width=i.width,r.dy="1em",r.text=e.message,r.class="noteText",r.fontFamily=o.noteFontFamily,r.fontSize=o.noteFontSize,r.fontWeight=o.noteFontWeight,r.anchor=o.noteAlign,r.textMargin=o.noteMargin,r.valign="center";const c=lt(r.text)?await At(a,r):bt(a,r),h=Math.round(c.map(g=>(g._groups||g)[0][0].getBBox().height).reduce((g,s)=>g+s));n.attr("height",h+2*o.noteMargin),e.height+=h+2*o.noteMargin,m.bumpVerticalPos(h+2*o.noteMargin),e.stopy=e.starty+h+2*o.noteMargin,e.stopx=e.startx+i.width,m.insert(e.startx,e.starty,e.stopx,e.stopy),m.models.addNote(e)},"drawNote"),xt=d(t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),"messageFont"),yt=d(t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight}),"noteFont"),Yt=d(t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight}),"actorFont");async function ye(t,e){m.bumpVerticalPos(10);const{startx:i,stopx:a,message:n}=e,r=P.splitBreaks(n).length,c=lt(n),h=c?await Et(n,rt()):Y.calculateTextDimensions(n,xt(o));if(!c){const f=h.height/r;e.height+=f,m.bumpVerticalPos(f)}let g,s=h.height-10;const x=h.width;if(i===a){g=m.getVerticalPos()+s,o.rightAngles||(s+=o.boxMargin,g=m.getVerticalPos()+s),s+=30;const f=P.getMax(x/2,o.width/2);m.insert(i-f,m.getVerticalPos()-10+s,a+f,m.getVerticalPos()+30+s)}else s+=o.boxMargin,g=m.getVerticalPos()+s,m.insert(i,g-10,a,g);return m.bumpVerticalPos(s),e.height+=s,e.stopy=e.starty+e.height,m.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),g}d(ye,"boundMessage");var A0=d(async function(t,e,i,a){const{startx:n,stopx:r,starty:c,message:h,type:g,sequenceIndex:s,sequenceVisible:x}=e,f=Y.calculateTextDimensions(h,xt(o)),u=Ft();u.x=n,u.y=c+10,u.width=r-n,u.class="messageText",u.dy="1em",u.text=h,u.fontFamily=o.messageFontFamily,u.fontSize=o.messageFontSize,u.fontWeight=o.messageFontWeight,u.anchor=o.messageAlign,u.valign="center",u.textMargin=o.wrapPadding,u.tspan=!1,lt(u.text)?await At(t,u,{startx:n,stopx:r,starty:i}):bt(t,u);const y=f.width;let E;n===r?E=o.rightAngles?t.append("path").attr("d",`M  ${n},${i} H ${n+P.getMax(o.width/2,y/2)} V ${i+25} H ${n}`):t.append("path").attr("d","M "+n+","+i+" C "+(n+60)+","+(i-10)+" "+(n+60)+","+(i+30)+" "+n+","+(i+20)):(E=t.append("line"),E.attr("x1",n),E.attr("y1",i),E.attr("x2",r),E.attr("y2",i)),g===a.db.LINETYPE.DOTTED||g===a.db.LINETYPE.DOTTED_CROSS||g===a.db.LINETYPE.DOTTED_POINT||g===a.db.LINETYPE.DOTTED_OPEN||g===a.db.LINETYPE.BIDIRECTIONAL_DOTTED?(E.style("stroke-dasharray","3, 3"),E.attr("class","messageLine1")):E.attr("class","messageLine0");let L="";o.arrowMarkerAbsolute&&(L=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,L=L.replace(/\(/g,"\\("),L=L.replace(/\)/g,"\\)")),E.attr("stroke-width",2),E.attr("stroke","none"),E.style("fill","none"),g!==a.db.LINETYPE.SOLID&&g!==a.db.LINETYPE.DOTTED||E.attr("marker-end","url("+L+"#arrowhead)"),g!==a.db.LINETYPE.BIDIRECTIONAL_SOLID&&g!==a.db.LINETYPE.BIDIRECTIONAL_DOTTED||(E.attr("marker-start","url("+L+"#arrowhead)"),E.attr("marker-end","url("+L+"#arrowhead)")),g!==a.db.LINETYPE.SOLID_POINT&&g!==a.db.LINETYPE.DOTTED_POINT||E.attr("marker-end","url("+L+"#filled-head)"),g!==a.db.LINETYPE.SOLID_CROSS&&g!==a.db.LINETYPE.DOTTED_CROSS||E.attr("marker-end","url("+L+"#crosshead)"),(x||o.showSequenceNumbers)&&(E.attr("marker-start","url("+L+"#sequencenumber)"),t.append("text").attr("x",n).attr("y",i+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(s))},"drawMessage"),N0=d(function(t,e,i,a,n,r,c){let h,g=0,s=0,x=0;for(const f of a){const u=e.get(f),y=u.box;h&&h!=y&&(c||m.models.addBox(h),s+=o.boxMargin+h.margin),y&&y!=h&&(c||(y.x=g+s,y.y=n),s+=y.margin),u.width=u.width||o.width,u.height=P.getMax(u.height||o.height,o.height),u.margin=u.margin||o.actorMargin,x=P.getMax(x,u.height),i.get(u.name)&&(s+=u.width/2),u.x=g+s,u.starty=m.getVerticalPos(),m.insert(u.x,n,u.x+u.width,u.height),g+=u.width+s,u.box&&(u.box.width=g+y.margin-u.box.x),s=u.margin,h=u.box,m.models.addActor(u)}h&&!c&&m.models.addBox(h),m.bumpVerticalPos(x)},"addActorRenderingData"),Vt=d(async function(t,e,i,a){if(a){let n=0;m.bumpVerticalPos(2*o.boxMargin);for(const r of i){const c=e.get(r);c.stopy||(c.stopy=m.getVerticalPos());const h=await C.drawActor(t,c,o,!0);n=P.getMax(n,h)}m.bumpVerticalPos(n+o.boxMargin)}else for(const n of i){const r=e.get(n);await C.drawActor(t,r,o,!1)}},"drawActors"),be=d(function(t,e,i,a){let n=0,r=0;for(const c of i){const h=e.get(c),g=k0(h),s=C.drawPopup(t,h,g,o,o.forceMenus,a);s.height>n&&(n=s.height),s.width+h.x>r&&(r=s.width+h.x)}return{maxHeight:n,maxWidth:r}},"drawActorsPopup"),Te=d(function(t){Be(o,t),t.fontFamily&&(o.actorFontFamily=o.noteFontFamily=o.messageFontFamily=t.fontFamily),t.fontSize&&(o.actorFontSize=o.noteFontSize=o.messageFontSize=t.fontSize),t.fontWeight&&(o.actorFontWeight=o.noteFontWeight=o.messageFontWeight=t.fontWeight)},"setConf"),Mt=d(function(t){return m.activations.filter(function(e){return e.actor===t})},"actorActivations"),Qt=d(function(t,e){const i=e.get(t),a=Mt(t);return[a.reduce(function(n,r){return P.getMin(n,r.startx)},i.x+i.width/2-1),a.reduce(function(n,r){return P.getMax(n,r.stopx)},i.x+i.width/2+1)]},"activationBounds");function ot(t,e,i,a,n){m.bumpVerticalPos(i);let r=a;if(e.id&&e.message&&t[e.id]){const c=t[e.id].width,h=xt(o);e.message=Y.wrapLabel(`[${e.message}]`,c-2*o.wrapPadding,h),e.width=c,e.wrap=!0;const g=Y.calculateTextDimensions(e.message,h),s=P.getMax(g.height,o.labelBoxHeight);r=a+s,U.debug(`${s} - ${e.message}`)}n(e),m.bumpVerticalPos(r)}function fe(t,e,i,a,n,r,c){function h(s,x){s.x<n.get(t.from).x?(m.insert(e.stopx-x,e.starty,e.startx,e.stopy+s.height/2+o.noteMargin),e.stopx=e.stopx+x):(m.insert(e.startx,e.starty,e.stopx+x,e.stopy+s.height/2+o.noteMargin),e.stopx=e.stopx-x)}function g(s,x){s.x<n.get(t.to).x?(m.insert(e.startx-x,e.starty,e.stopx,e.stopy+s.height/2+o.noteMargin),e.startx=e.startx+x):(m.insert(e.stopx,e.starty,e.startx+x,e.stopy+s.height/2+o.noteMargin),e.startx=e.startx-x)}if(d(h,"receiverAdjustment"),d(g,"senderAdjustment"),r.get(t.to)==a){const s=n.get(t.to);h(s,s.type=="actor"?21:s.width/2+3),s.starty=i-s.height/2,m.bumpVerticalPos(s.height/2)}else if(c.get(t.from)==a){const s=n.get(t.from);o.mirrorActors&&g(s,s.type=="actor"?18:s.width/2),s.stopy=i-s.height/2,m.bumpVerticalPos(s.height/2)}else if(c.get(t.to)==a){const s=n.get(t.to);o.mirrorActors&&h(s,s.type=="actor"?21:s.width/2+3),s.stopy=i-s.height/2,m.bumpVerticalPos(s.height/2)}}d(ot,"adjustLoopHeightForWrap"),d(fe,"adjustCreatedDestroyedData");var v0=d(async function(t,e,i,a){const{securityLevel:n,sequence:r}=rt();let c;o=r,n==="sandbox"&&(c=Rt("#i"+e));const h=Rt(n==="sandbox"?c.nodes()[0].contentDocument.body:"body"),g=n==="sandbox"?c.nodes()[0].contentDocument:document;m.init(),U.debug(a.db);const s=n==="sandbox"?h.select(`[id="${e}"]`):Rt(`[id="${e}"]`),x=a.db.getActors(),f=a.db.getCreatedActors(),u=a.db.getDestroyedActors(),y=a.db.getBoxes();let E=a.db.getActorKeys();const L=a.db.getMessages(),v=a.db.getDiagramTitle(),B=a.db.hasAtLeastOneBox(),O=a.db.hasAtLeastOneBoxWithTitle(),$=await Ee(x,L,a);if(o.height=await we(x,$,y),C.insertComputerIcon(s),C.insertDatabaseIcon(s),C.insertClockIcon(s),B&&(m.bumpVerticalPos(o.boxMargin),O&&m.bumpVerticalPos(y[0].textMaxHeight)),o.hideUnusedParticipants===!0){const b=new Set;L.forEach(_=>{b.add(_.from),b.add(_.to)}),E=E.filter(_=>b.has(_))}N0(s,x,f,E,0,L,!1);const R=await D0(L,x,$,a);function W(b,_){const J=m.endActivation(b);J.starty+18>_&&(J.starty=_-6,_+=12),C.drawActivation(s,J,_,o,Mt(b.from).length),m.insert(J.startx,_-10,J.stopx,_)}C.insertArrowHead(s),C.insertArrowCrossHead(s),C.insertArrowFilledHead(s),C.insertSequenceNumber(s),d(W,"activeEnd");let H=1,K=1;const Q=[],q=[];let z=0;for(const b of L){let _,J,nt;switch(b.type){case a.db.LINETYPE.NOTE:m.resetVerticalPos(),J=b.noteModel,await _0(s,J);break;case a.db.LINETYPE.ACTIVE_START:m.newActivation(b,s,x);break;case a.db.LINETYPE.ACTIVE_END:W(b,m.getVerticalPos());break;case a.db.LINETYPE.LOOP_START:ot(R,b,o.boxMargin,o.boxMargin+o.boxTextMargin,k=>m.newLoop(k));break;case a.db.LINETYPE.LOOP_END:_=m.endLoop(),await C.drawLoop(s,_,"loop",o),m.bumpVerticalPos(_.stopy-m.getVerticalPos()),m.models.addLoop(_);break;case a.db.LINETYPE.RECT_START:ot(R,b,o.boxMargin,o.boxMargin,k=>m.newLoop(void 0,k.message));break;case a.db.LINETYPE.RECT_END:_=m.endLoop(),q.push(_),m.models.addLoop(_),m.bumpVerticalPos(_.stopy-m.getVerticalPos());break;case a.db.LINETYPE.OPT_START:ot(R,b,o.boxMargin,o.boxMargin+o.boxTextMargin,k=>m.newLoop(k));break;case a.db.LINETYPE.OPT_END:_=m.endLoop(),await C.drawLoop(s,_,"opt",o),m.bumpVerticalPos(_.stopy-m.getVerticalPos()),m.models.addLoop(_);break;case a.db.LINETYPE.ALT_START:ot(R,b,o.boxMargin,o.boxMargin+o.boxTextMargin,k=>m.newLoop(k));break;case a.db.LINETYPE.ALT_ELSE:ot(R,b,o.boxMargin+o.boxTextMargin,o.boxMargin,k=>m.addSectionToLoop(k));break;case a.db.LINETYPE.ALT_END:_=m.endLoop(),await C.drawLoop(s,_,"alt",o),m.bumpVerticalPos(_.stopy-m.getVerticalPos()),m.models.addLoop(_);break;case a.db.LINETYPE.PAR_START:case a.db.LINETYPE.PAR_OVER_START:ot(R,b,o.boxMargin,o.boxMargin+o.boxTextMargin,k=>m.newLoop(k)),m.saveVerticalPos();break;case a.db.LINETYPE.PAR_AND:ot(R,b,o.boxMargin+o.boxTextMargin,o.boxMargin,k=>m.addSectionToLoop(k));break;case a.db.LINETYPE.PAR_END:_=m.endLoop(),await C.drawLoop(s,_,"par",o),m.bumpVerticalPos(_.stopy-m.getVerticalPos()),m.models.addLoop(_);break;case a.db.LINETYPE.AUTONUMBER:H=b.message.start||H,K=b.message.step||K,b.message.visible?a.db.enableSequenceNumbers():a.db.disableSequenceNumbers();break;case a.db.LINETYPE.CRITICAL_START:ot(R,b,o.boxMargin,o.boxMargin+o.boxTextMargin,k=>m.newLoop(k));break;case a.db.LINETYPE.CRITICAL_OPTION:ot(R,b,o.boxMargin+o.boxTextMargin,o.boxMargin,k=>m.addSectionToLoop(k));break;case a.db.LINETYPE.CRITICAL_END:_=m.endLoop(),await C.drawLoop(s,_,"critical",o),m.bumpVerticalPos(_.stopy-m.getVerticalPos()),m.models.addLoop(_);break;case a.db.LINETYPE.BREAK_START:ot(R,b,o.boxMargin,o.boxMargin+o.boxTextMargin,k=>m.newLoop(k));break;case a.db.LINETYPE.BREAK_END:_=m.endLoop(),await C.drawLoop(s,_,"break",o),m.bumpVerticalPos(_.stopy-m.getVerticalPos()),m.models.addLoop(_);break;default:try{nt=b.msgModel,nt.starty=m.getVerticalPos(),nt.sequenceIndex=H,nt.sequenceVisible=a.db.showSequenceNumbers();const k=await ye(0,nt);fe(b,nt,k,z,x,f,u),Q.push({messageModel:nt,lineStartY:k}),m.models.addMessage(nt)}catch(k){U.error("error while drawing message",k)}}[a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN,a.db.LINETYPE.SOLID,a.db.LINETYPE.DOTTED,a.db.LINETYPE.SOLID_CROSS,a.db.LINETYPE.DOTTED_CROSS,a.db.LINETYPE.SOLID_POINT,a.db.LINETYPE.DOTTED_POINT,a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(b.type)&&(H+=K),z++}U.debug("createdActors",f),U.debug("destroyedActors",u),await Vt(s,x,E,!1);for(const b of Q)await A0(s,b.messageModel,b.lineStartY,a);o.mirrorActors&&await Vt(s,x,E,!0),q.forEach(b=>C.drawBackgroundRect(s,b)),ue(s,x,E,o);for(const b of m.models.boxes)b.height=m.getVerticalPos()-b.y,m.insert(b.x,b.y,b.x+b.width,b.height),b.startx=b.x,b.starty=b.y,b.stopx=b.startx+b.width,b.stopy=b.starty+b.height,b.stroke="rgb(0,0,0, 0.5)",C.drawBox(s,b,o);B&&m.bumpVerticalPos(o.boxMargin);const F=be(s,x,E,g),{bounds:D}=m.getBounds();D.startx===void 0&&(D.startx=0),D.starty===void 0&&(D.starty=0),D.stopx===void 0&&(D.stopx=0),D.stopy===void 0&&(D.stopy=0);let X=D.stopy-D.starty;X<F.maxHeight&&(X=F.maxHeight);let j=X+2*o.diagramMarginY;o.mirrorActors&&(j=j-o.boxMargin+o.bottomMarginAdj);let G=D.stopx-D.startx;G<F.maxWidth&&(G=F.maxWidth);const tt=G+2*o.diagramMarginX;v&&s.append("text").text(v).attr("x",(D.stopx-D.startx)/2-2*o.diagramMarginX).attr("y",-25),Ce(s,j,tt,o.useMaxWidth);const M=v?40:0;s.attr("viewBox",D.startx-o.diagramMarginX+" -"+(o.diagramMarginY+M)+" "+tt+" "+(j+M)),U.debug("models:",m.models)},"draw");async function Ee(t,e,i){const a={};for(const n of e)if(t.get(n.to)&&t.get(n.from)){const r=t.get(n.to);if(n.placement===i.db.PLACEMENT.LEFTOF&&!r.prevActor||n.placement===i.db.PLACEMENT.RIGHTOF&&!r.nextActor)continue;const c=n.placement!==void 0,h=!c,g=c?yt(o):xt(o),s=n.wrap?Y.wrapLabel(n.message,o.width-2*o.wrapPadding,g):n.message,x=(lt(s)?await Et(n.message,rt()):Y.calculateTextDimensions(s,g)).width+2*o.wrapPadding;h&&n.from===r.nextActor?a[n.to]=P.getMax(a[n.to]||0,x):h&&n.from===r.prevActor?a[n.from]=P.getMax(a[n.from]||0,x):h&&n.from===n.to?(a[n.from]=P.getMax(a[n.from]||0,x/2),a[n.to]=P.getMax(a[n.to]||0,x/2)):n.placement===i.db.PLACEMENT.RIGHTOF?a[n.from]=P.getMax(a[n.from]||0,x):n.placement===i.db.PLACEMENT.LEFTOF?a[r.prevActor]=P.getMax(a[r.prevActor]||0,x):n.placement===i.db.PLACEMENT.OVER&&(r.prevActor&&(a[r.prevActor]=P.getMax(a[r.prevActor]||0,x/2)),r.nextActor&&(a[n.from]=P.getMax(a[n.from]||0,x/2)))}return U.debug("maxMessageWidthPerActor:",a),a}d(Ee,"getMaxMessageWidthPerActor");var k0=d(function(t){let e=0;const i=Yt(o);for(const a in t.links){const n=Y.calculateTextDimensions(a,i).width+2*o.wrapPadding+2*o.boxMargin;e<n&&(e=n)}return e},"getRequiredPopupWidth");async function we(t,e,i){let a=0;for(const r of t.keys()){const c=t.get(r);c.wrap&&(c.description=Y.wrapLabel(c.description,o.width-2*o.wrapPadding,Yt(o)));const h=lt(c.description)?await Et(c.description,rt()):Y.calculateTextDimensions(c.description,Yt(o));c.width=c.wrap?o.width:P.getMax(o.width,h.width+2*o.wrapPadding),c.height=c.wrap?P.getMax(h.height,o.height):o.height,a=P.getMax(a,c.height)}for(const r in e){const c=t.get(r);if(!c)continue;const h=t.get(c.nextActor);if(!h){const s=e[r]+o.actorMargin-c.width/2;c.margin=P.getMax(s,o.actorMargin);continue}const g=e[r]+o.actorMargin-c.width/2-h.width/2;c.margin=P.getMax(g,o.actorMargin)}let n=0;return i.forEach(r=>{const c=xt(o);let h=r.actorKeys.reduce((x,f)=>x+(t.get(f).width+(t.get(f).margin||0)),0);h-=2*o.boxTextMargin,r.wrap&&(r.name=Y.wrapLabel(r.name,h-2*o.wrapPadding,c));const g=Y.calculateTextDimensions(r.name,c);n=P.getMax(g.height,n);const s=P.getMax(h,g.width+2*o.wrapPadding);if(r.margin=o.boxTextMargin,h<s){const x=(s-h)/2;r.margin+=x}}),i.forEach(r=>r.textMaxHeight=n),P.getMax(a,o.height)}d(we,"calculateActorMargins");var M0=d(async function(t,e,i){const a=e.get(t.from),n=e.get(t.to),r=a.x,c=n.x,h=t.wrap&&t.message;let g=lt(t.message)?await Et(t.message,rt()):Y.calculateTextDimensions(h?Y.wrapLabel(t.message,o.width,yt(o)):t.message,yt(o));const s={width:h?o.width:P.getMax(o.width,g.width+2*o.noteMargin),height:0,startx:a.x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===i.db.PLACEMENT.RIGHTOF?(s.width=h?P.getMax(o.width,g.width):P.getMax(a.width/2+n.width/2,g.width+2*o.noteMargin),s.startx=r+(a.width+o.actorMargin)/2):t.placement===i.db.PLACEMENT.LEFTOF?(s.width=h?P.getMax(o.width,g.width+2*o.noteMargin):P.getMax(a.width/2+n.width/2,g.width+2*o.noteMargin),s.startx=r-s.width+(a.width-o.actorMargin)/2):t.to===t.from?(g=Y.calculateTextDimensions(h?Y.wrapLabel(t.message,P.getMax(o.width,a.width),yt(o)):t.message,yt(o)),s.width=h?P.getMax(o.width,a.width):P.getMax(a.width,o.width,g.width+2*o.noteMargin),s.startx=r+(a.width-s.width)/2):(s.width=Math.abs(r+a.width/2-(c+n.width/2))+o.actorMargin,s.startx=r<c?r+a.width/2-o.actorMargin/2:c+n.width/2-o.actorMargin/2),h&&(s.message=Y.wrapLabel(t.message,s.width-2*o.wrapPadding,yt(o))),U.debug(`NM:[${s.startx},${s.stopx},${s.starty},${s.stopy}:${s.width},${s.height}=${t.message}]`),s},"buildNoteModel"),O0=d(function(t,e,i){if(![i.db.LINETYPE.SOLID_OPEN,i.db.LINETYPE.DOTTED_OPEN,i.db.LINETYPE.SOLID,i.db.LINETYPE.DOTTED,i.db.LINETYPE.SOLID_CROSS,i.db.LINETYPE.DOTTED_CROSS,i.db.LINETYPE.SOLID_POINT,i.db.LINETYPE.DOTTED_POINT,i.db.LINETYPE.BIDIRECTIONAL_SOLID,i.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type))return{};const[a,n]=Qt(t.from,e),[r,c]=Qt(t.to,e),h=a<=r;let g=h?n:a,s=h?r:c;const x=Math.abs(r-c)>2,f=d(L=>h?-L:L,"adjustValue");t.from===t.to?s=g:(t.activate&&!x&&(s+=f(o.activationWidth/2-1)),[i.db.LINETYPE.SOLID_OPEN,i.db.LINETYPE.DOTTED_OPEN].includes(t.type)||(s+=f(3)),[i.db.LINETYPE.BIDIRECTIONAL_SOLID,i.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)&&(g-=f(3)));const u=[a,n,r,c],y=Math.abs(g-s);t.wrap&&t.message&&(t.message=Y.wrapLabel(t.message,P.getMax(y+2*o.wrapPadding,o.width),xt(o)));const E=Y.calculateTextDimensions(t.message,xt(o));return{width:P.getMax(t.wrap?0:E.width+2*o.wrapPadding,y+2*o.wrapPadding,o.width),height:0,startx:g,stopx:s,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,u),toBounds:Math.max.apply(null,u)}},"buildMessageModel"),D0=d(async function(t,e,i,a){const n={},r=[];let c,h,g;for(const s of t){switch(s.id=Y.random({length:10}),s.type){case a.db.LINETYPE.LOOP_START:case a.db.LINETYPE.ALT_START:case a.db.LINETYPE.OPT_START:case a.db.LINETYPE.PAR_START:case a.db.LINETYPE.PAR_OVER_START:case a.db.LINETYPE.CRITICAL_START:case a.db.LINETYPE.BREAK_START:r.push({id:s.id,msg:s.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case a.db.LINETYPE.ALT_ELSE:case a.db.LINETYPE.PAR_AND:case a.db.LINETYPE.CRITICAL_OPTION:s.message&&(c=r.pop(),n[c.id]=c,n[s.id]=c,r.push(c));break;case a.db.LINETYPE.LOOP_END:case a.db.LINETYPE.ALT_END:case a.db.LINETYPE.OPT_END:case a.db.LINETYPE.PAR_END:case a.db.LINETYPE.CRITICAL_END:case a.db.LINETYPE.BREAK_END:c=r.pop(),n[c.id]=c;break;case a.db.LINETYPE.ACTIVE_START:{const x=e.get(s.from?s.from:s.to.actor),f=Mt(s.from?s.from:s.to.actor).length,u=x.x+x.width/2+(f-1)*o.activationWidth/2,y={startx:u,stopx:u+o.activationWidth,actor:s.from,enabled:!0};m.activations.push(y)}break;case a.db.LINETYPE.ACTIVE_END:{const x=m.activations.map(f=>f.actor).lastIndexOf(s.from);m.activations.splice(x,1).splice(0,1)}}s.placement!==void 0?(h=await M0(s,e,a),s.noteModel=h,r.forEach(x=>{c=x,c.from=P.getMin(c.from,h.startx),c.to=P.getMax(c.to,h.startx+h.width),c.width=P.getMax(c.width,Math.abs(c.from-c.to))-o.labelBoxWidth})):(g=O0(s,e,a),s.msgModel=g,g.startx&&g.stopx&&r.length>0&&r.forEach(x=>{if(c=x,g.startx===g.stopx){const f=e.get(s.from),u=e.get(s.to);c.from=P.getMin(f.x-g.width/2,f.x-f.width/2,c.from),c.to=P.getMax(u.x+g.width/2,u.x+f.width/2,c.to),c.width=P.getMax(c.width,Math.abs(c.to-c.from))-o.labelBoxWidth}else c.from=P.getMin(g.startx,c.from),c.to=P.getMax(g.stopx,c.to),c.width=P.getMax(c.width,g.width)-o.labelBoxWidth}))}return m.activations=[],U.debug("Loop type widths:",n),n},"calculateLoopBounds"),Na={parser:Ve,db:Jt,renderer:{bounds:m,drawActors:Vt,drawActorsPopup:be,setConf:Te,draw:v0},styles:i0,init:d(({wrap:t})=>{Jt.setWrap(t)},"init")};export{Na as diagram};
