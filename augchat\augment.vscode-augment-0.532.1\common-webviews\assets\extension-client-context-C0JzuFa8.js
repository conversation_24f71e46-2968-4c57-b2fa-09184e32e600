var ie=Object.defineProperty;var ce=(n,e,s)=>e in n?ie(n,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[e]=s;var w=(n,e,s)=>ce(n,typeof e!="symbol"?e+"":e,s);import{f as L,b as g,x as Z,R as J,y as W,z,m as B,A as le,B as G,C,D as X,F as re,G as I,M as de,I as j,Q as V,o as b,O as D,Y as k,u as ue,L as q,J as he,K as Y,_ as ge,a1 as K,P as Q,w as me,ar as E,a as O,S as ve}from"./legacy-YP6Kq8lu.js";import{B as pe,a as fe}from"./partner-mcp-utils-Bk5-h15i.js";import{l as $,p as h,h as we,a as Ce}from"./SpinnerAugment-Dpcl1cXc.js";import{b as d}from"./host-BNehKqab.js";import{C as ye,h as P}from"./IconButtonAugment-CbpcmeFk.js";import{a as be}from"./input-C2nR_fsN.js";import{R as A}from"./chat-types-BfwvR7Kn.js";import{f as M}from"./index-CKSGO-M1.js";var xe=L('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor"></path></svg>');function Ue(n){var e=xe();g(n,e)}var Se=I("<div><!></div>");const Ne={Root:fe,IconButton:function(n,e){const s=$(e,["children","$$slots","$$events","$$legacy"]),t=$(s,["color","highContrast","disabled"]);Z(e,!1);const c=B(),v=B(),r=J(pe.CONTEXT_KEY);let u=h(e,"color",24,()=>{return a=r.color,l="neutral",typeof a=="string"&&["accent","neutral","error","success","warning","info"].includes(a)?a:l;var a,l}),o=h(e,"highContrast",8,!1),i=h(e,"disabled",8,!1),m=r.size===0?.5:r.size;W(()=>(C(c),C(v),G(t)),()=>{z(c,t.class),z(v,le(t,["class"]))}),X(),re();var x=Se(),_=b(x);const p=V(()=>`c-badge-icon-btn__base-btn ${C(c)}`);ye(_,we({get size(){return m},variant:"ghost",get color(){return u()},get highContrast(){return o()},get disabled(){return i()},get class(){return C(p)}},()=>C(v),{$$events:{click(a){d.call(this,e,a)},keyup(a){d.call(this,e,a)},keydown(a){d.call(this,e,a)},mousedown(a){d.call(this,e,a)},mouseover(a){d.call(this,e,a)},focus(a){d.call(this,e,a)},mouseleave(a){d.call(this,e,a)},blur(a){d.call(this,e,a)},contextmenu(a){d.call(this,e,a)}},children:(a,l)=>{var f=de(),S=j(f);Ce(S,e,"default",{},null),g(a,f)},$$slots:{default:!0}})),D(()=>k(x,1,ue(()=>`c-badge-icon-btn c-badge-icon-btn--${r.variant} c-badge-icon-btn--size-${m}`),"svelte-1im94um")),g(n,x),q()}};var ke=I("<span> </span> <span> </span>",1),$e=I('<label><!> <input type="checkbox" role="switch"/></label>');function Re(n,e){Z(e,!1);const s=B();let t=h(e,"checked",12,!1),c=h(e,"disabled",8,!1),v=h(e,"size",8,2),r=h(e,"ariaLabel",24,()=>{}),u=h(e,"onText",24,()=>{}),o=h(e,"offText",24,()=>{});W(()=>(G(u()),G(o())),()=>{z(s,u()||o())}),X();var i=$e();let m;var x=b(i),_=l=>{var f=ke(),S=j(f);let U;var se=b(S),N=Y(S,2);let R;var ne=b(N);D((ae,oe)=>{U=k(S,1,"c-toggle-text c-toggle-text--off svelte-xr5g0k",null,U,ae),Q(se,o()||""),R=k(N,1,"c-toggle-text c-toggle-text--on svelte-xr5g0k",null,R,oe),Q(ne,u()||"")},[()=>({visible:!t()&&o()}),()=>({visible:t()&&u()})],V),g(l,f)};he(x,l=>{C(s)&&l(_)});var p=Y(x,2);let a;D((l,f)=>{m=k(i,1,`c-toggle-track c-toggle-track-size--${v()??""}`,"svelte-xr5g0k",m,l),a=k(p,1,"c-toggle-input svelte-xr5g0k",null,a,f),p.disabled=c(),ge(p,"aria-label",r())},[()=>({checked:t(),disabled:c(),"has-text":C(s)}),()=>({disabled:c()})],V),be(p,t),K("keydown",p,function(l){c()||l.key!=="Enter"&&l.key!==" "||(l.preventDefault(),t(!t()))}),K("change",i,function(l){d.call(this,e,l)}),g(n,i),q()}function Ye(n){const{rules:e,workspaceGuidelinesContent:s,contextRules:t=[]}=n,c=e.filter(o=>o.type===A.ALWAYS_ATTACHED).reduce((o,i)=>o+i.content.length+i.path.length,0),v=e.filter(o=>o.type===A.AGENT_REQUESTED).reduce((o,i)=>{var m;return o+100+(((m=i.description)==null?void 0:m.length)??0)+i.path.length},0),r=c+e.filter(o=>o.type===A.MANUAL).filter(o=>t.some(i=>i.path===o.path)).reduce((o,i)=>o+i.content.length+i.path.length,0)+v+s.length,u=n.rulesAndGuidelinesLimit&&r>n.rulesAndGuidelinesLimit;return{totalCharacterCount:r,isOverLimit:u,warningMessage:u&&n.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${r} chars)
        exceeds the limit of ${n.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}const T={enabled:!1,volume:.5},Ke={enabled:!0},Le=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href;var ee=(n=>(n.AGENT_COMPLETE="agent-complete",n))(ee||{});const _e={"agent-complete":Le},y=class y{constructor(){w(this,"audioCache",new Map)}static getInstance(){return y._instance||(y._instance=new y),y._instance}retrieveAudioElement(e,s){let t=this.audioCache.get(e);return t?t.volume=s.volume:(t=new Audio,t.src=_e[e],t.volume=s.volume,t.preload="auto",t._isUnlocked=!1,this.audioCache.set(e,t)),t}async playSound(e,s){if(s.enabled)try{const t=this.retrieveAudioElement(e,s);t.currentTime=0,await t.play()}catch(t){if(t instanceof DOMException&&t.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",t)}}async unlockSoundForConfig(e){if(!e.enabled)return;const s=this.retrieveAudioElement("agent-complete",e);if(!s._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),s._isUnlocked=!0}catch(t){console.warn("Failed to unlock sound:",t)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};w(y,"_instance");let H=y;const F=H.getInstance();class Ee{constructor(e){w(this,"_soundSettings",me(T));w(this,"_isLoaded",!1);w(this,"dispose",()=>{F.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:M.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){E(this._soundSettings).enabled&&F.unlockSoundForConfig(E(this._soundSettings))}async playAgentComplete(){const e=E(this._soundSettings);await F.playSound(ee.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:M.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(T),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:M.updateSoundSettings,data:e}),this._soundSettings.update(s=>({...s,...e}))}catch(s){throw console.error("Failed to update sound settings:",s),s}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const s=Math.max(0,Math.min(1,e));await this.updateSettings({volume:s})}async resetToDefaults(){await this.updateSettings(T)}updateEnabled(e){this.setEnabled(e).catch(s=>{console.error("Failed to update enabled setting:",s)})}updateVolume(e){this.setVolume(e).catch(s=>{console.error("Failed to update volume setting:",s)})}}w(Ee,"key","soundModel");var Ae=L("<svg><!></svg>");function Qe(n,e){const s=$(e,["children","$$slots","$$events","$$legacy"]);var t=Ae();O(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var c=b(t);P(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',!0),g(n,t)}var Me=L('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg>');function Ze(n){var e=Me();g(n,e)}var Te=L("<svg><!></svg>");function Je(n,e){const s=$(e,["children","$$slots","$$events","$$legacy"]);var t=Te();O(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...s}));var c=b(t);P(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',!0),g(n,t)}var Fe=L("<svg><!></svg>");function We(n,e){const s=$(e,["children","$$slots","$$events","$$legacy"]);var t=Fe();O(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var c=b(t);P(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',!0),g(n,t)}const te="extensionClient";function Xe(n){ve(te,n)}function je(){const n=J(te);if(!n)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return n}export{Ne as B,Ue as C,Ke as D,Ze as G,Qe as P,Ee as S,We as T,Re as a,Je as b,Ye as c,je as g,Xe as s};
