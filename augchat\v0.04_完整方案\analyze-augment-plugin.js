#!/usr/bin/env node

/**
 * Augment插件源代码分析工具
 * 分析插件如何处理对话排序和数据读取
 */

const fs = require('fs');
const path = require('path');

// 分析插件的extension.js文件
function analyzeExtensionJS() {
    console.log('🔍 分析Augment插件的extension.js文件...\n');
    
    const extensionPath = path.join(__dirname, '../augment.vscode-augment-0.532.1/out/extension.js');
    
    if (!fs.existsSync(extensionPath)) {
        console.log('❌ 未找到extension.js文件');
        return null;
    }
    
    const content = fs.readFileSync(extensionPath, 'utf8');
    console.log(`📄 文件大小: ${(content.length / 1024 / 1024).toFixed(2)} MB`);
    console.log(`📊 总行数: ${content.split('\n').length}\n`);
    
    // 搜索关键的排序和对话相关代码
    const patterns = {
        'exchangeId相关': /exchangeId[^a-zA-Z0-9_][^;]{0,100}/g,
        'conversationId相关': /conversationId[^a-zA-Z0-9_][^;]{0,100}/g,
        '排序相关': /\.sort\([^)]{0,200}\)/g,
        'LevelDB相关': /level[^a-zA-Z0-9_][^;]{0,100}/g,
        '时间戳相关': /timestamp[^a-zA-Z0-9_][^;]{0,100}/g,
        '对话相关': /conversation[^a-zA-Z0-9_][^;]{0,100}/g,
        '交换相关': /exchange[^a-zA-Z0-9_][^;]{0,100}/g
    };
    
    const results = {};
    
    for (const [name, pattern] of Object.entries(patterns)) {
        const matches = content.match(pattern) || [];
        results[name] = matches.slice(0, 10); // 限制每个模式最多10个匹配
        console.log(`🔍 ${name}: 找到 ${matches.length} 个匹配`);
        
        if (matches.length > 0) {
            console.log('   样本:');
            matches.slice(0, 3).forEach((match, index) => {
                // 清理匹配结果，移除换行和多余空格
                const cleaned = match.replace(/\s+/g, ' ').trim();
                console.log(`     ${index + 1}. ${cleaned.substring(0, 80)}...`);
            });
        }
        console.log('');
    }
    
    return results;
}

// 搜索特定的函数或方法
function searchSpecificFunctions() {
    console.log('🔍 搜索特定的函数和方法...\n');
    
    const extensionPath = path.join(__dirname, '../augment.vscode-augment-0.532.1/out/extension.js');
    const content = fs.readFileSync(extensionPath, 'utf8');
    
    // 搜索可能的排序函数
    const functionPatterns = {
        '排序函数': /function[^{]*sort[^{]*\{[^}]{0,500}\}/gi,
        '比较函数': /function[^{]*compar[^{]*\{[^}]{0,300}\}/gi,
        '对话加载': /function[^{]*conversation[^{]*\{[^}]{0,500}\}/gi,
        '数据库查询': /function[^{]*query[^{]*\{[^}]{0,300}\}/gi,
        '消息排序': /function[^{]*message[^{]*sort[^{]*\{[^}]{0,300}\}/gi
    };
    
    for (const [name, pattern] of Object.entries(functionPatterns)) {
        const matches = content.match(pattern) || [];
        console.log(`🔍 ${name}: 找到 ${matches.length} 个匹配`);
        
        if (matches.length > 0) {
            matches.slice(0, 2).forEach((match, index) => {
                console.log(`\n   ${index + 1}. ${match.substring(0, 200)}...`);
            });
        }
        console.log('');
    }
}

// 分析数据库相关的代码
function analyzeDatabaseCode() {
    console.log('🔍 分析数据库相关代码...\n');
    
    const extensionPath = path.join(__dirname, '../augment.vscode-augment-0.532.1/out/extension.js');
    const content = fs.readFileSync(extensionPath, 'utf8');
    
    // 搜索数据库操作相关的代码
    const dbPatterns = {
        'LevelDB操作': /level[^a-zA-Z0-9_][^.;]{0,150}[.;]/g,
        '键值查询': /key[^a-zA-Z0-9_][^.;]{0,100}[.;]/g,
        '迭代器': /iterator[^a-zA-Z0-9_][^.;]{0,100}[.;]/g,
        '数据库打开': /open[^a-zA-Z0-9_][^.;]{0,100}[.;]/g
    };
    
    for (const [name, pattern] of Object.entries(dbPatterns)) {
        const matches = content.match(pattern) || [];
        console.log(`🔍 ${name}: 找到 ${matches.length} 个匹配`);
        
        if (matches.length > 0) {
            matches.slice(0, 3).forEach((match, index) => {
                const cleaned = match.replace(/\s+/g, ' ').trim();
                console.log(`     ${index + 1}. ${cleaned}`);
            });
        }
        console.log('');
    }
}

// 提取可能的排序逻辑
function extractSortingLogic() {
    console.log('🔍 提取排序逻辑...\n');
    
    const extensionPath = path.join(__dirname, '../augment.vscode-augment-0.532.1/out/extension.js');
    const content = fs.readFileSync(extensionPath, 'utf8');
    
    // 查找包含sort的代码段
    const sortMatches = content.match(/[^.]{0,200}\.sort\([^)]{0,300}\)[^.]{0,200}/g) || [];
    
    console.log(`📊 找到 ${sortMatches.length} 个排序操作`);
    
    if (sortMatches.length > 0) {
        console.log('\n🔍 排序逻辑样本:');
        sortMatches.slice(0, 5).forEach((match, index) => {
            const cleaned = match.replace(/\s+/g, ' ').trim();
            console.log(`\n${index + 1}. ${cleaned}`);
            
            // 尝试提取比较函数
            const compareMatch = match.match(/\.sort\(([^)]+)\)/);
            if (compareMatch) {
                console.log(`   比较函数: ${compareMatch[1]}`);
            }
        });
    }
    
    // 查找可能的时间相关排序
    const timeSort = content.match(/[^.]{0,100}(time|date|timestamp|created|updated)[^.]{0,100}\.sort[^.]{0,200}/gi) || [];
    
    if (timeSort.length > 0) {
        console.log('\n🕒 时间相关排序:');
        timeSort.forEach((match, index) => {
            const cleaned = match.replace(/\s+/g, ' ').trim();
            console.log(`${index + 1}. ${cleaned}`);
        });
    }
}

// 分析webview文件
function analyzeWebviewFiles() {
    console.log('🔍 分析webview文件...\n');
    
    const webviewDir = path.join(__dirname, '../augment.vscode-augment-0.532.1/common-webviews');
    
    if (!fs.existsSync(webviewDir)) {
        console.log('❌ 未找到webview目录');
        return;
    }
    
    const files = fs.readdirSync(webviewDir).filter(f => f.endsWith('.html'));
    
    for (const file of files) {
        const filePath = path.join(webviewDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        console.log(`📄 分析文件: ${file}`);
        
        // 搜索JavaScript代码中的排序逻辑
        const scriptMatches = content.match(/<script[^>]*>([\s\S]*?)<\/script>/gi) || [];
        
        for (const script of scriptMatches) {
            const jsContent = script.replace(/<\/?script[^>]*>/gi, '');
            
            // 搜索排序相关代码
            const sortMatches = jsContent.match(/[^.]{0,100}\.sort[^.]{0,200}/g) || [];
            const conversationMatches = jsContent.match(/conversation[^a-zA-Z0-9_][^;]{0,100}/g) || [];
            const exchangeMatches = jsContent.match(/exchange[^a-zA-Z0-9_][^;]{0,100}/g) || [];
            
            if (sortMatches.length > 0 || conversationMatches.length > 0 || exchangeMatches.length > 0) {
                console.log(`   🔍 找到相关代码:`);
                console.log(`     排序: ${sortMatches.length} 个`);
                console.log(`     对话: ${conversationMatches.length} 个`);
                console.log(`     交换: ${exchangeMatches.length} 个`);
                
                if (sortMatches.length > 0) {
                    sortMatches.slice(0, 2).forEach((match, index) => {
                        const cleaned = match.replace(/\s+/g, ' ').trim();
                        console.log(`       排序${index + 1}: ${cleaned}`);
                    });
                }
            }
        }
        console.log('');
    }
}

// 主函数
function main() {
    console.log('🔧 Augment插件源代码分析工具');
    console.log('================================\n');
    
    try {
        // 1. 分析主扩展文件
        const extensionResults = analyzeExtensionJS();
        
        // 2. 搜索特定函数
        searchSpecificFunctions();
        
        // 3. 分析数据库代码
        analyzeDatabaseCode();
        
        // 4. 提取排序逻辑
        extractSortingLogic();
        
        // 5. 分析webview文件
        analyzeWebviewFiles();
        
        console.log('\n💡 分析总结:');
        console.log('================================');
        console.log('1. 检查上述输出中的排序逻辑');
        console.log('2. 重点关注exchangeId和conversationId的使用');
        console.log('3. 查找时间戳相关的排序方法');
        console.log('4. 分析数据库查询和迭代的顺序');
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
    }
}

// 运行分析
if (require.main === module) {
    main();
}

module.exports = { analyzeExtensionJS, extractSortingLogic };
