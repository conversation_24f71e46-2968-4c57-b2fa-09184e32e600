import{c as V,E as W,l as X,p as Z,r as aa,a2 as sa,a3 as na,a4 as ea,a5 as la,a6 as ta,a7 as G,x as ra,y as m,D as oa,F as ia,M as J,I as E,b as u,L as ca,a as pa,a1 as va,G as w,J as $,u as K,K as k,O as L,P as M,C as i,m as h,o as g,Q as fa,Y as ua,z as _,B as N}from"./legacy-YP6Kq8lu.js";import{s as da,p as f,T as ma,a as O}from"./SpinnerAugment-Dpcl1cXc.js";import{n as ha,g as ga,a as _a}from"./focusTrapStack-CaEmYw0i.js";function ba(b,s,d,r,x,I){var c,p,l,t=null,z=b;V(()=>{const e=s()||null;var o=d||e==="svg"?na:null;e!==c&&(l&&(e===null?Z(l,()=>{l=null,p=null}):e===p?aa(l):(sa(l),G(!1))),e&&e!==p&&(l=X(()=>{if(t=o?document.createElementNS(o,e):document.createElement(e),ea(t,t),r){var C=t.appendChild(la());r(t,C)}ta.nodes_end=t,z.before(t)})),(c=e)&&(p=c),G(!0))},W)}var xa=w('<div><div class="c-filespan__dir-text svelte-9pfhnp"> </div></div>'),Ia=w('<span class="right-icons svelte-9pfhnp"><!></span>'),za=w('<!> <span class="c-filespan__filename svelte-9pfhnp"> </span> <!> <!>',1);function wa(b,s){const d=da(s);ra(s,!1);const r=h(),x=h(),I=h(),c=h();let p=f(s,"class",8,""),l=f(s,"filepath",8),t=f(s,"size",8,1),z=f(s,"nopath",8,!1),e=f(s,"growname",8,!0),o=f(s,"onClick",24,()=>{});m(()=>N(l()),()=>{_(r,ha(l()))}),m(()=>i(r),()=>{_(x,ga(i(r)))}),m(()=>i(r),()=>{_(I,_a(i(r)))}),m(()=>N(o()),()=>{_(c,o()?"button":"div")}),oa(),ia(),ma(b,{get size(){return t()},children:(C,Ca)=>{var y=J();ba(E(y),()=>i(c),!1,(F,P)=>{pa(F,()=>({class:`c-filespan ${p()}`,role:o()?"button":"",tabindex:"0"}),void 0,"svelte-9pfhnp"),va("click",F,function(...a){var n;(n=o())==null||n.apply(this,a)});var S=za(),Y=E(S),Q=a=>{var n=J(),v=E(n);O(v,s,"leftIcon",{},null),u(a,n)};$(Y,a=>{K(()=>d.leftIcon)&&a(Q)});var B=k(Y,2),T=g(B),D=k(B,2),j=a=>{var n=xa();let v;var H=g(n),R=g(H);L(U=>{v=ua(n,1,"c-filespan__dir svelte-9pfhnp",null,v,U),M(R,i(I))},[()=>({growname:e()})],fa),u(a,n)};$(D,a=>{z()||a(j)});var A=k(D,2),q=a=>{var n=Ia(),v=g(n);O(v,s,"rightIcon",{},null),u(a,n)};$(A,a=>{K(()=>d.rightIcon)&&a(q)}),L(()=>M(T,i(x))),u(P,S)}),u(C,y)},$$slots:{default:!0}}),ca()}export{wa as F,ba as e};
