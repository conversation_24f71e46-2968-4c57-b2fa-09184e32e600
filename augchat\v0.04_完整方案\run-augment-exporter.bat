@echo off
chcp 65001 >nul
title Augment聊天记录完整导出器 v3.0

echo.
echo ========================================
echo   🚀 Augment聊天记录完整导出器 v3.0
echo ========================================
echo.
echo 📋 功能特点:
echo   ✅ 自动发现所有VSCode工作区
echo   ✅ 智能提取聊天记录 (LevelDB + 字符串)
echo   ✅ 生成有意义的文件名
echo   ✅ 支持增量更新
echo   ✅ 统一输出目录管理
echo.

:MENU
echo 请选择操作:
echo.
echo [1] 🚀 运行完整导出 (推荐)
echo [2] 📂 打开输出目录
echo [3] 📋 查看导出索引
echo [4] 🔧 检查环境依赖
echo [5] 📖 查看使用说明
echo [0] 退出
echo.
set /p choice="请输入选择 (0-5): "

if "%choice%"=="1" goto RUN_EXPORT
if "%choice%"=="2" goto OPEN_OUTPUT
if "%choice%"=="3" goto VIEW_INDEX
if "%choice%"=="4" goto CHECK_DEPS
if "%choice%"=="5" goto SHOW_HELP
if "%choice%"=="0" goto EXIT
echo 无效选择，请重新输入。
goto MENU

:RUN_EXPORT
echo.
echo 🚀 开始运行Augment聊天记录导出器...
echo.
echo 💡 提示: 为获得最佳效果，建议先关闭VSCode
echo.
set /p confirm="是否继续? (Y/N): "
if /i not "%confirm%"=="Y" goto MENU

echo.
echo 📊 正在执行导出...
echo =====================================
node augment-chat-exporter.js

if %errorlevel% equ 0 (
    echo.
    echo 🎉 导出成功完成！
    echo.
    set /p open="是否打开输出目录? (Y/N): "
    if /i "%open%"=="Y" start "" "conversations_export"
) else (
    echo.
    echo ❌ 导出过程中出现错误
    echo 💡 请检查错误信息并重试
)

echo.
pause
goto MENU

:OPEN_OUTPUT
echo.
echo 📂 打开输出目录...

REM 查找最新的导出目录
for /f "delims=" %%i in ('dir /b /ad conversations_export_* 2^>nul ^| sort /r') do (
    set "latest_dir=%%i"
    goto found_output_dir
)

:found_output_dir
if defined latest_dir (
    start "" "%latest_dir%"
    echo ✅ 已打开最新输出目录: %latest_dir%
) else (
    echo ❌ 输出目录不存在，请先运行导出
)
echo.
pause
goto MENU

:VIEW_INDEX
echo.
echo 📋 查看导出索引...

REM 查找最新的导出目录
for /f "delims=" %%i in ('dir /b /ad conversations_export_* 2^>nul ^| sort /r') do (
    set "latest_dir=%%i"
    goto found_index_dir
)

:found_index_dir
if defined latest_dir (
    if exist "%latest_dir%\README.md" (
        start "" "%latest_dir%\README.md"
        echo ✅ 已打开最新索引文件: %latest_dir%\README.md
    ) else (
        echo ❌ 索引文件不存在
    )
) else (
    echo ❌ 输出目录不存在，请先运行导出
)
echo.
pause
goto MENU

:CHECK_DEPS
echo.
echo 🔧 检查环境依赖...
echo =====================================
echo.

echo 📋 检查Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js: 
    node --version
) else (
    echo ❌ Node.js 未安装或不在PATH中
    echo 💡 请从 https://nodejs.org 下载安装Node.js
)

echo.
echo 📋 检查npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm: 
    npm --version
) else (
    echo ❌ npm 未安装
)

echo.
echo 📋 检查level库...
if exist "node_modules\level" (
    echo ✅ level库已安装
) else (
    echo ❌ level库未安装
    echo 💡 正在尝试安装...
    npm install level
    if %errorlevel% equ 0 (
        echo ✅ level库安装成功
    ) else (
        echo ❌ level库安装失败
    )
)

echo.
echo 📋 检查脚本文件...
if exist "augment-chat-exporter.js" (
    echo ✅ 主脚本文件存在
) else (
    echo ❌ 主脚本文件不存在
)

echo.
echo 📋 检查VSCode工作区...
set workspace_found=0
if exist "%USERPROFILE%\AppData\Roaming\Code\User\workspaceStorage" (
    echo ✅ 发现VSCode工作区目录
    set workspace_found=1
)
if exist "%USERPROFILE%\.vscode\User\workspaceStorage" (
    echo ✅ 发现VSCode工作区目录 (备用位置)
    set workspace_found=1
)

if %workspace_found% equ 0 (
    echo ❌ 未发现VSCode工作区目录
    echo 💡 请确保已安装VSCode并使用过Augment插件
)

echo.
echo =====================================
echo 🔧 环境检查完成
echo.
pause
goto MENU

:SHOW_HELP
echo.
echo 📖 使用说明
echo =====================================
echo.
echo 🎯 功能介绍:
echo   本工具可以自动发现并导出所有VSCode工作区中的
echo   Augment聊天记录，生成易于阅读的Markdown文件。
echo.
echo 🚀 使用步骤:
echo   1. 确保已安装Node.js和level库
echo   2. 建议关闭VSCode以避免数据库锁定
echo   3. 选择"运行完整导出"
echo   4. 等待处理完成
echo   5. 查看conversations_export目录中的结果
echo.
echo 📁 输出结构:
echo   conversations_export/
echo   ├── README.md                    # 索引文件
echo   ├── workspace1_对话标题_12345678.md
echo   ├── workspace2_另一个对话_87654321.md
echo   └── ...
echo.
echo 🔄 增量更新:
echo   重新运行时，工具会自动检测变化，只更新
echo   有修改的对话，跳过未变化的文件。
echo.
echo 📊 文件命名规则:
echo   [工作区名称]_[对话标题]_[对话ID前8位].md
echo   对话标题自动从第一条用户消息中提取。
echo.
echo 🛠️  技术特点:
echo   - 双重提取方法 (LevelDB + 字符串)
echo   - 自动工作区发现
echo   - 智能文件命名
echo   - 增量更新支持
echo   - 完整的元数据保留
echo.
echo 💡 故障排除:
echo   - 如果提示权限错误，请关闭VSCode
echo   - 如果没有发现对话，检查Augment插件是否正常工作
echo   - 如果导出失败，检查磁盘空间和权限
echo.
echo =====================================
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 感谢使用Augment聊天记录导出器！
echo.
exit /b 0
