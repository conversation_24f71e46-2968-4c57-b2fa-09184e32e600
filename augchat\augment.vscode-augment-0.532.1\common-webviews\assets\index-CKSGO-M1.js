var ki=Object.defineProperty;var Ri=(e,t,n)=>t in e?ki(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var f=(e,t,n)=>Ri(e,typeof t!="symbol"?t+"":t,n);import{a as Ci}from"./async-messaging-gS_K9w3p.js";import{W as S,S as Ai,b as we}from"./host-BNehKqab.js";import{E as Z,S as be,R as me,P as oe,C as Oi}from"./chat-types-BfwvR7Kn.js";import{n as Mi,F as fs}from"./focusTrapStack-CaEmYw0i.js";import{g as Vr,p as R,a as K,e as ye,b as Ae,f as Pi,s as to,l as B,h as Ge,T as no,n as gs,i as ro}from"./SpinnerAugment-Dpcl1cXc.js";import{w as en,f as so,b as A,aP as tr,x as se,S as Gr,R as Y,y as ve,D as tt,F as ue,a1 as fe,a9 as xi,G as H,o as Q,O as nt,Y as rt,L as ae,C as x,m as _e,B as xe,z as Ee,I as de,J as ft,u as je,K as gt,M as ke,am as ys,Q as tn,A as ao,a as oo,aQ as bs,cT as Li,T as Di,aa as Fi,aK as Ui,$ as $i,_ as Bi}from"./legacy-YP6Kq8lu.js";import{a as Vi,c as Gi,_ as ji,i as jr}from"./isObjectLike-BNqj-rl6.js";import{a as nr,B as qi,h as Yi,s as Ki}from"./IconButtonAugment-CbpcmeFk.js";import{c as Hi,e as qt,f as Wi,C as Xi,a as Yt,R as zi,b as Xe,g as vs}from"./CardAugment-YBzgmAzG.js";import{b as Ji}from"./input-C2nR_fsN.js";import{B as Zi}from"./BaseTextInput-Br9yLRnx.js";function qr(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var b;function Qi(){let e=0,t=0;for(let r=0;r<28;r+=7){let a=this.buf[this.pos++];if(e|=(127&a)<<r,!(128&a))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let r=3;r<=31;r+=7){let a=this.buf[this.pos++];if(t|=(127&a)<<r,!(128&a))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function Tn(e,t,n){for(let s=0;s<28;s+=7){const o=e>>>s,i=!(!(o>>>7)&&t==0),c=255&(i?128|o:o);if(n.push(c),!i)return}const r=e>>>28&15|(7&t)<<4,a=!!(t>>3);if(n.push(255&(a?128|r:r)),a){for(let s=3;s<31;s+=7){const o=t>>>s,i=!!(o>>>7),c=255&(i?128|o:o);if(n.push(c),!i)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(b||(b={}));const Kt=4294967296;function _s(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let r=0,a=0;function s(o,i){const c=Number(e.slice(o,i));a*=n,r=r*n+c,r>=Kt&&(a+=r/Kt|0,r%=Kt)}return s(-24,-18),s(-18,-12),s(-12,-6),s(-6),t?io(r,a):Yr(r,a)}function Es(e,t){if({lo:e,hi:t}=function(c,l){return{lo:c>>>0,hi:l>>>0}}(e,t),t<=2097151)return String(Kt*t+e);const n=16777215&(e>>>24|t<<8),r=t>>16&65535;let a=(16777215&e)+6777216*n+6710656*r,s=n+8147497*r,o=2*r;const i=1e7;return a>=i&&(s+=Math.floor(a/i),a%=i),s>=i&&(o+=Math.floor(s/i),s%=i),o.toString()+Ss(s)+Ss(a)}function Yr(e,t){return{lo:0|e,hi:0|t}}function io(e,t){return t=~t,e?e=1+~e:t+=1,Yr(e,t)}const Ss=e=>{const t=String(e);return"0000000".slice(t.length)+t};function Ts(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function el(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var ws={};const P=tl();function tl(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof ws!="object"||ws.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),r=BigInt("0"),a=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(s){const o=typeof s=="bigint"?s:BigInt(s);if(o>n||o<t)throw new Error(`invalid int64: ${s}`);return o},uParse(s){const o=typeof s=="bigint"?s:BigInt(s);if(o>a||o<r)throw new Error(`invalid uint64: ${s}`);return o},enc(s){return e.setBigInt64(0,this.parse(s),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(s){return e.setBigInt64(0,this.uParse(s),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(s,o)=>(e.setInt32(0,s,!0),e.setInt32(4,o,!0),e.getBigInt64(0,!0)),uDec:(s,o)=>(e.setInt32(0,s,!0),e.setInt32(4,o,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),Ns(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),Is(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),Ns(t),_s(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),Is(t),_s(t)),dec:(t,n)=>function(r,a){let s=Yr(r,a);const o=2147483648&s.hi;o&&(s=io(s.lo,s.hi));const i=Es(s.lo,s.hi);return o?"-"+i:i}(t,n),uDec:(t,n)=>Es(t,n)}}function Ns(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function Is(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function qe(e,t){switch(e){case b.STRING:return"";case b.BOOL:return!1;case b.DOUBLE:case b.FLOAT:return 0;case b.INT64:case b.UINT64:case b.SFIXED64:case b.FIXED64:case b.SINT64:return t?"0":P.zero;case b.BYTES:return new Uint8Array(0);default:return 0}}const Re=Symbol.for("reflect unsafe local");function lo(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(r=>r.localName===n)}function nl(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(r,a){switch(r){case b.BOOL:return a===!1;case b.STRING:return a==="";case b.BYTES:return a instanceof Uint8Array&&!a.byteLength;default:return a==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function yt(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function uo(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function co(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function Fe(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function Kr(e,t){var n,r,a,s;if(Fe(e)&&Re in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.listKind==i.listKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((r=i.message)===null||r===void 0?void 0:r.typeName)&&((a=o.enum)===null||a===void 0?void 0:a.typeName)===((s=i.enum)===null||s===void 0?void 0:s.typeName)}return!0}return!1}function Hr(e,t){var n,r,a,s;if(Fe(e)&&Re in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.mapKey===i.mapKey&&o.mapKind==i.mapKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((r=i.message)===null||r===void 0?void 0:r.typeName)&&((a=o.enum)===null||a===void 0?void 0:a.typeName)===((s=i.enum)===null||s===void 0?void 0:s.typeName)}return!0}return!1}function Wr(e,t){return Fe(e)&&Re in e&&"desc"in e&&Fe(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function At(e){const t=e.fields[0];return po(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function po(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const rl=999,sl=998,Pt=2;function pe(e,t){if(qr(t,e))return t;const n=function(r){let a;if(function(s){switch(s.file.edition){case rl:return!1;case sl:return!0;default:return s.fields.some(o=>o.presence!=Pt&&o.fieldKind!="message"&&!o.oneof)}}(r)){const s=Rs.get(r);let o,i;if(s)({prototype:o,members:i}=s);else{o={},i=new Set;for(const c of r.members)c.kind!="oneof"&&(c.fieldKind!="scalar"&&c.fieldKind!="enum"||c.presence!=Pt&&(i.add(c),o[c.localName]=wn(c)));Rs.set(r,{prototype:o,members:i})}a=Object.create(o),a.$typeName=r.typeName;for(const c of r.members)if(!i.has(c)){if(c.kind=="field"&&(c.fieldKind=="message"||(c.fieldKind=="scalar"||c.fieldKind=="enum")&&c.presence!=Pt))continue;a[c.localName]=wn(c)}}else{a={$typeName:r.typeName};for(const s of r.members)s.kind!="oneof"&&s.presence!=Pt||(a[s.localName]=wn(s))}return a}(e);return t!==void 0&&function(r,a,s){for(const o of r.members){let i,c=s[o.localName];if(c!=null){if(o.kind=="oneof"){const l=lo(s,o);if(!l)continue;i=l,c=uo(s,l)}else i=o;switch(i.fieldKind){case"message":c=Xr(i,c);break;case"scalar":c=ho(i,c);break;case"list":c=ol(i,c);break;case"map":c=al(i,c)}co(a,i,c)}}}(e,n,t),n}function ho(e,t){return e.scalar==b.BYTES?zr(t):t}function al(e,t){if(Fe(t)){if(e.scalar==b.BYTES)return ks(t,zr);if(e.mapKind=="message")return ks(t,n=>Xr(e,n))}return t}function ol(e,t){if(Array.isArray(t)){if(e.scalar==b.BYTES)return t.map(zr);if(e.listKind=="message")return t.map(n=>Xr(e,n))}return t}function Xr(e,t){if(e.fieldKind=="message"&&!e.oneof&&At(e.message))return ho(e.message.fields[0],t);if(Fe(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!qr(t,e.message))return pe(e.message,t)}return t}function zr(e){return Array.isArray(e)?new Uint8Array(e):e}function ks(e,t){const n={};for(const r of Object.entries(e))n[r[0]]=t(r[1]);return n}const il=Symbol(),Rs=new WeakMap;function wn(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return il;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?qe(e.scalar,e.longAsString):e.enum.values[0].number}const ll=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class X extends Error{constructor(t,n,r="FieldValueInvalidError"){super(n),this.name=r,this.field=()=>t}}const Nn=Symbol.for("@bufbuild/protobuf/text-encoding");function Jr(){if(globalThis[Nn]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[Nn]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[Nn]}var L;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})(L||(L={}));const mo=34028234663852886e22,fo=-34028234663852886e22,go=4294967295,yo=2147483647,bo=-2147483648;class Zr{constructor(t=Jr().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let a=0;a<this.chunks.length;a++)t+=this.chunks[a].length;let n=new Uint8Array(t),r=0;for(let a=0;a<this.chunks.length;a++)n.set(this.chunks[a],r),r+=this.chunks[a].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(Cs(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return In(t),Ts(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(r){if(typeof r=="string"){const a=r;if(r=Number(r),Number.isNaN(r)&&a!=="NaN")throw new Error("invalid float32: "+a)}else if(typeof r!="number")throw new Error("invalid float32: "+typeof r);if(Number.isFinite(r)&&(r>mo||r<fo))throw new Error("invalid float32: "+r)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){Cs(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){In(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return In(t),Ts(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),a=P.enc(t);return r.setInt32(0,a.lo,!0),r.setInt32(4,a.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),a=P.uEnc(t);return r.setInt32(0,a.lo,!0),r.setInt32(4,a.hi,!0),this.raw(n)}int64(t){let n=P.enc(t);return Tn(n.lo,n.hi,this.buf),this}sint64(t){const n=P.enc(t),r=n.hi>>31;return Tn(n.lo<<1^r,(n.hi<<1|n.lo>>>31)^r,this.buf),this}uint64(t){const n=P.uEnc(t);return Tn(n.lo,n.hi,this.buf),this}}class Qr{constructor(t,n=Jr().decodeUtf8){this.decodeUtf8=n,this.varint64=Qi,this.uint32=el,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,r=7&t;if(n<=0||r<0||r>5)throw new Error("illegal tag: field no "+n+" wire type "+r);return[n,r]}skip(t,n){let r=this.pos;switch(t){case L.Varint:for(;128&this.buf[this.pos++];);break;case L.Bit64:this.pos+=4;case L.Bit32:this.pos+=4;break;case L.LengthDelimited:let a=this.uint32();this.pos+=a;break;case L.StartGroup:for(;;){const[s,o]=this.tag();if(o===L.EndGroup){if(n!==void 0&&s!==n)throw new Error("invalid end group tag");break}this.skip(o,s)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(r,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return P.dec(...this.varint64())}uint64(){return P.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),r=-(1&t);return t=(t>>>1|(1&n)<<31)^r,n=n>>>1^r,P.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return P.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return P.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function In(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>yo||e<bo)throw new Error("invalid int32: "+e)}function Cs(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>go||e<0)throw new Error("invalid uint32: "+e)}function $e(e,t){const n=e.fieldKind=="list"?Kr(t,e):e.fieldKind=="map"?Hr(t,e):es(e,t);if(n===!0)return;let r;switch(e.fieldKind){case"list":r=`expected ${Eo(e)}, got ${D(t)}`;break;case"map":r=`expected ${So(e)}, got ${D(t)}`;break;default:r=nn(e,t,n)}return new X(e,r)}function As(e,t,n){const r=es(e,n);if(r!==!0)return new X(e,`list item #${t+1}: ${nn(e,n,r)}`)}function es(e,t){return e.scalar!==void 0?vo(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):Wr(t,e.message)}function vo(e,t){switch(t){case b.DOUBLE:return typeof e=="number";case b.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>mo||e<fo)||`${e.toFixed()} out of range`);case b.INT32:case b.SFIXED32:case b.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>yo||e<bo)||`${e.toFixed()} out of range`);case b.FIXED32:case b.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>go||e<0)||`${e.toFixed()} out of range`);case b.BOOL:return typeof e=="boolean";case b.STRING:return typeof e=="string"&&(Jr().checkUtf8(e)||"invalid UTF8");case b.BYTES:return e instanceof Uint8Array;case b.INT64:case b.SFIXED64:case b.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return P.parse(e),!0}catch{return`${e} out of range`}return!1;case b.FIXED64:case b.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return P.uParse(e),!0}catch{return`${e} out of range`}return!1}}function nn(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${D(t)}`,e.scalar!==void 0?`expected ${function(r){switch(r){case b.STRING:return"string";case b.BOOL:return"boolean";case b.INT64:case b.SINT64:case b.SFIXED64:return"bigint (int64)";case b.UINT64:case b.FIXED64:return"bigint (uint64)";case b.BYTES:return"Uint8Array";case b.DOUBLE:return"number (float64)";case b.FLOAT:return"number (float32)";case b.FIXED32:case b.UINT32:return"number (uint32)";case b.INT32:case b.SFIXED32:case b.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${_o(e.message)}`+n}function D(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:Kr(e)?Eo(e.field()):Hr(e)?So(e.field()):Wr(e)?_o(e.desc):qr(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function _o(e){return`ReflectMessage (${e.typeName})`}function Eo(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${b[e.scalar]})`}}function So(e){switch(e.mapKind){case"message":return`ReflectMap (${b[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${b[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${b[e.mapKey]}, ${b[e.scalar]})`}}function ie(e,t,n=!0){return new To(e,t,n)}class To{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,r)=>n.number-r.number)}constructor(t,n,r=!0){this.lists=new Map,this.maps=new Map,this.check=r,this.desc=t,this.message=this[Re]=n??pe(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return lt(this.message,t),lo(this.message,t)}isSet(t){return lt(this.message,t),nl(this.message,t)}clear(t){lt(this.message,t),function(n,r){const a=r.localName;if(r.oneof){const s=r.oneof.localName;n[s].case===a&&(n[s]={case:void 0})}else if(r.presence!=2)delete n[a];else switch(r.fieldKind){case"map":n[a]={};break;case"list":n[a]=[];break;case"enum":n[a]=r.enum.values[0].number;break;case"scalar":n[a]=qe(r.scalar,r.longAsString)}}(this.message,t)}get(t){lt(this.message,t);const n=uo(this.message,t);switch(t.fieldKind){case"list":let r=this.lists.get(t);return r&&r[Re]===n||this.lists.set(t,r=new ul(t,n,this.check)),r;case"map":let a=this.maps.get(t);return a&&a[Re]===n||this.maps.set(t,a=new cl(t,n,this.check)),a;case"message":return ns(t,n,this.check);case"scalar":return n===void 0?qe(t.scalar,!1):rs(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(lt(this.message,t),this.check){const a=$e(t,n);if(a)throw a}let r;r=t.fieldKind=="message"?ts(t,n):Hr(n)||Kr(n)?n[Re]:ss(t,n),co(this.message,t,r)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function lt(e,t){if(t.parent.typeName!==e.$typeName)throw new X(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class ul{field(){return this._field}get size(){return this._arr.length}constructor(t,n,r){this._field=t,this._arr=this[Re]=n,this.check=r}get(t){const n=this._arr[t];return n===void 0?void 0:kn(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new X(this._field,`list item #${t+1}: out of range`);if(this.check){const r=As(this._field,t,n);if(r)throw r}this._arr[t]=Os(this._field,n)}add(t){if(this.check){const n=As(this._field,this._arr.length,t);if(n)throw n}this._arr.push(Os(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield kn(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,kn(this._field,this._arr[t],this.check)]}}class cl{constructor(t,n,r=!0){this.obj=this[Re]=n??{},this.check=r,this._field=t}field(){return this._field}set(t,n){if(this.check){const r=function(a,s,o){const i=vo(s,a.mapKey);if(i!==!0)return new X(a,`invalid map key: ${nn({scalar:a.mapKey},s,i)}`);const c=es(a,o);return c!==!0?new X(a,`map entry ${D(s)}: ${nn(a,o,c)}`):void 0}(this._field,t,n);if(r)throw r}return this.obj[xt(t)]=function(r,a){return r.mapKind=="message"?ts(r,a):ss(r,a)}(this._field,n),this}delete(t){const n=xt(t),r=Object.prototype.hasOwnProperty.call(this.obj,n);return r&&delete this.obj[n],r}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[xt(t)];return n!==void 0&&(n=Rn(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,xt(t))}*keys(){for(const t of Object.keys(this.obj))yield Ms(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[Ms(t[0],this._field.mapKey),Rn(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield Rn(this._field,t,this.check)}forEach(t,n){for(const r of this.entries())t.call(n,r[1],r[0],this)}}function ts(e,t){return Wr(t)?po(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?No(t.message):t.message:t}function ns(e,t,n){return t!==void 0&&(At(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:rs(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&Fe(t)&&(t=wo(t))),new To(e.message,t,n)}function Os(e,t){return e.listKind=="message"?ts(e,t):ss(e,t)}function kn(e,t,n){return e.listKind=="message"?ns(e,t,n):rs(e,t)}function Rn(e,t,n){return e.mapKind=="message"?ns(e,t,n):t}function xt(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function Ms(e,t){switch(t){case b.STRING:return e;case b.INT32:case b.FIXED32:case b.UINT32:case b.SFIXED32:case b.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case b.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case b.UINT64:case b.FIXED64:try{return P.uParse(e)}catch{}break;default:try{return P.parse(e)}catch{}}return e}function rs(e,t){switch(e.scalar){case b.INT64:case b.SFIXED64:case b.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=P.parse(t));break;case b.FIXED64:case b.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=P.uParse(t))}return t}function ss(e,t){switch(e.scalar){case b.INT64:case b.SFIXED64:case b.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=P.parse(t));break;case b.FIXED64:case b.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=P.uParse(t))}return t}function wo(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(Fe(e))for(const[n,r]of Object.entries(e))t.fields[n]=ko(r);return t}function No(e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=Io(r);return t}function Io(e){switch(e.kind.case){case"structValue":return No(e.kind.value);case"listValue":return e.kind.value.values.map(Io);case"nullValue":case void 0:return null;default:return e.kind.value}}function ko(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const r of e)n.values.push(ko(r));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:wo(e)}}return t}function Ro(e){const t=function(){if(!ze){ze=[];const c=Co("std");for(let l=0;l<c.length;l++)ze[c[l].charCodeAt(0)]=l;ze[45]=c.indexOf("+"),ze[95]=c.indexOf("/")}return ze}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let r,a=new Uint8Array(n),s=0,o=0,i=0;for(let c=0;c<e.length;c++){if(r=t[e.charCodeAt(c)],r===void 0)switch(e[c]){case"=":o=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(o){case 0:i=r,o=1;break;case 1:a[s++]=i<<2|(48&r)>>4,i=r,o=2;break;case 2:a[s++]=(15&i)<<4|(60&r)>>2,i=r,o=3;break;case 3:a[s++]=(3&i)<<6|r,o=0}}if(o==1)throw Error("invalid base64 string");return a.subarray(0,s)}let Lt,Ps,ze;function Co(e){return Lt||(Lt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),Ps=Lt.slice(0,-2).concat("-","_")),e=="url"?Ps:Lt}function wt(e){let t=!1;const n=[];for(let r=0;r<e.length;r++){let a=e.charAt(r);switch(a){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(a),t=!1;break;default:t&&(t=!1,a=a.toUpperCase()),n.push(a)}}return n.join("")}const dl=new Set(["constructor","toString","toJSON","valueOf"]);function Nt(e){return dl.has(e)?e+"$":e}function as(e){for(const t of e.field)yt(t,"jsonName")||(t.jsonName=wt(t.name));e.nestedType.forEach(as)}function pl(e,t){switch(e){case b.STRING:return t;case b.BYTES:{const n=function(r){const a=[],s={tail:r,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(o){if(this.tail.length>=o){const i=this.tail.substring(0,o);return this.tail=this.tail.substring(o),i}return!1}};for(;s.next();)if(s.c==="\\"){if(s.next())switch(s.c){case"\\":a.push(s.c.charCodeAt(0));break;case"b":a.push(8);break;case"f":a.push(12);break;case"n":a.push(10);break;case"r":a.push(13);break;case"t":a.push(9);break;case"v":a.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const o=s.c,i=s.take(2);if(i===!1)return!1;const c=parseInt(o+i,8);if(Number.isNaN(c))return!1;a.push(c);break}case"x":{const o=s.c,i=s.take(2);if(i===!1)return!1;const c=parseInt(o+i,16);if(Number.isNaN(c))return!1;a.push(c);break}case"u":{const o=s.c,i=s.take(4);if(i===!1)return!1;const c=parseInt(o+i,16);if(Number.isNaN(c))return!1;const l=new Uint8Array(4);new DataView(l.buffer).setInt32(0,c,!0),a.push(l[0],l[1],l[2],l[3]);break}case"U":{const o=s.c,i=s.take(8);if(i===!1)return!1;const c=P.uEnc(o+i),l=new Uint8Array(8),p=new DataView(l.buffer);p.setInt32(0,c.lo,!0),p.setInt32(4,c.hi,!0),a.push(l[0],l[1],l[2],l[3],l[4],l[5],l[6],l[7]);break}}}else a.push(s.c.charCodeAt(0));return new Uint8Array(a)}(t);if(n===!1)throw new Error(`cannot parse ${b[e]} default value: ${t}`);return n}case b.INT64:case b.SFIXED64:case b.SINT64:return P.parse(t);case b.UINT64:case b.FIXED64:return P.uParse(t);case b.DOUBLE:case b.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case b.BOOL:return t==="true";case b.INT32:case b.UINT32:case b.SINT32:case b.FIXED32:case b.SFIXED32:return parseInt(t,10)}}function*rr(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*rr(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*rr(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function Ao(...e){const t=function(){const n=new Map,r=new Map,a=new Map;return{kind:"registry",types:n,extendees:r,[Symbol.iterator]:()=>n.values(),get files(){return a.values()},addFile(s,o,i){if(a.set(s.proto.name,s),!o)for(const c of rr(s))this.add(c);if(i)for(const c of s.dependencies)this.addFile(c,o,i)},add(s){if(s.kind=="extension"){let o=r.get(s.extendee.typeName);o||r.set(s.extendee.typeName,o=new Map),o.set(s.number,s)}n.set(s.typeName,s)},get:s=>n.get(s),getFile:s=>a.get(s),getMessage(s){const o=n.get(s);return(o==null?void 0:o.kind)=="message"?o:void 0},getEnum(s){const o=n.get(s);return(o==null?void 0:o.kind)=="enum"?o:void 0},getExtension(s){const o=n.get(s);return(o==null?void 0:o.kind)=="extension"?o:void 0},getExtensionFor(s,o){var i;return(i=r.get(s.typeName))===null||i===void 0?void 0:i.get(o)},getService(s){const o=n.get(s);return(o==null?void 0:o.kind)=="service"?o:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)Fs(n,t);return t}if("$typeName"in e[0]){let s=function(o){const i=[];for(const c of o.dependency){if(t.getFile(c)!=null||a.has(c))continue;const l=r(c);if(!l)throw new Error(`Unable to resolve ${c}, imported by ${o.name}`);"kind"in l?t.addFile(l,!1,!0):(a.add(l.name),i.push(l))}return i.concat(...i.map(s))};const n=e[0],r=e[1],a=new Set;for(const o of[n,...s(n)].reverse())Fs(o,t)}else for(const n of e)for(const r of n.files)t.addFile(r);return t}const hl=998,ml=999,fl=9,bt=10,pt=11,gl=12,xs=14,sr=3,yl=2,Ls=1,bl=0,Cn=1,Ds=2,vl=3,_l=1,El=2,Sl=1,Oo={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2,defaultSymbolVisibility:1},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2,defaultSymbolVisibility:1},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2,defaultSymbolVisibility:1}};function Fs(e,t){var n,r;const a={kind:"file",proto:e,deprecated:(r=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&r!==void 0&&r,edition:Nl(e),name:e.name.replace(/\.proto$/,""),dependencies:Il(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},s=new Map,o={get:i=>s.get(i),add(i){var c;ce(((c=i.proto.options)===null||c===void 0?void 0:c.mapEntry)===!0),s.set(i.typeName,i)}};for(const i of e.enumType)Mo(i,a,void 0,t);for(const i of e.messageType)Po(i,a,void 0,t,o);for(const i of e.service)Tl(i,a,t);ar(a,t);for(const i of s.values())or(i,t,o);for(const i of a.messages)or(i,t,o),ar(i,t);t.addFile(a,!0)}function ar(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const r=ir(n,e,t);e.extensions.push(r),t.add(r)}break;case"message":for(const n of e.proto.extension){const r=ir(n,e,t);e.nestedExtensions.push(r),t.add(r)}for(const n of e.nestedMessages)ar(n,t)}}function or(e,t,n){const r=e.proto.oneofDecl.map(s=>function(o,i){return{kind:"oneof",proto:o,deprecated:!1,parent:i,fields:[],name:o.name,localName:Nt(wt(o.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(s,e)),a=new Set;for(const s of e.proto.field){const o=kl(s,r),i=ir(s,e,t,o,n);e.fields.push(i),e.field[i.localName]=i,o===void 0?e.members.push(i):(o.fields.push(i),a.has(o)||(a.add(o),e.members.push(o)))}for(const s of r.filter(o=>a.has(o)))e.oneofs.push(s);for(const s of e.nestedMessages)or(s,t,n)}function Mo(e,t,n,r){var a,s,o,i,c;const l=function(u,d){const h=(m=u,(m.substring(0,1)+m.substring(1).replace(/[A-Z]/g,y=>"_"+y)).toLowerCase()+"_");var m;for(const y of d){if(!y.name.toLowerCase().startsWith(h))return;const g=y.name.substring(h.length);if(g.length==0||/^\d/.test(g))return}return h}(e.name,e.value),p={kind:"enum",proto:e,deprecated:(s=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&s!==void 0&&s,file:t,parent:n,open:!0,name:e.name,typeName:_n(e,n,t),value:{},values:[],sharedPrefix:l,toString(){return`enum ${this.typeName}`}};p.open=function(u){var d;return Sl==at("enumType",{proto:u.proto,parent:(d=u.parent)!==null&&d!==void 0?d:u.file})}(p),r.add(p);for(const u of e.value){const d=u.name;p.values.push(p.value[u.number]={kind:"enum_value",proto:u,deprecated:(i=(o=u.options)===null||o===void 0?void 0:o.deprecated)!==null&&i!==void 0&&i,parent:p,name:d,localName:Nt(l==null?d:d.substring(l.length)),number:u.number,toString:()=>`enum value ${p.typeName}.${d}`})}((c=n==null?void 0:n.nestedEnums)!==null&&c!==void 0?c:t.enums).push(p)}function Po(e,t,n,r,a){var s,o,i,c;const l={kind:"message",proto:e,deprecated:(o=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&o!==void 0&&o,file:t,parent:n,name:e.name,typeName:_n(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=e.options)===null||i===void 0?void 0:i.mapEntry)===!0?a.add(l):(((c=n==null?void 0:n.nestedMessages)!==null&&c!==void 0?c:t.messages).push(l),r.add(l));for(const p of e.enumType)Mo(p,t,l,r);for(const p of e.nestedType)Po(p,t,l,r,a)}function Tl(e,t,n){var r,a;const s={kind:"service",proto:e,deprecated:(a=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&a!==void 0&&a,file:t,name:e.name,typeName:_n(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(s),n.add(s);for(const o of e.method){const i=wl(o,s,n);s.methods.push(i),s.method[i.localName]=i}}function wl(e,t,n){var r,a,s,o;let i;i=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const c=n.getMessage(Ie(e.inputType)),l=n.getMessage(Ie(e.outputType));ce(c,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),ce(l,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const p=e.name;return{kind:"rpc",proto:e,deprecated:(a=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&a!==void 0&&a,parent:t,name:p,localName:Nt(p.length?Nt(p[0].toLowerCase()+p.substring(1)):p),methodKind:i,input:c,output:l,idempotency:(o=(s=e.options)===null||s===void 0?void 0:s.idempotencyLevel)!==null&&o!==void 0?o:bl,toString:()=>`rpc ${t.typeName}.${p}`}}function ir(e,t,n,r,a){var s,o,i;const c=a===void 0,l={kind:"field",proto:e,deprecated:(o=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&o!==void 0&&o,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:Rl(e,r,c,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(c){const h=t.kind=="file"?t:t.file,m=t.kind=="file"?void 0:t,y=_n(e,m,h);l.kind="extension",l.file=h,l.parent=m,l.oneof=void 0,l.typeName=y,l.jsonName=`[${y}]`,l.toString=()=>`extension ${y}`;const g=n.getMessage(Ie(e.extendee));ce(g,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),l.extendee=g}else{const h=t;ce(h.kind=="message"),l.parent=h,l.oneof=r,l.localName=r?wt(e.name):Nt(wt(e.name)),l.jsonName=e.jsonName,l.toString=()=>`field ${h.typeName}.${e.name}`}const p=e.label,u=e.type,d=(i=e.options)===null||i===void 0?void 0:i.jstype;if(p===sr){const h=u==pt?a==null?void 0:a.get(Ie(e.typeName)):void 0;if(h){l.fieldKind="map";const{key:m,value:y}=function(g){const v=g.fields.find(_=>_.number===1),E=g.fields.find(_=>_.number===2);return ce(v&&v.fieldKind=="scalar"&&v.scalar!=b.BYTES&&v.scalar!=b.FLOAT&&v.scalar!=b.DOUBLE&&E&&E.fieldKind!="list"&&E.fieldKind!="map"),{key:v,value:E}}(h);return l.mapKey=m.scalar,l.mapKind=y.fieldKind,l.message=y.message,l.delimitedEncoding=!1,l.enum=y.enum,l.scalar=y.scalar,l}switch(l.fieldKind="list",u){case pt:case bt:l.listKind="message",l.message=n.getMessage(Ie(e.typeName)),ce(l.message),l.delimitedEncoding=Us(e,t);break;case xs:l.listKind="enum",l.enum=n.getEnum(Ie(e.typeName)),ce(l.enum);break;default:l.listKind="scalar",l.scalar=u,l.longAsString=d==Ls}return l.packed=function(m,y){if(m.label!=sr)return!1;switch(m.type){case fl:case gl:case bt:case pt:return!1}const g=m.options;return g&&yt(g,"packed")?g.packed:_l==at("repeatedFieldEncoding",{proto:m,parent:y})}(e,t),l}switch(u){case pt:case bt:l.fieldKind="message",l.message=n.getMessage(Ie(e.typeName)),ce(l.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.delimitedEncoding=Us(e,t),l.getDefaultValue=()=>{};break;case xs:{const h=n.getEnum(Ie(e.typeName));ce(h!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.fieldKind="enum",l.enum=n.getEnum(Ie(e.typeName)),l.getDefaultValue=()=>yt(e,"defaultValue")?function(m,y){const g=m.values.find(v=>v.name===y);if(!g)throw new Error(`cannot parse ${m} default value: ${y}`);return g.number}(h,e.defaultValue):void 0;break}default:l.fieldKind="scalar",l.scalar=u,l.longAsString=d==Ls,l.getDefaultValue=()=>yt(e,"defaultValue")?pl(u,e.defaultValue):void 0}return l}function Nl(e){switch(e.syntax){case"":case"proto2":return hl;case"proto3":return ml;case"editions":if(e.edition in Oo)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function Il(e,t){return e.dependency.map(n=>{const r=t.getFile(n);if(!r)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return r})}function _n(e,t,n){let r;return r=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,r}function Ie(e){return e.startsWith(".")?e.substring(1):e}function kl(e,t){if(!yt(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return ce(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function Rl(e,t,n,r){if(e.label==yl)return vl;if(e.label==sr)return Ds;if(t||e.proto3Optional||n)return Cn;const a=at("fieldPresence",{proto:e,parent:r});return a!=Ds||e.type!=pt&&e.type!=bt?a:Cn}function Us(e,t){return e.type==bt||El==at("messageEncoding",{proto:e,parent:t})}function at(e,t){var n,r;const a=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(a){const s=a[e];if(s!=0)return s}if("kind"in t){if(t.kind=="message")return at(e,(r=t.parent)!==null&&r!==void 0?r:t.file);const s=Oo[t.edition];if(!s)throw new Error(`feature default for edition ${t.edition} not found`);return s[e]}return at(e,t.parent)}function ce(e,t){if(!e)throw new Error(t)}function Cl(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],optionDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(xo),enumType:n.enumType.map(Lo)}))}(e);return t.messageType.forEach(as),Ao(t,()=>{}).getFile(t.name)}function xo(e){var t,n,r,a,s,o,i,c;return Object.assign(Object.create({visibility:0}),{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(Al))!==null&&n!==void 0?n:[],extension:[],nestedType:(a=(r=e.nestedType)===null||r===void 0?void 0:r.map(xo))!==null&&a!==void 0?a:[],enumType:(o=(s=e.enumType)===null||s===void 0?void 0:s.map(Lo))!==null&&o!==void 0?o:[],extensionRange:(c=(i=e.extensionRange)===null||i===void 0?void 0:i.map(p=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},p)))!==null&&c!==void 0?c:[],oneofDecl:[],reservedRange:[],reservedName:[]})}function Al(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?Ol(e.options):void 0}))}function Ol(e){var t,n,r;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(r=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(s=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},s)))!==null&&r!==void 0?r:[],uninterpretedOption:[]}))}function Lo(e){return Object.assign(Object.create({visibility:0}),{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(n=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},n))})}function Ot(e,t,...n){return n.reduce((r,a)=>r.nestedMessages[a],e.messages[t])}const Ml=Ot(Cl({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"option_dependency",number:15,type:9,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3},{name:"visibility",number:11,type:14,label:1,typeName:".google.protobuf.SymbolVisibility"}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3},{name:"visibility",number:6,type:14,label:1,typeName:".google.protobuf.SymbolVisibility"}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}},{name:"default_symbol_visibility",number:8,type:14,label:1,typeName:".google.protobuf.FeatureSet.VisibilityFeature.DefaultSymbolVisibility",options:{retention:2,targets:[1],editionDefaults:[{value:"EXPORT_ALL",edition:900},{value:"EXPORT_TOP_LEVEL",edition:1001}]}}],nestedType:[{name:"VisibilityFeature",enumType:[{name:"DefaultSymbolVisibility",value:[{name:"DEFAULT_SYMBOL_VISIBILITY_UNKNOWN",number:0},{name:"EXPORT_ALL",number:1},{name:"EXPORT_TOP_LEVEL",number:2},{name:"LOCAL_ALL",number:3},{name:"STRICT",number:4}]}]}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]},{name:"SymbolVisibility",value:[{name:"VISIBILITY_UNSET",number:0},{name:"VISIBILITY_LOCAL",number:1},{name:"VISIBILITY_EXPORT",number:2}]}]}),1);var $s,Bs,Vs,Gs,js,qs,Ys,Ks,Hs,Ws,Xs,zs,Js,Zs,Qs,ea,ta,na,ra,sa;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})($s||($s={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(Bs||(Bs={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(Vs||(Vs={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(Gs||(Gs={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(js||(js={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(qs||(qs={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(Ys||(Ys={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(Ks||(Ks={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(Hs||(Hs={})),function(e){e[e.DEFAULT_SYMBOL_VISIBILITY_UNKNOWN=0]="DEFAULT_SYMBOL_VISIBILITY_UNKNOWN",e[e.EXPORT_ALL=1]="EXPORT_ALL",e[e.EXPORT_TOP_LEVEL=2]="EXPORT_TOP_LEVEL",e[e.LOCAL_ALL=3]="LOCAL_ALL",e[e.STRICT=4]="STRICT"}(Ws||(Ws={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(Xs||(Xs={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(zs||(zs={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(Js||(Js={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(Zs||(Zs={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(Qs||(Qs={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(ea||(ea={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(ta||(ta={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(na||(na={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(ra||(ra={})),function(e){e[e.VISIBILITY_UNSET=0]="VISIBILITY_UNSET",e[e.VISIBILITY_LOCAL=1]="VISIBILITY_LOCAL",e[e.VISIBILITY_EXPORT=2]="VISIBILITY_EXPORT"}(sa||(sa={}));const Pl={readUnknownFields:!0};function os(e,t,n){const r=ie(e,void 0,!1);return Do(r,new Qr(t),Pl,!1,t.byteLength),r.message}function Do(e,t,n,r,a){var s;const o=r?t.len:t.pos+a;let i,c;const l=(s=e.getUnknown())!==null&&s!==void 0?s:[];for(;t.pos<o&&([i,c]=t.tag(),!r||c!=L.EndGroup);){const p=e.findNumber(i);if(p)Fo(e,t,p,c,n);else{const u=t.skip(c,i);n.readUnknownFields&&l.push({no:i,wireType:c,data:u})}}if(r&&(c!=L.EndGroup||i!==a))throw new Error("invalid end group tag");l.length>0&&e.setUnknown(l)}function Fo(e,t,n,r,a){var s;switch(n.fieldKind){case"scalar":e.set(n,Je(t,n.scalar));break;case"enum":const o=Je(t,b.INT32);if(n.enum.open)e.set(n,o);else if(n.enum.values.some(i=>i.number===o))e.set(n,o);else if(a.readUnknownFields){const i=new Zr().int32(o).finish(),c=(s=e.getUnknown())!==null&&s!==void 0?s:[];c.push({no:n.number,wireType:r,data:i}),e.setUnknown(c)}break;case"message":e.set(n,An(t,a,n,e.get(n)));break;case"list":(function(i,c,l,p){var u;const d=l.field();if(d.listKind==="message")return void l.add(An(i,p,d));const h=(u=d.scalar)!==null&&u!==void 0?u:b.INT32;if(!(c==L.LengthDelimited&&h!=b.STRING&&h!=b.BYTES))return void l.add(Je(i,h));const y=i.uint32()+i.pos;for(;i.pos<y;)l.add(Je(i,h))})(t,r,e.get(n),a);break;case"map":(function(i,c,l){const p=c.field();let u,d;const h=i.pos+i.uint32();for(;i.pos<h;){const[m]=i.tag();switch(m){case 1:u=Je(i,p.mapKey);break;case 2:switch(p.mapKind){case"scalar":d=Je(i,p.scalar);break;case"enum":d=i.int32();break;case"message":d=An(i,l,p)}}}if(u===void 0&&(u=qe(p.mapKey,!1)),d===void 0)switch(p.mapKind){case"scalar":d=qe(p.scalar,!1);break;case"enum":d=p.enum.values[0].number;break;case"message":d=ie(p.message,void 0,!1)}c.set(u,d)})(t,e.get(n),a)}}function An(e,t,n,r){const a=n.delimitedEncoding,s=r??ie(n.message,void 0,!1);return Do(s,e,t,a,a?n.number:e.uint32()),s}function Je(e,t){switch(t){case b.STRING:return e.string();case b.BOOL:return e.bool();case b.DOUBLE:return e.double();case b.FLOAT:return e.float();case b.INT32:return e.int32();case b.INT64:return e.int64();case b.UINT64:return e.uint64();case b.FIXED64:return e.fixed64();case b.BYTES:return e.bytes();case b.FIXED32:return e.fixed32();case b.SFIXED32:return e.sfixed32();case b.SFIXED64:return e.sfixed64();case b.SINT64:return e.sint64();case b.UINT32:return e.uint32();case b.SINT32:return e.sint32()}}function is(e,t){const n=os(Ml,Ro(e));return n.messageType.forEach(as),n.dependency=[],Ao(n,r=>{}).getFile(n.name)}const xl=Ot(is("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),Ll=3,aa={writeUnknownFields:!0};function Dl(e,t,n){return rn(new Zr,function(r){return r?Object.assign(Object.assign({},aa),r):aa}(n),ie(e,t)).finish()}function rn(e,t,n){var r;for(const a of n.sortedFields)if(n.isSet(a))Uo(e,t,n,a);else if(a.presence==Ll)throw new Error(`cannot encode ${a} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:a,wireType:s,data:o}of(r=n.getUnknown())!==null&&r!==void 0?r:[])e.tag(a,s).raw(o);return e}function Uo(e,t,n,r){var a;switch(r.fieldKind){case"scalar":case"enum":sn(e,n.desc.typeName,r.name,(a=r.scalar)!==null&&a!==void 0?a:b.INT32,r.number,n.get(r));break;case"list":(function(s,o,i,c){var l;if(i.listKind=="message"){for(const u of c)oa(s,o,i,u);return}const p=(l=i.scalar)!==null&&l!==void 0?l:b.INT32;if(i.packed){if(!c.size)return;s.tag(i.number,L.LengthDelimited).fork();for(const u of c)$o(s,i.parent.typeName,i.name,p,u);return void s.join()}for(const u of c)sn(s,i.parent.typeName,i.name,p,i.number,u)})(e,t,r,n.get(r));break;case"message":oa(e,t,r,n.get(r));break;case"map":for(const[s,o]of n.get(r))Fl(e,t,r,s,o)}}function sn(e,t,n,r,a,s){$o(e.tag(a,function(o){switch(o){case b.BYTES:case b.STRING:return L.LengthDelimited;case b.DOUBLE:case b.FIXED64:case b.SFIXED64:return L.Bit64;case b.FIXED32:case b.SFIXED32:case b.FLOAT:return L.Bit32;default:return L.Varint}}(r)),t,n,r,s)}function oa(e,t,n,r){n.delimitedEncoding?rn(e.tag(n.number,L.StartGroup),t,r).tag(n.number,L.EndGroup):rn(e.tag(n.number,L.LengthDelimited).fork(),t,r).join()}function Fl(e,t,n,r,a){var s;switch(e.tag(n.number,L.LengthDelimited).fork(),sn(e,n.parent.typeName,n.name,n.mapKey,1,r),n.mapKind){case"scalar":case"enum":sn(e,n.parent.typeName,n.name,(s=n.scalar)!==null&&s!==void 0?s:b.INT32,2,a);break;case"message":rn(e.tag(2,L.LengthDelimited).fork(),t,a).join()}e.join()}function $o(e,t,n,r,a){try{switch(r){case b.STRING:e.string(a);break;case b.BOOL:e.bool(a);break;case b.DOUBLE:e.double(a);break;case b.FLOAT:e.float(a);break;case b.INT32:e.int32(a);break;case b.INT64:e.int64(a);break;case b.UINT64:e.uint64(a);break;case b.FIXED64:e.fixed64(a);break;case b.BYTES:e.bytes(a);break;case b.FIXED32:e.fixed32(a);break;case b.SFIXED32:e.sfixed32(a);break;case b.SFIXED64:e.sfixed64(a);break;case b.SINT64:e.sint64(a);break;case b.UINT32:e.uint32(a);break;case b.SINT32:e.sint32(a)}}catch(s){throw s instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${s.message}`):s}}function Ul(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(ia(e.typeUrl));return n&&function(r,a){return r.typeUrl!==""&&(typeof a=="string"?a:a.typeName)===ia(r.typeUrl)}(e,n)?os(n,e.value):void 0}function ia(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}const ls=is("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),$l=Ot(ls,0),Bo=Ot(ls,1),Bl=Ot(ls,2);var lr;function Vl(e,t){Vo(t,e);const n=function(o,i){if(o===void 0)return[];if(i.fieldKind==="enum"||i.fieldKind==="scalar"){for(let c=o.length-1;c>=0;--c)if(o[c].no==i.number)return[o[c]];return[]}return o.filter(c=>c.no===i.number)}(e.$unknown,t),[r,a,s]=En(t);for(const o of n)Fo(r,new Qr(o.data),a,o.wireType,{readUnknownFields:!0});return s()}function Gl(e,t,n){var r;Vo(t,e);const a=((r=e.$unknown)!==null&&r!==void 0?r:[]).filter(l=>l.no!==t.number),[s,o]=En(t,n),i=new Zr;Uo(i,{writeUnknownFields:!0},s,o);const c=new Qr(i.finish());for(;c.pos<c.len;){const[l,p]=c.tag(),u=c.skip(p,l);a.push({no:l,wireType:p,data:u})}e.$unknown=a}function En(e,t){const n=e.typeName,r=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),a=Object.assign(Object.assign({},e.extendee),{fields:[r],members:[r],oneofs:[]}),s=pe(a,t!==void 0?{[n]:t}:void 0);return[ie(a,s),r,()=>{const o=s[n];if(o===void 0){const i=e.message;return At(i)?qe(i.fields[0].scalar,i.fields[0].longAsString):pe(i)}return o}]}function Vo(e,t){if(e.extendee.typeName!=t.$typeName)throw new Error(`extension ${e.typeName} can only be applied to message ${e.extendee.typeName}`)}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})(lr||(lr={}));const jl=3,ql=2,la={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function Yl(e,t,n){return vt(ie(e,t),function(r){return r?Object.assign(Object.assign({},la),r):la}(n))}function vt(e,t){var n;const r=function(s,o){if(s.desc.typeName.startsWith("google.protobuf.")){switch(s.desc.typeName){case"google.protobuf.Any":return function(c,l){if(c.typeUrl==="")return{};const{registry:p}=l;let u,d;if(p&&(u=Ul(c,p),u&&(d=p.getMessage(u.$typeName))),!d||!u)throw new Error(`cannot encode message ${c.$typeName} to JSON: "${c.typeUrl}" is not in the type registry`);let h=vt(ie(d,u),l);return(d.typeName.startsWith("google.protobuf.")||h===null||Array.isArray(h)||typeof h!="object")&&(h={value:h}),h["@type"]=c.typeUrl,h}(s.message,o);case"google.protobuf.Timestamp":return function(c){const l=1e3*Number(c.seconds);if(l<Date.parse("0001-01-01T00:00:00Z")||l>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${c.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(c.nanos<0)throw new Error(`cannot encode message ${c.$typeName} to JSON: nanos must not be negative`);let p="Z";if(c.nanos>0){const u=(c.nanos+1e9).toString().substring(1);p=u.substring(3)==="000000"?"."+u.substring(0,3)+"Z":u.substring(6)==="000"?"."+u.substring(0,6)+"Z":"."+u+"Z"}return new Date(l).toISOString().replace(".000Z",p)}(s.message);case"google.protobuf.Duration":return function(c){if(Number(c.seconds)>315576e6||Number(c.seconds)<-315576e6)throw new Error(`cannot encode message ${c.$typeName} to JSON: value out of range`);let l=c.seconds.toString();if(c.nanos!==0){let p=Math.abs(c.nanos).toString();p="0".repeat(9-p.length)+p,p.substring(3)==="000000"?p=p.substring(0,3):p.substring(6)==="000"&&(p=p.substring(0,6)),l+="."+p,c.nanos<0&&Number(c.seconds)==0&&(l="-"+l)}return l+"s"}(s.message);case"google.protobuf.FieldMask":return(i=s.message).paths.map(c=>{if(c.match(/_[0-9]?_/g)||c.match(/[A-Z]/g))throw new Error(`cannot encode message ${i.$typeName} to JSON: lowerCamelCase of path name "`+c+'" is irreversible');return wt(c)}).join(",");case"google.protobuf.Struct":return Go(s.message);case"google.protobuf.Value":return us(s.message);case"google.protobuf.ListValue":return jo(s.message);default:if(At(s.desc)){const c=s.desc.fields[0];return Ht(c,s.get(c))}return}var i}}(e,t);if(r!==void 0)return r;const a={};for(const s of e.sortedFields){if(!e.isSet(s)){if(s.presence==jl)throw new Error(`cannot encode ${s} to JSON: required field not set`);if(!t.alwaysEmitImplicit||s.presence!==ql)continue}const o=ua(s,e.get(s),t);o!==void 0&&(a[Kl(s,t)]=o)}if(t.registry){const s=new Set;for(const{no:o}of(n=e.getUnknown())!==null&&n!==void 0?n:[])if(!s.has(o)){s.add(o);const i=t.registry.getExtensionFor(e.desc,o);if(!i)continue;const c=Vl(e.message,i),[l,p]=En(i,c),u=ua(p,l.get(p),t);u!==void 0&&(a[i.jsonName]=u)}}return a}function ua(e,t,n){switch(e.fieldKind){case"scalar":return Ht(e,t);case"message":return vt(t,n);case"enum":return On(e.enum,t,n.enumAsInteger);case"list":return function(r,a){const s=r.field(),o=[];switch(s.listKind){case"scalar":for(const i of r)o.push(Ht(s,i));break;case"enum":for(const i of r)o.push(On(s.enum,i,a.enumAsInteger));break;case"message":for(const i of r)o.push(vt(i,a))}return a.alwaysEmitImplicit||o.length>0?o:void 0}(t,n);case"map":return function(r,a){const s=r.field(),o={};switch(s.mapKind){case"scalar":for(const[i,c]of r)o[i]=Ht(s,c);break;case"message":for(const[i,c]of r)o[i]=vt(c,a);break;case"enum":for(const[i,c]of r)o[i]=On(s.enum,c,a.enumAsInteger)}return a.alwaysEmitImplicit||r.size>0?o:void 0}(t,n)}}function On(e,t,n){var r;if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: expected number, got ${D(t)}`);if(e.typeName=="google.protobuf.NullValue")return null;if(n)return t;const a=e.value[t];return(r=a==null?void 0:a.name)!==null&&r!==void 0?r:t}function Ht(e,t){var n,r,a,s,o,i;switch(e.scalar){case b.INT32:case b.SFIXED32:case b.SINT32:case b.FIXED32:case b.UINT32:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(n=$e(e,t))===null||n===void 0?void 0:n.message}`);return t;case b.FLOAT:case b.DOUBLE:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(r=$e(e,t))===null||r===void 0?void 0:r.message}`);return Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case b.STRING:if(typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(a=$e(e,t))===null||a===void 0?void 0:a.message}`);return t;case b.BOOL:if(typeof t!="boolean")throw new Error(`cannot encode ${e} to JSON: ${(s=$e(e,t))===null||s===void 0?void 0:s.message}`);return t;case b.UINT64:case b.FIXED64:case b.INT64:case b.SFIXED64:case b.SINT64:if(typeof t!="bigint"&&typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(o=$e(e,t))===null||o===void 0?void 0:o.message}`);return t.toString();case b.BYTES:if(t instanceof Uint8Array)return function(c,l="std"){const p=Co(l),u=l=="std";let d,h="",m=0,y=0;for(let g=0;g<c.length;g++)switch(d=c[g],m){case 0:h+=p[d>>2],y=(3&d)<<4,m=1;break;case 1:h+=p[y|d>>4],y=(15&d)<<2,m=2;break;case 2:h+=p[y|d>>6],h+=p[63&d],m=0}return m&&(h+=p[y],u&&(h+="=",m==1&&(h+="="))),h}(t);throw new Error(`cannot encode ${e} to JSON: ${(i=$e(e,t))===null||i===void 0?void 0:i.message}`)}}function Kl(e,t){return t.useProtoFieldName?e.name:e.jsonName}function Go(e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=us(r);return t}function us(e){switch(e.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(e.kind.value))throw new Error(`${e.$typeName} cannot be NaN or Infinity`);return e.kind.value;case"boolValue":case"stringValue":return e.kind.value;case"structValue":return Go(e.kind.value);case"listValue":return jo(e.kind.value);default:throw new Error(`${e.$typeName} must have a value`)}}function jo(e){return e.values.map(us)}const ca={ignoreUnknownFields:!1};function Hl(e,t,n){const r=ie(e);try{st(r,t,function(s){return s?Object.assign(Object.assign({},ca),s):ca}(n))}catch(s){throw(a=s)instanceof Error&&ll.includes(a.name)&&"field"in a&&typeof a.field=="function"?new Error(`cannot decode ${s.field()} from JSON: ${s.message}`,{cause:s}):s}var a;return r.message}function st(e,t,n){var r;if(function(o,i,c){if(!o.desc.typeName.startsWith("google.protobuf."))return!1;switch(o.desc.typeName){case"google.protobuf.Any":return function(l,p,u){var d;if(p===null||Array.isArray(p)||typeof p!="object")throw new Error(`cannot decode message ${l.$typeName} from JSON: expected object but got ${D(p)}`);if(Object.keys(p).length==0)return;const h=p["@type"];if(typeof h!="string"||h=="")throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is empty`);const m=h.includes("/")?h.substring(h.lastIndexOf("/")+1):h;if(!m.length)throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is invalid`);const y=(d=u.registry)===null||d===void 0?void 0:d.getMessage(m);if(!y)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${h} is not in the type registry`);const g=ie(y);if(m.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(p,"value"))st(g,p.value,u);else{const v=Object.assign({},p);delete v["@type"],st(g,v,u)}(function(v,E,_){let T=!1;_||(_=pe(xl),T=!0),_.value=Dl(v,E),_.typeUrl=`type.googleapis.com/${E.$typeName}`})(g.desc,g.message,l)}(o.message,i,c),!0;case"google.protobuf.Timestamp":return function(l,p){if(typeof p!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${D(p)}`);const u=p.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!u)throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);const d=Date.parse(u[1]+"-"+u[2]+"-"+u[3]+"T"+u[4]+":"+u[5]+":"+u[6]+(u[8]?u[8]:"Z"));if(Number.isNaN(d))throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);if(d<Date.parse("0001-01-01T00:00:00Z")||d>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${l.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);l.seconds=P.parse(d/1e3),l.nanos=0,u[7]&&(l.nanos=parseInt("1"+u[7]+"0".repeat(9-u[7].length))-1e9)}(o.message,i),!0;case"google.protobuf.Duration":return function(l,p){if(typeof p!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${D(p)}`);const u=p.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(u===null)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${D(p)}`);const d=Number(u[1]);if(d>315576e6||d<-315576e6)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${D(p)}`);if(l.seconds=P.parse(d),typeof u[2]!="string")return;const h=u[2]+"0".repeat(9-u[2].length);l.nanos=parseInt(h),(d<0||Object.is(d,-0))&&(l.nanos=-l.nanos)}(o.message,i),!0;case"google.protobuf.FieldMask":return function(l,p){if(typeof p!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${D(p)}`);if(p==="")return;function u(d){if(d.includes("_"))throw new Error(`cannot decode message ${l.$typeName} from JSON: path names must be lowerCamelCase`);const h=d.replace(/[A-Z]/g,m=>"_"+m.toLowerCase());return h[0]==="_"?h.substring(1):h}l.paths=p.split(",").map(u)}(o.message,i),!0;case"google.protobuf.Struct":return Yo(o.message,i),!0;case"google.protobuf.Value":return cs(o.message,i),!0;case"google.protobuf.ListValue":return Ko(o.message,i),!0;default:if(At(o.desc)){const l=o.desc.fields[0];return i===null?o.clear(l):o.set(l,Xt(l,i,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${D(t)}`);const a=new Map,s=new Map;for(const o of e.desc.fields)s.set(o.name,o).set(o.jsonName,o);for(const[o,i]of Object.entries(t)){const c=s.get(o);if(c){if(c.oneof){if(i===null&&c.fieldKind=="scalar")continue;const l=a.get(c.oneof);if(l!==void 0)throw new X(c.oneof,`oneof set multiple times by ${l.name} and ${c.name}`);a.set(c.oneof,c)}da(e,c,i,n)}else{let l;if(o.startsWith("[")&&o.endsWith("]")&&(l=(r=n.registry)===null||r===void 0?void 0:r.getExtension(o.substring(1,o.length-1)))&&l.extendee.typeName===e.desc.typeName){const[p,u,d]=En(l);da(p,u,i,n),Gl(e.message,l,d())}if(!l&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${o}" is unknown`)}}}function da(e,t,n,r){switch(t.fieldKind){case"scalar":(function(a,s,o){const i=Xt(s,o,!1);i===an?a.clear(s):a.set(s,i)})(e,t,n);break;case"enum":(function(a,s,o,i){const c=Mn(s.enum,o,i.ignoreUnknownFields,!1);c===an?a.clear(s):c!==Wt&&a.set(s,c)})(e,t,n,r);break;case"message":(function(a,s,o,i){if(o===null&&s.message.typeName!="google.protobuf.Value")return void a.clear(s);const c=a.isSet(s)?a.get(s):ie(s.message);st(c,o,i),a.set(s,c)})(e,t,n,r);break;case"list":(function(a,s,o){if(s===null)return;const i=a.field();if(!Array.isArray(s))throw new X(i,"expected Array, got "+D(s));for(const c of s){if(c===null)throw new X(i,"list item must not be null");switch(i.listKind){case"message":const l=ie(i.message);st(l,c,o),a.add(l);break;case"enum":const p=Mn(i.enum,c,o.ignoreUnknownFields,!0);p!==Wt&&a.add(p);break;case"scalar":a.add(Xt(i,c,!0))}}})(e.get(t),n,r);break;case"map":(function(a,s,o){if(s===null)return;const i=a.field();if(typeof s!="object"||Array.isArray(s))throw new X(i,"expected object, got "+D(s));for(const[c,l]of Object.entries(s)){if(l===null)throw new X(i,"map value must not be null");let p;switch(i.mapKind){case"message":const d=ie(i.message);st(d,l,o),p=d;break;case"enum":if(p=Mn(i.enum,l,o.ignoreUnknownFields,!0),p===Wt)return;break;case"scalar":p=Xt(i,l,!0)}const u=Wl(i.mapKey,c);a.set(u,p)}})(e.get(t),n,r)}}const Wt=Symbol();function Mn(e,t,n,r){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:r?e.values[0].number:an;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const a=e.values.find(s=>s.name===t);if(a!==void 0)return a.number;if(n)return Wt}throw new Error(`cannot decode ${e} from JSON: ${D(t)}`)}const an=Symbol();function Xt(e,t,n){if(t===null)return n?qe(e.scalar,!1):an;switch(e.scalar){case b.DOUBLE:case b.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new X(e,"unexpected NaN number");if(!Number.isFinite(t))throw new X(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const r=Number(t);if(!Number.isFinite(r))break;return r}break;case b.INT32:case b.FIXED32:case b.SFIXED32:case b.SINT32:case b.UINT32:return qo(t);case b.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return Ro(t)}catch(r){const a=r instanceof Error?r.message:String(r);throw new X(e,a)}}}return t}function Wl(e,t){switch(e){case b.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case b.INT32:case b.FIXED32:case b.UINT32:case b.SFIXED32:case b.SINT32:return qo(t);default:return t}}function qo(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function Yo(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${D(t)}`);for(const[n,r]of Object.entries(t)){const a=pe(Bo);cs(a,r),e.fields[n]=a}}function cs(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:lr.NULL_VALUE};else if(Array.isArray(t)){const n=pe(Bl);Ko(n,t),e.kind={case:"listValue",value:n}}else{const n=pe($l);Yo(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${D(t)}`)}return e}function Ko(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${D(t)}`);for(const n of t){const r=pe(Bo);cs(r,n),e.values.push(r)}}class Ho{constructor(t){f(this,"target");f(this,"pendingRequests",new Map);f(this,"cleanup");f(this,"serviceRegistries",new Set);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,r=>{this.target.sendMessage(r)})}catch(r){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:r instanceof Error?r.message:String(r)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(`gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}`));else try{if(!t.data&&t.data!==null&&t.data!=="")throw new Error(`gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})`);n.resolve(t)}catch(r){const a=r instanceof Error?r.message:String(r);n.reject(new Error(`Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${a}`))}}sendRequest(t,n){return new Promise((r,a)=>{let s;n&&(s=setTimeout(()=>{this.pendingRequests.delete(t.id),a(new Error(`gRPC request timed out after ${n}ms: ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(t.id,{resolve:r,reject:a,timeout:s}),this.target.sendMessage(t)})}async unary(t,n,r,a,s,o){const i=crypto.randomUUID(),c=t.localName,l=t.parent.typeName;if(!l)throw new Error("Service name is required for unary calls");const p=s?Yl(t.input,pe(t.input,s)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${l}.${c} (ID: ${i})`);let u;n&&(u=()=>{const h=this.pendingRequests.get(i);h&&(this.pendingRequests.delete(i),clearTimeout(h.timeout),h.reject(new Error(`gRPC request aborted during execution: ${l}.${c} (ID: ${i})`)))},n.addEventListener("abort",u));const d=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:c,serviceTypeName:l,data:p,timeout:r},r);return n&&u&&n.removeEventListener("abort",u),{stream:!1,method:t,service:t.parent,header:new Headers(a),message:Hl(t.output,d.data),trailer:new Headers}}stream(t,n,r,a,s,o){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}}f(Ho,"PROTOCOL_NAME","com.augmentcode.client.rpc");var Le;function pa(e){const t=Le[e];return typeof t!="string"?e.toString():t[0].toLowerCase()+t.substring(1).replace(/[A-Z]/g,n=>"_"+n.toLowerCase())}(function(e){e[e.Canceled=1]="Canceled",e[e.Unknown=2]="Unknown",e[e.InvalidArgument=3]="InvalidArgument",e[e.DeadlineExceeded=4]="DeadlineExceeded",e[e.NotFound=5]="NotFound",e[e.AlreadyExists=6]="AlreadyExists",e[e.PermissionDenied=7]="PermissionDenied",e[e.ResourceExhausted=8]="ResourceExhausted",e[e.FailedPrecondition=9]="FailedPrecondition",e[e.Aborted=10]="Aborted",e[e.OutOfRange=11]="OutOfRange",e[e.Unimplemented=12]="Unimplemented",e[e.Internal=13]="Internal",e[e.Unavailable=14]="Unavailable",e[e.DataLoss=15]="DataLoss",e[e.Unauthenticated=16]="Unauthenticated"})(Le||(Le={}));class Oe extends Error{constructor(t,n=Le.Unknown,r,a,s){super(function(o,i){return o.length?`[${pa(i)}] ${o}`:`[${pa(i)}]`}(t,n)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=t,this.code=n,this.metadata=new Headers(r??{}),this.details=a??[],this.cause=s}static from(t,n=Le.Unknown){return t instanceof Oe?t:t instanceof Error?t.name=="AbortError"?new Oe(t.message,Le.Canceled):new Oe(t.message,n,void 0,void 0,t):new Oe(String(t),n,void 0,void 0,t)}static[Symbol.hasInstance](t){return t instanceof Error&&(Object.getPrototypeOf(t)===Oe.prototype||t.name==="ConnectError"&&"code"in t&&typeof t.code=="number"&&"metadata"in t&&"details"in t&&Array.isArray(t.details)&&"rawMessage"in t&&typeof t.rawMessage=="string"&&"cause"in t)}findDetails(t){const n=t.kind==="message"?{getMessage:a=>a===t.typeName?t:void 0}:t,r=[];for(const a of this.details){if("desc"in a){n.getMessage(a.desc.typeName)&&r.push(pe(a.desc,a.value));continue}const s=n.getMessage(a.type);if(s)try{r.push(os(s,a.value))}catch{}}return r}}var Xl=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(a){t[a]=e[a]&&function(s){return new Promise(function(o,i){(function(c,l,p,u){Promise.resolve(u).then(function(d){c({value:d,done:p})},l)})(o,i,(s=e[a](s)).done,s.value)})}}},It=function(e){return this instanceof It?(this.v=e,this):new It(e)},zl=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,a=n.apply(e,t||[]),s=[];return r=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(u){return function(d){return Promise.resolve(d).then(u,l)}}),r[Symbol.asyncIterator]=function(){return this},r;function o(u,d){a[u]&&(r[u]=function(h){return new Promise(function(m,y){s.push([u,h,m,y])>1||i(u,h)})},d&&(r[u]=d(r[u])))}function i(u,d){try{(h=a[u](d)).value instanceof It?Promise.resolve(h.value.v).then(c,l):p(s[0][2],h)}catch(m){p(s[0][3],m)}var h}function c(u){i("next",u)}function l(u){i("throw",u)}function p(u,d){u(d),s.shift(),s.length&&i(s[0][0],s[0][1])}},Jl=function(e){var t,n;return t={},r("next"),r("throw",function(a){throw a}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(a,s){t[a]=e[a]?function(o){return(n=!n)?{value:It(e[a](o)),done:!1}:s?s(o):o}:s}},Wo=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(a){t[a]=e[a]&&function(s){return new Promise(function(o,i){(function(c,l,p,u){Promise.resolve(u).then(function(d){c({value:d,done:p})},l)})(o,i,(s=e[a](s)).done,s.value)})}}},ot=function(e){return this instanceof ot?(this.v=e,this):new ot(e)},Zl=function(e){var t,n;return t={},r("next"),r("throw",function(a){throw a}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(a,s){t[a]=e[a]?function(o){return(n=!n)?{value:ot(e[a](o)),done:!1}:s?s(o):o}:s}},Ql=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,a=n.apply(e,t||[]),s=[];return r=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(u){return function(d){return Promise.resolve(d).then(u,l)}}),r[Symbol.asyncIterator]=function(){return this},r;function o(u,d){a[u]&&(r[u]=function(h){return new Promise(function(m,y){s.push([u,h,m,y])>1||i(u,h)})},d&&(r[u]=d(r[u])))}function i(u,d){try{(h=a[u](d)).value instanceof ot?Promise.resolve(h.value.v).then(c,l):p(s[0][2],h)}catch(m){p(s[0][3],m)}var h}function c(u){i("next",u)}function l(u){i("throw",u)}function p(u,d){u(d),s.shift(),s.length&&i(s[0][0],s[0][1])}};function eu(e,t){return function(n,r){const a={};for(const s of n.methods){const o=r(s);o!=null&&(a[s.localName]=o)}return a}(e,n=>{switch(n.methodKind){case"unary":return function(r,a){return async function(s,o){var i,c;const l=await r.unary(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,s,o==null?void 0:o.contextValues);return(i=o==null?void 0:o.onHeader)===null||i===void 0||i.call(o,l.header),(c=o==null?void 0:o.onTrailer)===null||c===void 0||c.call(o,l.trailer),l.message}}(t,n);case"server_streaming":return function(r,a){return function(s,o){return ha(r.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,function(i){return zl(this,arguments,function*(){yield It(yield*Jl(Xl(i)))})}([s]),o==null?void 0:o.contextValues),o)}}(t,n);case"client_streaming":return function(r,a){return async function(s,o){var i,c,l,p,u,d;const h=await r.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,s,o==null?void 0:o.contextValues);let m;(u=o==null?void 0:o.onHeader)===null||u===void 0||u.call(o,h.header);let y=0;try{for(var g,v=!0,E=Wo(h.message);!(i=(g=await E.next()).done);v=!0)p=g.value,v=!1,m=p,y++}catch(_){c={error:_}}finally{try{v||i||!(l=E.return)||await l.call(E)}finally{if(c)throw c.error}}if(!m)throw new Oe("protocol error: missing response message",Le.Unimplemented);if(y>1)throw new Oe("protocol error: received extra messages for client streaming method",Le.Unimplemented);return(d=o==null?void 0:o.onTrailer)===null||d===void 0||d.call(o,h.trailer),m}}(t,n);case"bidi_streaming":return function(r,a){return function(s,o){return ha(r.stream(a,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,s,o==null?void 0:o.contextValues),o)}}(t,n);default:return null}})}function ha(e,t){const n=function(){return Ql(this,arguments,function*(){var r,a;const s=yield ot(e);(r=t==null?void 0:t.onHeader)===null||r===void 0||r.call(t,s.header),yield ot(yield*Zl(Wo(s.message))),(a=t==null?void 0:t.onTrailer)===null||a===void 0||a.call(t,s.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>n.next()})}}async function ma(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}for(var Pn=256,tu=[];Pn--;)tu[Pn]=(Pn+256).toString(16).substring(1);function nu(e){throw new Error("Logger not initialized. Call setLibraryLogger() before using getLogger().")}const ru={EXPERIMENT_VIEWED:"experiment_viewed",THREAD_CREATION_ATTEMPTED:"thread_creation_attempted",SEND_ACTION_TRIGGERED:"send_action_triggered",CANCEL_ACTION_TRIGGERED:"cancel_action_triggered",RESEND_ACTION_TRIGGERED:"resend_action_triggered",AGENT_EXECUTION_MODE_TOGGLED:"agent_execution_mode_toggled",MESSAGE_SEND_ERROR_DISPLAYED:"message_send_error_displayed",MESSAGE_SEND_RETRY_CLICKED:"message_send_retry_clicked",MESSAGE_SEND_TIMING:"message_sent_timing",VSCODE_EXTENSION_STARTUP:"vscode_extension_started_up",NOTIFICATION_DISPLAYED:"notification_displayed",NOTIFICATION_DISMISSED:"notification_dismissed",TOOL_CONNECT_BUTTON_CLICKED:"tool_connect_button_clicked",TOOL_CONNECTED:"tool_connected",TURN_SUMMARY_BUTTON_CLICKED:"turn_summary_button_clicked"},_p={OFF:"off"};var Xo=(e=>(e.chat="chat",e))(Xo||{}),su=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(su||{});function fa(e){return e.replace(/^data:.*?;base64,/,"")}async function xn(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=a=>{var s;return t((s=a.target)==null?void 0:s.result)},r.onerror=n,r.readAsDataURL(e)})}async function Ln(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,r=>r.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const r=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));r.onmessage=function(a){a.data.error?n(new Error(a.data.error)):t(a.data),r.terminate()},r.onerror=function(a){n(a.error),r.terminate()},r.postMessage(e)})}function au(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}const ou=au(is("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var U=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e.getSoundSettings="get-sound-settings",e.getSoundSettingsResponse="get-sound-settings-response",e.updateSoundSettings="update-sound-settings",e.soundSettingsBroadcast="sound-settings-broadcast",e.getSwarmModeSettings="get-swarm-mode-settings",e.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",e.updateSwarmModeSettings="update-swarm-mode-settings",e.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",e.getChatModeRequest="get-chat-mode-request",e.getChatModeResponse="get-chat-mode-response",e))(U||{}),ur=(e=>(e.trackAnalyticsEvent="track-analytics-event",e.trackExperimentViewedEvent="track-experiment-viewed-event",e))(ur||{}),zt=(e=>(e.checkToolCallSafeRequest="check-tool-call-safe-request",e.checkToolCallSafeResponse="check-tool-call-safe-response",e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(zt||{}),Jt=(e=>(e.loadConversationExchangesRequest="load-conversation-exchanges-request",e.loadConversationExchangesResponse="load-conversation-exchanges-response",e.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",e.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",e.saveExchangesRequest="save-exchanges-request",e.saveExchangesResponse="save-exchanges-response",e.deleteExchangesRequest="delete-exchanges-request",e.deleteExchangesResponse="delete-exchanges-response",e.deleteConversationExchangesRequest="delete-conversation-exchanges-request",e.deleteConversationExchangesResponse="delete-conversation-exchanges-response",e.countExchangesRequest="count-exchanges-request",e.countExchangesResponse="count-exchanges-response",e))(Jt||{});async function*iu(e,t=1e3){for(;e>0;)yield e,await new Promise(n=>setTimeout(n,Math.min(t,e))),e-=t}class lu{constructor(t,n,r,a=5,s=4e3,o){f(this,"_isCancelled",!1);this.requestId=t,this.chatMessage=n,this.startStreamFn=r,this.maxRetries=a,this.baseDelay=s,this.flags=o}cancel(){this._isCancelled=!0}async*getStream(){let t=0,n=0,r=!1;try{for(;!this._isCancelled;){const a=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let s,o,i=!1,c=!0;for await(const l of a){if(l.status===Z.failed){if(l.isRetriable!==!0||r)return yield l;i=!0,c=l.shouldBackoff??!0,s=l.display_error_message,o=l.request_id;break}r=!0,yield l}if(!i)return;if(this._isCancelled)return yield this.createCancelledStatus();if(t++,t>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${s}`),void(yield{request_id:o??this.requestId,seen_state:be.unseen,status:Z.failed,display_error_message:s,isRetriable:!1});if(c){const l=this.baseDelay*2**n;n++;for await(const p of iu(l))yield{request_id:this.requestId,status:Z.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(p/1e3)} seconds... (Attempt ${t} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:Z.sent,display_error_message:`Generating response... (Attempt ${t+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(a){console.error("Unexpected error in chat stream:",a),yield{request_id:this.requestId,seen_state:be.unseen,status:Z.failed,display_error_message:a instanceof Error?a.message:String(a)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:be.unseen,status:Z.cancelled}}}var Ze=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(Ze||{});class uu{constructor(t){f(this,"getHydratedTask",async t=>{const n={type:Ze.getHydratedTaskRequest,data:{uuid:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.task});f(this,"createTask",async(t,n,r)=>{const a={type:Ze.createTaskRequest,data:{name:t,description:n,parentTaskUuid:r}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.uuid});f(this,"updateTask",async(t,n,r)=>{const a={type:Ze.updateTaskRequest,data:{uuid:t,updates:n,updatedBy:r}};await this._asyncMsgSender.sendToSidecar(a,3e4)});f(this,"setCurrentRootTaskUuid",t=>{const n={type:Ze.setCurrentRootTaskUuid,data:{uuid:t}};this._asyncMsgSender.sendToSidecar(n)});f(this,"updateHydratedTask",async(t,n)=>{const r={type:Ze.updateHydratedTaskRequest,data:{task:t,updatedBy:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data});this._asyncMsgSender=t}}var cu=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(cu||{}),du=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(du||{});function pu(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class zo{constructor(){this.tracingData={flags:{},nums:{},string_stats:{},request_ids:{}}}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:pu(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var hu=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e))(hu||{}),mu=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(mu||{});class Jo extends zo{constructor(){super()}static create(){return new Jo}}var fu=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalCwdNotAbsolute="vs-code-terminal-cwd-not-absolute",e.vsCodeTerminalCwdDoesNotExist="vs-code-terminal-cwd-does-not-exist",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalTimedOutWaitingForSetCwdCommand="vs-code-terminal-timed-out-waiting-for-set-cwd-command",e.vsCodeTerminalTimedOutWaitingForStartupCommand="vs-code-terminal-timed-out-waiting-for-startup-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeScriptStrategyPollingDeterminedProcessIsDone="vs-code-script-strategy-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalErrorCreatingBashEnvironment="vs-code-terminal-error-creating-bash-environment",e.vsCodeTerminalErrorAccessingConfiguration="vs-code-terminal-error-accessing-configuration",e.vsCodeTerminalErrorSavingSettings="vs-code-terminal-error-saving-settings",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.vsCodeTerminalScriptCommandNotAvailable="vs-code-terminal-script-command-not-available",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e.taskListUsage="task-list-usage",e.memoryUsage="memory-usage",e.contentTruncation="content-truncation",e.modelSelectionChange="model-selection-change",e))(fu||{}),gu=(e=>(e.sentUserMessage="sent-user-message",e.chatHistorySummarization="chat-history-summarization",e.enhancedPrompt="enhanced-prompt",e.firstTokenReceived="first-token-received",e.chatHistoryTruncated="chat-history-truncated",e))(gu||{}),yu=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(yu||{});class Zo extends zo{constructor(){super()}static create(){return new Zo}}var bu=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e.modeSelector="mode-selector",e.remoteAgentSetupWindow="remote-agent-setup-window",e.remoteAgentThreadList="remote-agent-thread-list",e.remoteAgentNewThreadButton="remote-agent-new-thread-button",e))(bu||{}),vu=(e=>(e[e.unknownSourceControl=0]="unknownSourceControl",e[e.git=1]="git",e[e.github=2]="github",e))(vu||{}),_u=(e=>(e[e.unknownMode=0]="unknownMode",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(_u||{}),Eu=(e=>(e[e.unknownModeSelectorAction=0]="unknownModeSelectorAction",e[e.open=1]="open",e[e.close=2]="close",e[e.select=3]="select",e[e.init=4]="init",e))(Eu||{}),Su=(e=>(e[e.unknownSetupWindowAction=0]="unknownSetupWindowAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectRepo=3]="selectRepo",e[e.selectBranch=4]="selectBranch",e[e.selectSetupScript=5]="selectSetupScript",e[e.autoGenerateSetupScript=6]="autoGenerateSetupScript",e[e.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",e[e.typeInPromptWindow=8]="typeInPromptWindow",e[e.clickRewritePrompt=9]="clickRewritePrompt",e[e.enableNotifications=10]="enableNotifications",e[e.disableNotifications=11]="disableNotifications",e[e.clickCreateAgent=12]="clickCreateAgent",e))(Su||{}),Tu=(e=>(e[e.unknownAgentListAction=0]="unknownAgentListAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectAgent=3]="selectAgent",e[e.deleteAgent=4]="deleteAgent",e[e.pinAgent=5]="pinAgent",e[e.unpinAgent=6]="unpinAgent",e))(Tu||{}),wu=(e=>(e[e.unknown=0]="unknown",e[e.click=1]="click",e[e.open=2]="open",e[e.close=3]="close",e))(wu||{}),Nu=(e=>(e[e.unknown=0]="unknown",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Nu||{}),Iu=(e=>(e[e.unknown=0]="unknown",e[e.addTask=1]="addTask",e[e.addSubtask=2]="addSubtask",e[e.updateTaskStatus=3]="updateTaskStatus",e[e.updateTaskName=4]="updateTaskName",e[e.updateTaskDescription=5]="updateTaskDescription",e[e.reorganizeTaskList=6]="reorganizeTaskList",e[e.deleteTask=7]="deleteTask",e[e.runSingleTask=8]="runSingleTask",e[e.runAllTasks=9]="runAllTasks",e[e.viewTaskList=10]="viewTaskList",e[e.exportTaskList=11]="exportTaskList",e[e.importTaskList=12]="importTaskList",e[e.syncTaskList=13]="syncTaskList",e))(Iu||{}),ku=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(ku||{}),Ru=(e=>(e[e.unknown=0]="unknown",e[e.saveMemory=1]="saveMemory",e[e.discardMemory=2]="discardMemory",e[e.editMemory=3]="editMemory",e[e.viewMemories=4]="viewMemories",e[e.refreshMemories=5]="refreshMemories",e[e.filterByState=6]="filterByState",e[e.filterByVersion=7]="filterByVersion",e[e.openMemoriesFile=8]="openMemoriesFile",e[e.createMemory=9]="createMemory",e))(Ru||{}),Cu=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(Cu||{}),Au=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(Au||{}),Ou=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(Ou||{});const hs=class hs{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,r=!0){const a=this.extractFrontmatter(t);if(a){const s=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),o=a.match(s);if(o&&o[1])return o[1].toLowerCase()==="true"}return r}static parseString(t,n,r=""){const a=this.extractFrontmatter(t);if(a){const s=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),o=a.match(s);if(o&&o[1])return o[1].trim()}return r}static updateFrontmatter(t,n,r){const a=t.match(this.frontmatterRegex),s=typeof r!="string"||/^(true|false)$/.test(r.toLowerCase())?String(r):`"${r}"`;if(a){const o=a[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(o.match(i)){const c=o.replace(i,`$1${s}`);return t.replace(this.frontmatterRegex,`---
${c}---
`)}{const c=`${o.endsWith(`
`)?o:o+`
`}${n}: ${s}
`;return t.replace(this.frontmatterRegex,`---
${c}---
`)}}return`---
${n}: ${s}
---

${t}`}static createFrontmatter(t,n){let r=t;this.hasFrontmatter(r)&&(r=this.extractContent(r));for(const[a,s]of Object.entries(n))r=this.updateFrontmatter(r,a,s);return r}};hs.frontmatterRegex=/^---\s*\n([\s\S]*?)\n---\s*\n/;let J=hs;const Ve=class Ve{static parseRuleFile(t,n){const r=J.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,""),a=J.extractContent(t);return{type:this.getRuleTypeFromContent(t),path:n,content:a,description:r||void 0}}static formatRuleFileForMarkdown(t){let n=t.content;return n=J.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(t.type)),t.description&&(n=J.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,t.description)),n}static getAlwaysApplyFrontmatterKey(t){return J.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return J.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return J.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(t){return J.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(t,n){return J.updateFrontmatter(t,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(t){switch(t.toLowerCase()){case"always_apply":return me.ALWAYS_ATTACHED;case"manual":return me.MANUAL;case"agent_requested":return me.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(t){switch(t){case me.ALWAYS_ATTACHED:return"always_apply";case me.MANUAL:return"manual";case me.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(t){return this.VALID_TYPE_VALUES.includes(t.toLowerCase())}static getTypeFrontmatterKey(t){return J.parseString(t,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(t,n){const r=this.mapRuleTypeToString(n);return J.updateFrontmatter(t,this.TYPE_FRONTMATTER_KEY,r)}static getRuleTypeFromContent(t){const n=this.getTypeFrontmatterKey(t);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const r=this.getAlwaysApplyFrontmatterKey(t),a=this.getDescriptionFrontmatterKey(t);return r?me.ALWAYS_ATTACHED:a&&a.trim()!==""?me.AGENT_REQUESTED:me.MANUAL}};Ve.ALWAYS_APPLY_FRONTMATTER_KEY="alwaysApply",Ve.DESCRIPTION_FRONTMATTER_KEY="description",Ve.TYPE_FRONTMATTER_KEY="type",Ve.VALID_TYPE_VALUES=["always_apply","manual","agent_requested"],Ve.DEFAULT_RULE_TYPE=me.MANUAL;let cr=Ve;const Mu=".augment",Pu="rules",Ep=".augment-guidelines";function W(e,t){return t in e&&e[t]!==void 0}function xu(e){return W(e,"file")}function Lu(e){return W(e,"recentFile")}function Du(e){return W(e,"folder")}function Fu(e){return W(e,"sourceFolder")}function Sp(e){return W(e,"sourceFolderGroup")}function Tp(e){return W(e,"selection")}function Uu(e){return W(e,"externalSource")}function wp(e){return W(e,"allDefaultContext")}function Np(e){return W(e,"clearContext")}function Ip(e){return W(e,"userGuidelines")}function kp(e){return W(e,"agentMemories")}function $u(e){return W(e,"personality")}function Bu(e){return W(e,"rule")}function Rp(e){return W(e,"task")}const Cp={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Ap={clearContext:!0,label:"Clear Context",id:"clearContext"},Op={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Mp={agentMemories:{},label:"Agent Memories",id:"agentMemories"},ga=[{personality:{type:oe.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:oe.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:oe.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:oe.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Pp(e){return W(e,"group")}function xp(e){const t=new Map;return e.forEach(n=>{xu(n)?t.set("file",[...t.get("file")??[],n]):Lu(n)?t.set("recentFile",[...t.get("recentFile")??[],n]):Du(n)?t.set("folder",[...t.get("folder")??[],n]):Uu(n)?t.set("externalSource",[...t.get("externalSource")??[],n]):Fu(n)?t.set("sourceFolder",[...t.get("sourceFolder")??[],n]):$u(n)?t.set("personality",[...t.get("personality")??[],n]):Bu(n)&&t.set("rule",[...t.get("rule")??[],n])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:t.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:t.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:t.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:t.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:t.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:t.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:t.get("rule")??[]}}].filter(n=>n.group.items.length>0)}function Vu(e){const t=(n={rootPath:e.repoRoot,relPath:e.pathName}).rootPath+"/"+n.relPath;var n;const r={label:Mi(e.pathName).split("/").filter(a=>a.trim()!=="").pop()||"",name:t,id:t};if(e.fullRange){const a=`:L${e.fullRange.startLineNumber}-${e.fullRange.endLineNumber}`;r.label+=a,r.name+=a,r.id+=a}else if(e.range){const a=`:L${e.range.start}-${e.range.stop}`;r.label+=a,r.name+=a,r.id+=a}return r}function Gu(e){const t=e.path.split("/"),n=t[t.length-1],r=n.endsWith(".md")?n.slice(0,-3):n,a=`${Mu}/${Pu}/${e.path}`;return{label:r,name:a,id:a}}function Lp(e,t){const n=e.customPersonalityPrompts;if(n)switch(t){case oe.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case oe.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case oe.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case oe.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return ju[t]}const ju={[oe.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[oe.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[oe.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[oe.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};var ee=(e=>(e.NOT_STARTED="NOT_STARTED",e.IN_PROGRESS="IN_PROGRESS",e.CANCELLED="CANCELLED",e.COMPLETE="COMPLETE",e))(ee||{}),ds=(e=>(e.USER="USER",e.AGENT="AGENT",e))(ds||{}),Qo={},on={},ln={};let Dt;Object.defineProperty(ln,"__esModule",{value:!0}),ln.default=function(){if(!Dt&&(Dt=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Dt))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Dt(qu)};const qu=new Uint8Array(16);var De={},Ye={},un={};Object.defineProperty(un,"__esModule",{value:!0}),un.default=void 0;un.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(Ye,"__esModule",{value:!0}),Ye.default=void 0;var Ft,Yu=(Ft=un)&&Ft.__esModule?Ft:{default:Ft},Ku=function(e){return typeof e=="string"&&Yu.default.test(e)};Ye.default=Ku,Object.defineProperty(De,"__esModule",{value:!0}),De.default=void 0,De.unsafeStringify=ei;var Hu=function(e){return e&&e.__esModule?e:{default:e}}(Ye);const $=[];for(let e=0;e<256;++e)$.push((e+256).toString(16).slice(1));function ei(e,t=0){return $[e[t+0]]+$[e[t+1]]+$[e[t+2]]+$[e[t+3]]+"-"+$[e[t+4]]+$[e[t+5]]+"-"+$[e[t+6]]+$[e[t+7]]+"-"+$[e[t+8]]+$[e[t+9]]+"-"+$[e[t+10]]+$[e[t+11]]+$[e[t+12]]+$[e[t+13]]+$[e[t+14]]+$[e[t+15]]}var Wu=function(e,t=0){const n=ei(e,t);if(!(0,Hu.default)(n))throw TypeError("Stringified UUID is invalid");return n};De.default=Wu,Object.defineProperty(on,"__esModule",{value:!0}),on.default=void 0;var Xu=function(e){return e&&e.__esModule?e:{default:e}}(ln),zu=De;let ya,Dn,Fn=0,Un=0;var Ju=function(e,t,n){let r=t&&n||0;const a=t||new Array(16);let s=(e=e||{}).node||ya,o=e.clockseq!==void 0?e.clockseq:Dn;if(s==null||o==null){const d=e.random||(e.rng||Xu.default)();s==null&&(s=ya=[1|d[0],d[1],d[2],d[3],d[4],d[5]]),o==null&&(o=Dn=16383&(d[6]<<8|d[7]))}let i=e.msecs!==void 0?e.msecs:Date.now(),c=e.nsecs!==void 0?e.nsecs:Un+1;const l=i-Fn+(c-Un)/1e4;if(l<0&&e.clockseq===void 0&&(o=o+1&16383),(l<0||i>Fn)&&e.nsecs===void 0&&(c=0),c>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Fn=i,Un=c,Dn=o,i+=122192928e5;const p=(1e4*(268435455&i)+c)%4294967296;a[r++]=p>>>24&255,a[r++]=p>>>16&255,a[r++]=p>>>8&255,a[r++]=255&p;const u=i/4294967296*1e4&268435455;a[r++]=u>>>8&255,a[r++]=255&u,a[r++]=u>>>24&15|16,a[r++]=u>>>16&255,a[r++]=o>>>8|128,a[r++]=255&o;for(let d=0;d<6;++d)a[r+d]=s[d];return t||(0,zu.unsafeStringify)(a)};on.default=Ju;var cn={},Me={},kt={};Object.defineProperty(kt,"__esModule",{value:!0}),kt.default=void 0;var Zu=function(e){return e&&e.__esModule?e:{default:e}}(Ye),Qu=function(e){if(!(0,Zu.default)(e))throw TypeError("Invalid UUID");let t;const n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};kt.default=Qu,Object.defineProperty(Me,"__esModule",{value:!0}),Me.URL=Me.DNS=void 0,Me.default=function(e,t,n){function r(a,s,o,i){var c;if(typeof a=="string"&&(a=function(p){p=unescape(encodeURIComponent(p));const u=[];for(let d=0;d<p.length;++d)u.push(p.charCodeAt(d));return u}(a)),typeof s=="string"&&(s=(0,tc.default)(s)),((c=s)===null||c===void 0?void 0:c.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+a.length);if(l.set(s),l.set(a,s.length),l=n(l),l[6]=15&l[6]|t,l[8]=63&l[8]|128,o){i=i||0;for(let p=0;p<16;++p)o[i+p]=l[p];return o}return(0,ec.unsafeStringify)(l)}try{r.name=e}catch{}return r.DNS=ti,r.URL=ni,r};var ec=De,tc=function(e){return e&&e.__esModule?e:{default:e}}(kt);const ti="6ba7b810-9dad-11d1-80b4-00c04fd430c8";Me.DNS=ti;const ni="6ba7b811-9dad-11d1-80b4-00c04fd430c8";Me.URL=ni;var dn={};function ba(e){return 14+(e+64>>>9<<4)+1}function Pe(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function Sn(e,t,n,r,a,s){return Pe((o=Pe(Pe(t,e),Pe(r,s)))<<(i=a)|o>>>32-i,n);var o,i}function V(e,t,n,r,a,s,o){return Sn(t&n|~t&r,e,t,a,s,o)}function G(e,t,n,r,a,s,o){return Sn(t&r|n&~r,e,t,a,s,o)}function j(e,t,n,r,a,s,o){return Sn(t^n^r,e,t,a,s,o)}function q(e,t,n,r,a,s,o){return Sn(n^(t|~r),e,t,a,s,o)}Object.defineProperty(dn,"__esModule",{value:!0}),dn.default=void 0;var nc=function(e){if(typeof e=="string"){const t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(t){const n=[],r=32*t.length,a="0123456789abcdef";for(let s=0;s<r;s+=8){const o=t[s>>5]>>>s%32&255,i=parseInt(a.charAt(o>>>4&15)+a.charAt(15&o),16);n.push(i)}return n}(function(t,n){t[n>>5]|=128<<n%32,t[ba(n)-1]=n;let r=1732584193,a=-271733879,s=-1732584194,o=271733878;for(let i=0;i<t.length;i+=16){const c=r,l=a,p=s,u=o;r=V(r,a,s,o,t[i],7,-680876936),o=V(o,r,a,s,t[i+1],12,-389564586),s=V(s,o,r,a,t[i+2],17,606105819),a=V(a,s,o,r,t[i+3],22,-1044525330),r=V(r,a,s,o,t[i+4],7,-176418897),o=V(o,r,a,s,t[i+5],12,1200080426),s=V(s,o,r,a,t[i+6],17,-1473231341),a=V(a,s,o,r,t[i+7],22,-45705983),r=V(r,a,s,o,t[i+8],7,1770035416),o=V(o,r,a,s,t[i+9],12,-1958414417),s=V(s,o,r,a,t[i+10],17,-42063),a=V(a,s,o,r,t[i+11],22,-1990404162),r=V(r,a,s,o,t[i+12],7,1804603682),o=V(o,r,a,s,t[i+13],12,-40341101),s=V(s,o,r,a,t[i+14],17,-1502002290),a=V(a,s,o,r,t[i+15],22,1236535329),r=G(r,a,s,o,t[i+1],5,-165796510),o=G(o,r,a,s,t[i+6],9,-1069501632),s=G(s,o,r,a,t[i+11],14,643717713),a=G(a,s,o,r,t[i],20,-373897302),r=G(r,a,s,o,t[i+5],5,-701558691),o=G(o,r,a,s,t[i+10],9,38016083),s=G(s,o,r,a,t[i+15],14,-660478335),a=G(a,s,o,r,t[i+4],20,-405537848),r=G(r,a,s,o,t[i+9],5,568446438),o=G(o,r,a,s,t[i+14],9,-1019803690),s=G(s,o,r,a,t[i+3],14,-187363961),a=G(a,s,o,r,t[i+8],20,1163531501),r=G(r,a,s,o,t[i+13],5,-1444681467),o=G(o,r,a,s,t[i+2],9,-51403784),s=G(s,o,r,a,t[i+7],14,1735328473),a=G(a,s,o,r,t[i+12],20,-1926607734),r=j(r,a,s,o,t[i+5],4,-378558),o=j(o,r,a,s,t[i+8],11,-2022574463),s=j(s,o,r,a,t[i+11],16,1839030562),a=j(a,s,o,r,t[i+14],23,-35309556),r=j(r,a,s,o,t[i+1],4,-1530992060),o=j(o,r,a,s,t[i+4],11,1272893353),s=j(s,o,r,a,t[i+7],16,-155497632),a=j(a,s,o,r,t[i+10],23,-1094730640),r=j(r,a,s,o,t[i+13],4,681279174),o=j(o,r,a,s,t[i],11,-358537222),s=j(s,o,r,a,t[i+3],16,-722521979),a=j(a,s,o,r,t[i+6],23,76029189),r=j(r,a,s,o,t[i+9],4,-640364487),o=j(o,r,a,s,t[i+12],11,-421815835),s=j(s,o,r,a,t[i+15],16,530742520),a=j(a,s,o,r,t[i+2],23,-995338651),r=q(r,a,s,o,t[i],6,-198630844),o=q(o,r,a,s,t[i+7],10,1126891415),s=q(s,o,r,a,t[i+14],15,-1416354905),a=q(a,s,o,r,t[i+5],21,-57434055),r=q(r,a,s,o,t[i+12],6,1700485571),o=q(o,r,a,s,t[i+3],10,-1894986606),s=q(s,o,r,a,t[i+10],15,-1051523),a=q(a,s,o,r,t[i+1],21,-2054922799),r=q(r,a,s,o,t[i+8],6,1873313359),o=q(o,r,a,s,t[i+15],10,-30611744),s=q(s,o,r,a,t[i+6],15,-1560198380),a=q(a,s,o,r,t[i+13],21,1309151649),r=q(r,a,s,o,t[i+4],6,-145523070),o=q(o,r,a,s,t[i+11],10,-1120210379),s=q(s,o,r,a,t[i+2],15,718787259),a=q(a,s,o,r,t[i+9],21,-343485551),r=Pe(r,c),a=Pe(a,l),s=Pe(s,p),o=Pe(o,u)}return[r,a,s,o]}(function(t){if(t.length===0)return[];const n=8*t.length,r=new Uint32Array(ba(n));for(let a=0;a<n;a+=8)r[a>>5]|=(255&t[a/8])<<a%32;return r}(e),8*e.length))};dn.default=nc,Object.defineProperty(cn,"__esModule",{value:!0}),cn.default=void 0;var rc=ri(Me),sc=ri(dn);function ri(e){return e&&e.__esModule?e:{default:e}}var ac=(0,rc.default)("v3",48,sc.default);cn.default=ac;var pn={},hn={};Object.defineProperty(hn,"__esModule",{value:!0}),hn.default=void 0;var oc={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};hn.default=oc,Object.defineProperty(pn,"__esModule",{value:!0}),pn.default=void 0;var va=si(hn),ic=si(ln),lc=De;function si(e){return e&&e.__esModule?e:{default:e}}var uc=function(e,t,n){if(va.default.randomUUID&&!t&&!e)return va.default.randomUUID();const r=(e=e||{}).random||(e.rng||ic.default)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(let a=0;a<16;++a)t[n+a]=r[a];return t}return(0,lc.unsafeStringify)(r)};pn.default=uc;var mn={},fn={};function cc(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function $n(e,t){return e<<t|e>>>32-t}Object.defineProperty(fn,"__esModule",{value:!0}),fn.default=void 0;var dc=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){const o=unescape(encodeURIComponent(e));e=[];for(let i=0;i<o.length;++i)e.push(o.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const r=e.length/4+2,a=Math.ceil(r/16),s=new Array(a);for(let o=0;o<a;++o){const i=new Uint32Array(16);for(let c=0;c<16;++c)i[c]=e[64*o+4*c]<<24|e[64*o+4*c+1]<<16|e[64*o+4*c+2]<<8|e[64*o+4*c+3];s[o]=i}s[a-1][14]=8*(e.length-1)/Math.pow(2,32),s[a-1][14]=Math.floor(s[a-1][14]),s[a-1][15]=8*(e.length-1)&4294967295;for(let o=0;o<a;++o){const i=new Uint32Array(80);for(let h=0;h<16;++h)i[h]=s[o][h];for(let h=16;h<80;++h)i[h]=$n(i[h-3]^i[h-8]^i[h-14]^i[h-16],1);let c=n[0],l=n[1],p=n[2],u=n[3],d=n[4];for(let h=0;h<80;++h){const m=Math.floor(h/20),y=$n(c,5)+cc(m,l,p,u)+d+t[m]+i[h]>>>0;d=u,u=p,p=$n(l,30)>>>0,l=c,c=y}n[0]=n[0]+c>>>0,n[1]=n[1]+l>>>0,n[2]=n[2]+p>>>0,n[3]=n[3]+u>>>0,n[4]=n[4]+d>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};fn.default=dc,Object.defineProperty(mn,"__esModule",{value:!0}),mn.default=void 0;var pc=ai(Me),hc=ai(fn);function ai(e){return e&&e.__esModule?e:{default:e}}var mc=(0,pc.default)("v5",80,hc.default);mn.default=mc;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;gn.default="00000000-0000-0000-0000-000000000000";var yn={};Object.defineProperty(yn,"__esModule",{value:!0}),yn.default=void 0;var fc=function(e){return e&&e.__esModule?e:{default:e}}(Ye),gc=function(e){if(!(0,fc.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};function dr(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}yn.default=gc,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return o.default}});var t=p(on),n=p(cn),r=p(pn),a=p(mn),s=p(gn),o=p(yn),i=p(Ye),c=p(De),l=p(kt);function p(u){return u&&u.__esModule?u:{default:u}}}(Qo),dr.prototype.convert=function(e){var t,n,r,a={},s=this.srcAlphabet.length,o=this.dstAlphabet.length,i=e.length,c=typeof e=="string"?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<i;t++)a[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,r=0,t=0;t<i;t++)(n=n*s+a[t])>=o?(a[r++]=parseInt(n/o,10),n%=o):r>0&&(a[r++]=0);i=r,c=this.dstAlphabet.slice(n,n+1).concat(c)}while(r!==0);return c},dr.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(this.srcAlphabet.indexOf(e[t])===-1)return!1;return!0};var yc=dr;function ht(e,t){var n=new yc(e,t);return function(r){return n.convert(r)}}ht.BIN="01",ht.OCT="01234567",ht.DEC="0123456789",ht.HEX="0123456789abcdef";var bc=ht;const{v4:Bn,validate:vc}=Qo,Ut=bc,Vn={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},_c={consistentLength:!0};let Gn;const _a=(e,t,n)=>{const r=t(e.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?r.padStart(n.shortIdLength,n.paddingChar):r},Ea=(e,t)=>{const n=t(e).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var Ec=(()=>{const e=(t,n)=>{const r=t||Vn.flickrBase58,a={..._c,...n};if([...new Set(Array.from(r))].length!==r.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const s=(o=r.length,Math.ceil(Math.log(2**128)/Math.log(o)));var o;const i={shortIdLength:s,consistentLength:a.consistentLength,paddingChar:r[0]},c=Ut(Ut.HEX,r),l=Ut(r,Ut.HEX),p=()=>_a(Bn(),c,i),u={alphabet:r,fromUUID:d=>_a(d,c,i),maxLength:s,generate:p,new:p,toUUID:d=>Ea(d,l),uuid:Bn,validate:(d,h=!1)=>{if(!d||typeof d!="string")return!1;const m=a.consistentLength?d.length===s:d.length<=s,y=d.split("").every(g=>r.includes(g));return h===!1?m&&y:m&&y&&vc(Ea(d,l))}};return Object.freeze(u),u};return e.constants=Vn,e.uuid=Bn,e.generate=()=>(Gn||(Gn=e(Vn.flickrBase58).generate),Gn()),e})();const Sc=Vr(Ec),oi={[ee.NOT_STARTED]:"[ ]",[ee.IN_PROGRESS]:"[/]",[ee.COMPLETE]:"[x]",[ee.CANCELLED]:"[-]"},ii=Sc(void 0,{consistentLength:!0});function Tc(e,t){if(e.uuid===t)return e;if(e.subTasksData)for(const n of e.subTasksData){const r=Tc(n,t);if(r)return r}}function li(e,t={}){const{shallow:n=!1,excludeUuid:r=!1,shortUuid:a=!0}=t;return ui(e,{shallow:n,excludeUuid:r,shortUuid:a}).join(`
`)}function ui(e,t={}){const{shallow:n=!1,excludeUuid:r=!1,shortUuid:a=!0}=t;let s="";r||(s=`UUID:${a?function(i){try{return ii.fromUUID(i)}catch{return i}}(e.uuid):e.uuid} `);const o=`${oi[e.state]} ${s}NAME:${e.name} DESCRIPTION:${e.description}`;return n||!e.subTasksData||e.subTasksData.length===0?[o]:[o,...(e.subTasksData||[]).map(i=>ui(i,t).map(c=>`-${c}`)).flat()]}function wc(e,t){var r;const n=(r=e.subTasksData)==null?void 0:r.map(a=>wc(a,t));return{...e,uuid:t!=null&&t.keepUuid?e.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(a=>a.uuid))||[],subTasksData:n}}function Dp(e,t={}){if(!e.trim())throw new Error("Empty markdown");const n=e.split(`
`);let r=0;for(const l of n)if(l.trim()&&Sa(l)===0)try{pr(l,t),r++}catch{}if(r===0)throw new Error("No root task found");if(r>1)throw new Error(`Multiple root tasks found (${r}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const a=e.split(`
`);function s(){for(;a.length>0;){const l=a.shift(),p=Sa(l);try{return{task:pr(l,t),level:p}}catch{}}}const o=s();if(!o)throw new Error("No root task found");const i=[o.task];let c;for(;c=s();){const l=i[c.level-1];if(!l)throw new Error(`Invalid markdown: level ${c.level+1} has no parent
Line: ${c.task.name} is missing a parent
Current tasks: 
${li(o.task)}`);l.subTasksData&&l.subTasks||(l.subTasks=[],l.subTasksData=[]),l.subTasksData.push(c.task),l.subTasks.push(c.task.uuid),i[c.level]=c.task,i.splice(c.level+1)}return o.task}function Sa(e){let t=0,n=0;for(;n<e.length&&(e[n]===" "||e[n]==="	");)e[n]===" "?t+=.5:e[n]==="	"&&(t+=1),n++;for(;n<e.length&&e[n]==="-";)t+=1,n++;return Math.floor(t)}function pr(e,t={}){const{excludeUuid:n=!1,shortUuid:r=!0}=t;let a=0;for(;a<e.length&&(e[a]===" "||e[a]==="	"||e[a]==="-");)a++;const s=e.substring(a),o=s.match(/^\s*\[([ x\-/?])\]/);if(!o)throw new Error(`Invalid task line: ${e} (missing state)`);const i=o[1],c=Object.entries(oi).reduce((h,[m,y])=>(h[y.substring(1,2)]=m,h),{})[i]||ee.NOT_STARTED,l=s.substring(o.index+o[0].length).trim();let p,u,d;if(n){const h=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=l.match(h);if(!m){const y=/\b(?:name|NAME):/i.test(l),g=/\b(?:description|DESCRIPTION):/i.test(l);throw!y||!g?new Error(`Invalid task line: ${e} (missing required fields)`):l.toLowerCase().indexOf("name:")<l.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(u=m[1].trim(),d=m[2].trim(),!u)throw new Error(`Invalid task line: ${e} (missing required fields)`);p=crypto.randomUUID()}else{const h=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=l.match(h);if(!m){const y=/\b(?:uuid|UUID):/i.test(l),g=/\b(?:name|NAME):/i.test(l),v=/\b(?:description|DESCRIPTION):/i.test(l);if(!y||!g||!v)throw new Error(`Invalid task line: ${e} (missing required fields)`);const E=l.toLowerCase().indexOf("uuid:"),_=l.toLowerCase().indexOf("name:"),T=l.toLowerCase().indexOf("description:");throw E<_&&_<T?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(p=m[1].trim(),u=m[2].trim(),d=m[3].trim(),!p||!u)throw new Error(`Invalid task line: ${e} (missing required fields)`);if(p==="NEW_UUID")p=crypto.randomUUID();else if(r)try{p=function(y){try{return ii.toUUID(y)}catch{return y}}(p)}catch{}}return{uuid:p,name:u,description:d,state:c,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:ds.USER}}const it=e=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:ee.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:ds.USER,...e}),Ta=it({name:"Task 1.1",description:"This is the first sub task",state:ee.IN_PROGRESS}),wa=it({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:ee.NOT_STARTED}),Na=it({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:ee.IN_PROGRESS}),Ia=it({name:"Task 1.2",description:"This is the second sub task",state:ee.COMPLETE,subTasks:[wa.uuid,Na.uuid],subTasksData:[wa,Na]}),ka=it({name:"Task 1.3",description:"This is the third sub task",state:ee.CANCELLED}),Fp=li(it({name:"Task 1",description:"This is the first task",state:ee.NOT_STARTED,subTasks:[Ta.uuid,Ia.uuid,ka.uuid],subTasksData:[Ta,Ia,ka]}));function ci(e){const t=e.split(`
`);let n=null;const r={created:[],updated:[],deleted:[]};for(const a of t){const s=a.trim();if(s!=="## Created Tasks")if(s!=="## Updated Tasks")if(s!=="## Deleted Tasks"){if(n&&(s.startsWith("[ ]")||s.startsWith("[/]")||s.startsWith("[x]")||s.startsWith("[-]")))try{const o=pr(s,{excludeUuid:!1,shortUuid:!0});o&&r[n].push(o)}catch{}}else n="deleted";else n="updated";else n="created"}return r}function Up(e){const t=e.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const n=ci(di(e));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function di(e){const t=e.indexOf("# Task Changes");if(t===-1)return"";const n=e.substring(t),r=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let a=n.length;for(const i of r){const c=n.indexOf(i);c!==-1&&c<a&&(a=c)}const s=n.substring(0,a),o=s.indexOf(`
`);return o===-1?"":s.substring(o+1).trim()}function $p(e){return ci(di(e))}class Bp{static getTaskOrchestratorPrompt(t){const{taskTree:n,surroundingContext:r}=t,a=this.buildTaskContext(n,r);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${a}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(t){return`task:${t.taskUuid}:${t.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(t){const{taskTree:n,surroundingContext:r}=t;return r.targetTaskPath.length>1?`${r.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(t,n){const{rootTask:r,targetTaskPath:a}=n;let s=`This task is part of a larger project: "${r.name}"`;return r.description&&(s+=`

**Project Description:** ${r.description}`),a.length>1&&(s+=`

**Task Path:** ${a.join(" → ")}`),t.subTasksData&&t.subTasksData.length>0&&(s+=`

**Subtasks:**`,t.subTasksData.forEach((o,i)=>{s+=`
${i+1}. ${o.name} (${o.state})`,o.description&&(s+=` - ${o.description}`)})),s}static parseJsonTaskList(t){try{const n=JSON.parse(t);return Array.isArray(n)?n:this.flattenTaskTree(n)}catch{return[]}}static flattenTaskTree(t,n=[]){if(n.push({uuid:t.uuid,name:t.name,description:t.description,state:t.state,level:t.level}),t.subTasks)for(const r of t.subTasks)this.flattenTaskTree(r,n);return n}}var hr={exports:{}},mr={exports:{}},le={},O={__esModule:!0};O.extend=Ra,O.indexOf=function(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1},O.escapeExpression=function(e){if(typeof e!="string"){if(e&&e.toHTML)return e.toHTML();if(e==null)return"";if(!e)return e+"";e=""+e}return kc.test(e)?e.replace(Ic,Rc):e},O.isEmpty=function(e){return!e&&e!==0||!(!pi(e)||e.length!==0)},O.createFrame=function(e){var t=Ra({},e);return t._parent=e,t},O.blockParams=function(e,t){return e.path=t,e},O.appendContextPath=function(e,t){return(e?e+".":"")+t};var Nc={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},Ic=/[&<>"'`=]/g,kc=/[&<>"'`=]/;function Rc(e){return Nc[e]}function Ra(e){for(var t=1;t<arguments.length;t++)for(var n in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],n)&&(e[n]=arguments[t][n]);return e}var ps=Object.prototype.toString;O.toString=ps;var jn=function(e){return typeof e=="function"};jn(/x/)&&(O.isFunction=jn=function(e){return typeof e=="function"&&ps.call(e)==="[object Function]"}),O.isFunction=jn;var pi=Array.isArray||function(e){return!(!e||typeof e!="object")&&ps.call(e)==="[object Array]"};O.isArray=pi;var fr={exports:{}};(function(e,t){t.__esModule=!0;var n=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function r(a,s){var o=s&&s.loc,i=void 0,c=void 0,l=void 0,p=void 0;o&&(i=o.start.line,c=o.end.line,l=o.start.column,p=o.end.column,a+=" - "+i+":"+l);for(var u=Error.prototype.constructor.call(this,a),d=0;d<n.length;d++)this[n[d]]=u[n[d]];Error.captureStackTrace&&Error.captureStackTrace(this,r);try{o&&(this.lineNumber=i,this.endLineNumber=c,Object.defineProperty?(Object.defineProperty(this,"column",{value:l,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:p,enumerable:!0})):(this.column=l,this.endColumn=p))}catch{}}r.prototype=new Error,t.default=r,e.exports=t.default})(fr,fr.exports);var he=fr.exports,_t={},gr={exports:{}};(function(e,t){t.__esModule=!0;var n=O;t.default=function(r){r.registerHelper("blockHelperMissing",function(a,s){var o=s.inverse,i=s.fn;if(a===!0)return i(this);if(a===!1||a==null)return o(this);if(n.isArray(a))return a.length>0?(s.ids&&(s.ids=[s.name]),r.helpers.each(a,s)):o(this);if(s.data&&s.ids){var c=n.createFrame(s.data);c.contextPath=n.appendContextPath(s.data.contextPath,s.name),s={data:c}}return i(a,s)})},e.exports=t.default})(gr,gr.exports);var Cc=gr.exports,yr={exports:{}};(function(e,t){t.__esModule=!0;var n=O,r=function(a){return a&&a.__esModule?a:{default:a}}(he);t.default=function(a){a.registerHelper("each",function(s,o){if(!o)throw new r.default("Must pass iterator to #each");var i,c=o.fn,l=o.inverse,p=0,u="",d=void 0,h=void 0;function m(_,T,w){d&&(d.key=_,d.index=T,d.first=T===0,d.last=!!w,h&&(d.contextPath=h+_)),u+=c(s[_],{data:d,blockParams:n.blockParams([s[_],_],[h+_,null])})}if(o.data&&o.ids&&(h=n.appendContextPath(o.data.contextPath,o.ids[0])+"."),n.isFunction(s)&&(s=s.call(this)),o.data&&(d=n.createFrame(o.data)),s&&typeof s=="object")if(n.isArray(s))for(var y=s.length;p<y;p++)p in s&&m(p,p,p===s.length-1);else if(typeof Symbol=="function"&&s[Symbol.iterator]){for(var g=[],v=s[Symbol.iterator](),E=v.next();!E.done;E=v.next())g.push(E.value);for(y=(s=g).length;p<y;p++)m(p,p,p===s.length-1)}else i=void 0,Object.keys(s).forEach(function(_){i!==void 0&&m(i,p-1),i=_,p++}),i!==void 0&&m(i,p-1,!0);return p===0&&(u=l(this)),u})},e.exports=t.default})(yr,yr.exports);var Ac=yr.exports,br={exports:{}};(function(e,t){t.__esModule=!0;var n=function(r){return r&&r.__esModule?r:{default:r}}(he);t.default=function(r){r.registerHelper("helperMissing",function(){if(arguments.length!==1)throw new n.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})},e.exports=t.default})(br,br.exports);var Oc=br.exports,vr={exports:{}};(function(e,t){t.__esModule=!0;var n=O,r=function(a){return a&&a.__esModule?a:{default:a}}(he);t.default=function(a){a.registerHelper("if",function(s,o){if(arguments.length!=2)throw new r.default("#if requires exactly one argument");return n.isFunction(s)&&(s=s.call(this)),!o.hash.includeZero&&!s||n.isEmpty(s)?o.inverse(this):o.fn(this)}),a.registerHelper("unless",function(s,o){if(arguments.length!=2)throw new r.default("#unless requires exactly one argument");return a.helpers.if.call(this,s,{fn:o.inverse,inverse:o.fn,hash:o.hash})})},e.exports=t.default})(vr,vr.exports);var Ca,qn,Mc=vr.exports,_r={exports:{}};Ca=_r,(qn=_r.exports).__esModule=!0,qn.default=function(e){e.registerHelper("log",function(){for(var t=[void 0],n=arguments[arguments.length-1],r=0;r<arguments.length-1;r++)t.push(arguments[r]);var a=1;n.hash.level!=null?a=n.hash.level:n.data&&n.data.level!=null&&(a=n.data.level),t[0]=a,e.log.apply(e,t)})},Ca.exports=qn.default;var Pc=_r.exports,Er={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){n.registerHelper("lookup",function(r,a,s){return r&&s.lookupProperty(r,a)})},e.exports=t.default})(Er,Er.exports);var xc=Er.exports,Sr={exports:{}};(function(e,t){t.__esModule=!0;var n=O,r=function(a){return a&&a.__esModule?a:{default:a}}(he);t.default=function(a){a.registerHelper("with",function(s,o){if(arguments.length!=2)throw new r.default("#with requires exactly one argument");n.isFunction(s)&&(s=s.call(this));var i=o.fn;if(n.isEmpty(s))return o.inverse(this);var c=o.data;return o.data&&o.ids&&((c=n.createFrame(o.data)).contextPath=n.appendContextPath(o.data.contextPath,o.ids[0])),i(s,{data:c,blockParams:n.blockParams([s],[c&&c.contextPath])})})},e.exports=t.default})(Sr,Sr.exports);var Lc=Sr.exports;function Ke(e){return e&&e.__esModule?e:{default:e}}_t.__esModule=!0,_t.registerDefaultHelpers=function(e){Dc.default(e),Fc.default(e),Uc.default(e),$c.default(e),Bc.default(e),Vc.default(e),Gc.default(e)},_t.moveHelperToHooks=function(e,t,n){e.helpers[t]&&(e.hooks[t]=e.helpers[t],n||delete e.helpers[t])};var Dc=Ke(Cc),Fc=Ke(Ac),Uc=Ke(Oc),$c=Ke(Mc),Bc=Ke(Pc),Vc=Ke(xc),Gc=Ke(Lc),Tr={},wr={exports:{}};(function(e,t){t.__esModule=!0;var n=O;t.default=function(r){r.registerDecorator("inline",function(a,s,o,i){var c=a;return s.partials||(s.partials={},c=function(l,p){var u=o.partials;o.partials=n.extend({},u,s.partials);var d=a(l,p);return o.partials=u,d}),s.partials[i.args[0]]=i.fn,c})},e.exports=t.default})(wr,wr.exports);var jc=wr.exports;Tr.__esModule=!0,Tr.registerDefaultDecorators=function(e){qc.default(e)};var qc=function(e){return e&&e.__esModule?e:{default:e}}(jc),Nr={exports:{}};(function(e,t){t.__esModule=!0;var n=O,r={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(a){if(typeof a=="string"){var s=n.indexOf(r.methodMap,a.toLowerCase());a=s>=0?s:parseInt(a,10)}return a},log:function(a){if(a=r.lookupLevel(a),typeof console<"u"&&r.lookupLevel(r.level)<=a){var s=r.methodMap[a];console[s]||(s="log");for(var o=arguments.length,i=Array(o>1?o-1:0),c=1;c<o;c++)i[c-1]=arguments[c];console[s].apply(console,i)}}};t.default=r,e.exports=t.default})(Nr,Nr.exports);var hi=Nr.exports,Qe={},Yc={__esModule:!0,createNewLookupObject:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Kc.extend.apply(void 0,[Object.create(null)].concat(t))}},Kc=O;Qe.__esModule=!0,Qe.createProtoAccessControl=function(e){var t=Object.create(null);t.constructor=!1,t.__defineGetter__=!1,t.__defineSetter__=!1,t.__lookupGetter__=!1;var n=Object.create(null);return n.__proto__=!1,{properties:{whitelist:Aa.createNewLookupObject(n,e.allowedProtoProperties),defaultValue:e.allowProtoPropertiesByDefault},methods:{whitelist:Aa.createNewLookupObject(t,e.allowedProtoMethods),defaultValue:e.allowProtoMethodsByDefault}}},Qe.resultIsAllowed=function(e,t,n){return Wc(typeof e=="function"?t.methods:t.properties,n)},Qe.resetLoggedProperties=function(){Object.keys(bn).forEach(function(e){delete bn[e]})};var Aa=Yc,Hc=function(e){return e&&e.__esModule?e:{default:e}}(hi),bn=Object.create(null);function Wc(e,t){return e.whitelist[t]!==void 0?e.whitelist[t]===!0:e.defaultValue!==void 0?e.defaultValue:(function(n){bn[n]!==!0&&(bn[n]=!0,Hc.default.log("error",'Handlebars: Access has been denied to resolve the property "'+n+`" because it is not an "own property" of its parent.
You can add a runtime option to disable the check or this warning:
See https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details`))}(t),!1)}function mi(e){return e&&e.__esModule?e:{default:e}}le.__esModule=!0,le.HandlebarsEnvironment=Ir;var Be=O,Yn=mi(he),Xc=_t,zc=Tr,vn=mi(hi),Jc=Qe;le.VERSION="4.7.8";le.COMPILER_REVISION=8;le.LAST_COMPATIBLE_COMPILER_REVISION=7;le.REVISION_CHANGES={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};var Kn="[object Object]";function Ir(e,t,n){this.helpers=e||{},this.partials=t||{},this.decorators=n||{},Xc.registerDefaultHelpers(this),zc.registerDefaultDecorators(this)}Ir.prototype={constructor:Ir,logger:vn.default,log:vn.default.log,registerHelper:function(e,t){if(Be.toString.call(e)===Kn){if(t)throw new Yn.default("Arg not supported with multiple helpers");Be.extend(this.helpers,e)}else this.helpers[e]=t},unregisterHelper:function(e){delete this.helpers[e]},registerPartial:function(e,t){if(Be.toString.call(e)===Kn)Be.extend(this.partials,e);else{if(t===void 0)throw new Yn.default('Attempting to register a partial called "'+e+'" as undefined');this.partials[e]=t}},unregisterPartial:function(e){delete this.partials[e]},registerDecorator:function(e,t){if(Be.toString.call(e)===Kn){if(t)throw new Yn.default("Arg not supported with multiple decorators");Be.extend(this.decorators,e)}else this.decorators[e]=t},unregisterDecorator:function(e){delete this.decorators[e]},resetLoggedPropertyAccesses:function(){Jc.resetLoggedProperties()}};var Zc=vn.default.log;le.log=Zc,le.createFrame=Be.createFrame,le.logger=vn.default;var kr={exports:{}};(function(e,t){function n(r){this.string=r}t.__esModule=!0,n.prototype.toString=n.prototype.toHTML=function(){return""+this.string},t.default=n,e.exports=t.default})(kr,kr.exports);var Qc=kr.exports,Ce={},Rr={};Rr.__esModule=!0,Rr.wrapHelper=function(e,t){return typeof e!="function"?e:function(){return arguments[arguments.length-1]=t(arguments[arguments.length-1]),e.apply(this,arguments)}},Ce.__esModule=!0,Ce.checkRevision=function(e){var t=e&&e[0]||1,n=Te.COMPILER_REVISION;if(!(t>=Te.LAST_COMPATIBLE_COMPILER_REVISION&&t<=Te.COMPILER_REVISION)){if(t<Te.LAST_COMPATIBLE_COMPILER_REVISION){var r=Te.REVISION_CHANGES[n],a=Te.REVISION_CHANGES[t];throw new Se.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+r+") or downgrade your runtime to an older version ("+a+").")}throw new Se.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+e[1]+").")}},Ce.template=function(e,t){if(!t)throw new Se.default("No environment passed to template");if(!e||!e.main)throw new Se.default("Unknown template object: "+typeof e);e.main.decorator=e.main_d,t.VM.checkRevision(e.compiler);var n=e.compiler&&e.compiler[0]===7,r={strict:function(s,o,i){if(!s||!(o in s))throw new Se.default('"'+o+'" not defined in '+s,{loc:i});return r.lookupProperty(s,o)},lookupProperty:function(s,o){var i=s[o];return i==null||Object.prototype.hasOwnProperty.call(s,o)||Ma.resultIsAllowed(i,r.protoAccessControl,o)?i:void 0},lookup:function(s,o){for(var i=s.length,c=0;c<i;c++)if((s[c]&&r.lookupProperty(s[c],o))!=null)return s[c][o]},lambda:function(s,o){return typeof s=="function"?s.call(o):s},escapeExpression:Ne.escapeExpression,invokePartial:function(s,o,i){i.hash&&(o=Ne.extend({},o,i.hash),i.ids&&(i.ids[0]=!0)),s=t.VM.resolvePartial.call(this,s,o,i);var c=Ne.extend({},i,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),l=t.VM.invokePartial.call(this,s,o,c);if(l==null&&t.compile&&(i.partials[i.name]=t.compile(s,e.compilerOptions,t),l=i.partials[i.name](o,c)),l!=null){if(i.indent){for(var p=l.split(`
`),u=0,d=p.length;u<d&&(p[u]||u+1!==d);u++)p[u]=i.indent+p[u];l=p.join(`
`)}return l}throw new Se.default("The partial "+i.name+" could not be compiled when running in runtime-only mode")},fn:function(s){var o=e[s];return o.decorator=e[s+"_d"],o},programs:[],program:function(s,o,i,c,l){var p=this.programs[s],u=this.fn(s);return o||l||c||i?p=$t(this,s,u,o,i,c,l):p||(p=this.programs[s]=$t(this,s,u)),p},data:function(s,o){for(;s&&o--;)s=s._parent;return s},mergeIfNeeded:function(s,o){var i=s||o;return s&&o&&s!==o&&(i=Ne.extend({},o,s)),i},nullContext:Object.seal({}),noop:t.VM.noop,compilerInfo:e.compiler};function a(s){var o=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],i=o.data;a._setup(o),!o.partial&&e.useData&&(i=function(u,d){return d&&"root"in d||((d=d?Te.createFrame(d):{}).root=u),d}(s,i));var c=void 0,l=e.useBlockParams?[]:void 0;function p(u){return""+e.main(r,u,r.helpers,r.partials,i,l,c)}return e.useDepths&&(c=o.depths?s!=o.depths[0]?[s].concat(o.depths):o.depths:[s]),(p=fi(e.main,p,r,o.depths||[],i,l))(s,o)}return a.isTop=!0,a._setup=function(s){if(s.partial)r.protoAccessControl=s.protoAccessControl,r.helpers=s.helpers,r.partials=s.partials,r.decorators=s.decorators,r.hooks=s.hooks;else{var o=Ne.extend({},t.helpers,s.helpers);(function(c,l){Object.keys(c).forEach(function(p){var u=c[p];c[p]=function(d,h){var m=h.lookupProperty;return ed.wrapHelper(d,function(y){return Ne.extend({lookupProperty:m},y)})}(u,l)})})(o,r),r.helpers=o,e.usePartial&&(r.partials=r.mergeIfNeeded(s.partials,t.partials)),(e.usePartial||e.useDecorators)&&(r.decorators=Ne.extend({},t.decorators,s.decorators)),r.hooks={},r.protoAccessControl=Ma.createProtoAccessControl(s);var i=s.allowCallsToHelperMissing||n;Oa.moveHelperToHooks(r,"helperMissing",i),Oa.moveHelperToHooks(r,"blockHelperMissing",i)}},a._child=function(s,o,i,c){if(e.useBlockParams&&!i)throw new Se.default("must pass block params");if(e.useDepths&&!c)throw new Se.default("must pass parent depths");return $t(r,s,e[s],o,0,i,c)},a},Ce.wrapProgram=$t,Ce.resolvePartial=function(e,t,n){return e?e.call||n.name||(n.name=e,e=n.partials[e]):e=n.name==="@partial-block"?n.data["partial-block"]:n.partials[n.name],e},Ce.invokePartial=function(e,t,n){var r=n.data&&n.data["partial-block"];n.partial=!0,n.ids&&(n.data.contextPath=n.ids[0]||n.data.contextPath);var a=void 0;if(n.fn&&n.fn!==Pa&&function(){n.data=Te.createFrame(n.data);var s=n.fn;a=n.data["partial-block"]=function(o){var i=arguments.length<=1||arguments[1]===void 0?{}:arguments[1];return i.data=Te.createFrame(i.data),i.data["partial-block"]=r,s(o,i)},s.partials&&(n.partials=Ne.extend({},n.partials,s.partials))}(),e===void 0&&a&&(e=a),e===void 0)throw new Se.default("The partial "+n.name+" could not be found");if(e instanceof Function)return e(t,n)},Ce.noop=Pa;var Ne=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(O),Se=function(e){return e&&e.__esModule?e:{default:e}}(he),Te=le,Oa=_t,ed=Rr,Ma=Qe;function $t(e,t,n,r,a,s,o){function i(c){var l=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],p=o;return!o||c==o[0]||c===e.nullContext&&o[0]===null||(p=[c].concat(o)),n(e,c,e.helpers,e.partials,l.data||r,s&&[l.blockParams].concat(s),p)}return(i=fi(n,i,e,o,r,s)).program=t,i.depth=o?o.length:0,i.blockParams=a||0,i}function Pa(){return""}function fi(e,t,n,r,a,s){if(e.decorator){var o={};t=e.decorator(t,o,n,r&&r[0],a,s,r),Ne.extend(t,o)}return t}var Cr={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){typeof globalThis!="object"&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__);var r=globalThis.Handlebars;n.noConflict=function(){return globalThis.Handlebars===n&&(globalThis.Handlebars=r),n}},e.exports=t.default})(Cr,Cr.exports);var gi=Cr.exports;(function(e,t){function n(d){return d&&d.__esModule?d:{default:d}}function r(d){if(d&&d.__esModule)return d;var h={};if(d!=null)for(var m in d)Object.prototype.hasOwnProperty.call(d,m)&&(h[m]=d[m]);return h.default=d,h}t.__esModule=!0;var a=r(le),s=n(Qc),o=n(he),i=r(O),c=r(Ce),l=n(gi);function p(){var d=new a.HandlebarsEnvironment;return i.extend(d,a),d.SafeString=s.default,d.Exception=o.default,d.Utils=i,d.escapeExpression=i.escapeExpression,d.VM=c,d.template=function(h){return c.template(h,d)},d}var u=p();u.create=p,l.default(u),u.default=u,t.default=u,e.exports=t.default})(mr,mr.exports);var td=mr.exports,Ar={exports:{}};(function(e,t){t.__esModule=!0;var n={helpers:{helperExpression:function(r){return r.type==="SubExpression"||(r.type==="MustacheStatement"||r.type==="BlockStatement")&&!!(r.params&&r.params.length||r.hash)},scopedId:function(r){return/^\.|this\b/.test(r.original)},simpleId:function(r){return r.parts.length===1&&!n.helpers.scopedId(r)&&!r.depth}}};t.default=n,e.exports=t.default})(Ar,Ar.exports);var yi=Ar.exports,Et={},Or={exports:{}};(function(e,t){t.__esModule=!0;var n=function(){var r={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,mustache_repetition0:49,mustache_option0:50,OPEN_UNESCAPED:51,mustache_repetition1:52,mustache_option1:53,CLOSE_UNESCAPED:54,OPEN_PARTIAL:55,partialName:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,param:63,sexpr:64,OPEN_SEXPR:65,sexpr_repetition0:66,sexpr_option0:67,CLOSE_SEXPR:68,hash:69,hash_repetition_plus0:70,hashSegment:71,ID:72,EQUALS:73,blockParams:74,OPEN_BLOCK_PARAMS:75,blockParams_repetition_plus0:76,CLOSE_BLOCK_PARAMS:77,path:78,dataName:79,STRING:80,NUMBER:81,BOOLEAN:82,UNDEFINED:83,NULL:84,DATA:85,pathSegments:86,SEP:87,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",51:"OPEN_UNESCAPED",54:"CLOSE_UNESCAPED",55:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",65:"OPEN_SEXPR",68:"CLOSE_SEXPR",72:"ID",73:"EQUALS",75:"OPEN_BLOCK_PARAMS",77:"CLOSE_BLOCK_PARAMS",80:"STRING",81:"NUMBER",82:"BOOLEAN",83:"UNDEFINED",84:"NULL",85:"DATA",87:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[63,1],[63,1],[64,5],[69,1],[71,3],[74,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[56,1],[56,1],[79,2],[78,1],[86,3],[86,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[49,0],[49,2],[50,0],[50,1],[52,0],[52,2],[53,0],[53,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[66,0],[66,2],[67,0],[67,1],[70,1],[70,2],[76,1],[76,2]],performAction:function(o,i,c,l,p,u,d){var h=u.length-1;switch(p){case 1:return u[h-1];case 2:this.$=l.prepareProgram(u[h]);break;case 3:case 4:case 5:case 6:case 7:case 8:case 20:case 27:case 28:case 33:case 34:case 40:case 41:this.$=u[h];break;case 9:this.$={type:"CommentStatement",value:l.stripComment(u[h]),strip:l.stripFlags(u[h],u[h]),loc:l.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:u[h],value:u[h],loc:l.locInfo(this._$)};break;case 11:this.$=l.prepareRawBlock(u[h-2],u[h-1],u[h],this._$);break;case 12:this.$={path:u[h-3],params:u[h-2],hash:u[h-1]};break;case 13:this.$=l.prepareBlock(u[h-3],u[h-2],u[h-1],u[h],!1,this._$);break;case 14:this.$=l.prepareBlock(u[h-3],u[h-2],u[h-1],u[h],!0,this._$);break;case 15:this.$={open:u[h-5],path:u[h-4],params:u[h-3],hash:u[h-2],blockParams:u[h-1],strip:l.stripFlags(u[h-5],u[h])};break;case 16:case 17:this.$={path:u[h-4],params:u[h-3],hash:u[h-2],blockParams:u[h-1],strip:l.stripFlags(u[h-5],u[h])};break;case 18:this.$={strip:l.stripFlags(u[h-1],u[h-1]),program:u[h]};break;case 19:var m=l.prepareBlock(u[h-2],u[h-1],u[h],u[h],!1,this._$),y=l.prepareProgram([m],u[h-1].loc);y.chained=!0,this.$={strip:u[h-2].strip,program:y,chain:!0};break;case 21:this.$={path:u[h-1],strip:l.stripFlags(u[h-2],u[h])};break;case 22:case 23:this.$=l.prepareMustache(u[h-3],u[h-2],u[h-1],u[h-4],l.stripFlags(u[h-4],u[h]),this._$);break;case 24:this.$={type:"PartialStatement",name:u[h-3],params:u[h-2],hash:u[h-1],indent:"",strip:l.stripFlags(u[h-4],u[h]),loc:l.locInfo(this._$)};break;case 25:this.$=l.preparePartialBlock(u[h-2],u[h-1],u[h],this._$);break;case 26:this.$={path:u[h-3],params:u[h-2],hash:u[h-1],strip:l.stripFlags(u[h-4],u[h])};break;case 29:this.$={type:"SubExpression",path:u[h-3],params:u[h-2],hash:u[h-1],loc:l.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:u[h],loc:l.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:l.id(u[h-2]),value:u[h],loc:l.locInfo(this._$)};break;case 32:this.$=l.id(u[h-1]);break;case 35:this.$={type:"StringLiteral",value:u[h],original:u[h],loc:l.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(u[h]),original:Number(u[h]),loc:l.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:u[h]==="true",original:u[h]==="true",loc:l.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:l.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:l.locInfo(this._$)};break;case 42:this.$=l.preparePath(!0,u[h],this._$);break;case 43:this.$=l.preparePath(!1,u[h],this._$);break;case 44:u[h-2].push({part:l.id(u[h]),original:u[h],separator:u[h-1]}),this.$=u[h-2];break;case 45:this.$=[{part:l.id(u[h]),original:u[h]}];break;case 46:case 48:case 50:case 58:case 64:case 70:case 78:case 82:case 86:case 90:case 94:this.$=[];break;case 47:case 49:case 51:case 59:case 65:case 71:case 79:case 83:case 87:case 91:case 95:case 99:case 101:u[h-1].push(u[h]);break;case 98:case 100:this.$=[u[h]]}},table:[{3:1,4:2,5:[2,46],6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{1:[3]},{5:[1,4]},{5:[2,2],7:5,8:6,9:7,10:8,11:9,12:10,13:11,14:[1,12],15:[1,20],16:17,19:[1,23],24:15,27:16,29:[1,21],34:[1,22],39:[2,2],44:[2,2],47:[2,2],48:[1,13],51:[1,14],55:[1,18],59:19,60:[1,24]},{1:[2,1]},{5:[2,47],14:[2,47],15:[2,47],19:[2,47],29:[2,47],34:[2,47],39:[2,47],44:[2,47],47:[2,47],48:[2,47],51:[2,47],55:[2,47],60:[2,47]},{5:[2,3],14:[2,3],15:[2,3],19:[2,3],29:[2,3],34:[2,3],39:[2,3],44:[2,3],47:[2,3],48:[2,3],51:[2,3],55:[2,3],60:[2,3]},{5:[2,4],14:[2,4],15:[2,4],19:[2,4],29:[2,4],34:[2,4],39:[2,4],44:[2,4],47:[2,4],48:[2,4],51:[2,4],55:[2,4],60:[2,4]},{5:[2,5],14:[2,5],15:[2,5],19:[2,5],29:[2,5],34:[2,5],39:[2,5],44:[2,5],47:[2,5],48:[2,5],51:[2,5],55:[2,5],60:[2,5]},{5:[2,6],14:[2,6],15:[2,6],19:[2,6],29:[2,6],34:[2,6],39:[2,6],44:[2,6],47:[2,6],48:[2,6],51:[2,6],55:[2,6],60:[2,6]},{5:[2,7],14:[2,7],15:[2,7],19:[2,7],29:[2,7],34:[2,7],39:[2,7],44:[2,7],47:[2,7],48:[2,7],51:[2,7],55:[2,7],60:[2,7]},{5:[2,8],14:[2,8],15:[2,8],19:[2,8],29:[2,8],34:[2,8],39:[2,8],44:[2,8],47:[2,8],48:[2,8],51:[2,8],55:[2,8],60:[2,8]},{5:[2,9],14:[2,9],15:[2,9],19:[2,9],29:[2,9],34:[2,9],39:[2,9],44:[2,9],47:[2,9],48:[2,9],51:[2,9],55:[2,9],60:[2,9]},{20:25,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:36,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:37,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{4:38,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{15:[2,48],17:39,18:[2,48]},{20:41,56:40,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:44,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{5:[2,10],14:[2,10],15:[2,10],18:[2,10],19:[2,10],29:[2,10],34:[2,10],39:[2,10],44:[2,10],47:[2,10],48:[2,10],51:[2,10],55:[2,10],60:[2,10]},{20:45,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:46,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:47,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:41,56:48,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[2,78],49:49,65:[2,78],72:[2,78],80:[2,78],81:[2,78],82:[2,78],83:[2,78],84:[2,78],85:[2,78]},{23:[2,33],33:[2,33],54:[2,33],65:[2,33],68:[2,33],72:[2,33],75:[2,33],80:[2,33],81:[2,33],82:[2,33],83:[2,33],84:[2,33],85:[2,33]},{23:[2,34],33:[2,34],54:[2,34],65:[2,34],68:[2,34],72:[2,34],75:[2,34],80:[2,34],81:[2,34],82:[2,34],83:[2,34],84:[2,34],85:[2,34]},{23:[2,35],33:[2,35],54:[2,35],65:[2,35],68:[2,35],72:[2,35],75:[2,35],80:[2,35],81:[2,35],82:[2,35],83:[2,35],84:[2,35],85:[2,35]},{23:[2,36],33:[2,36],54:[2,36],65:[2,36],68:[2,36],72:[2,36],75:[2,36],80:[2,36],81:[2,36],82:[2,36],83:[2,36],84:[2,36],85:[2,36]},{23:[2,37],33:[2,37],54:[2,37],65:[2,37],68:[2,37],72:[2,37],75:[2,37],80:[2,37],81:[2,37],82:[2,37],83:[2,37],84:[2,37],85:[2,37]},{23:[2,38],33:[2,38],54:[2,38],65:[2,38],68:[2,38],72:[2,38],75:[2,38],80:[2,38],81:[2,38],82:[2,38],83:[2,38],84:[2,38],85:[2,38]},{23:[2,39],33:[2,39],54:[2,39],65:[2,39],68:[2,39],72:[2,39],75:[2,39],80:[2,39],81:[2,39],82:[2,39],83:[2,39],84:[2,39],85:[2,39]},{23:[2,43],33:[2,43],54:[2,43],65:[2,43],68:[2,43],72:[2,43],75:[2,43],80:[2,43],81:[2,43],82:[2,43],83:[2,43],84:[2,43],85:[2,43],87:[1,50]},{72:[1,35],86:51},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{52:52,54:[2,82],65:[2,82],72:[2,82],80:[2,82],81:[2,82],82:[2,82],83:[2,82],84:[2,82],85:[2,82]},{25:53,38:55,39:[1,57],43:56,44:[1,58],45:54,47:[2,54]},{28:59,43:60,44:[1,58],47:[2,56]},{13:62,15:[1,20],18:[1,61]},{33:[2,86],57:63,65:[2,86],72:[2,86],80:[2,86],81:[2,86],82:[2,86],83:[2,86],84:[2,86],85:[2,86]},{33:[2,40],65:[2,40],72:[2,40],80:[2,40],81:[2,40],82:[2,40],83:[2,40],84:[2,40],85:[2,40]},{33:[2,41],65:[2,41],72:[2,41],80:[2,41],81:[2,41],82:[2,41],83:[2,41],84:[2,41],85:[2,41]},{20:64,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:65,47:[1,66]},{30:67,33:[2,58],65:[2,58],72:[2,58],75:[2,58],80:[2,58],81:[2,58],82:[2,58],83:[2,58],84:[2,58],85:[2,58]},{33:[2,64],35:68,65:[2,64],72:[2,64],75:[2,64],80:[2,64],81:[2,64],82:[2,64],83:[2,64],84:[2,64],85:[2,64]},{21:69,23:[2,50],65:[2,50],72:[2,50],80:[2,50],81:[2,50],82:[2,50],83:[2,50],84:[2,50],85:[2,50]},{33:[2,90],61:70,65:[2,90],72:[2,90],80:[2,90],81:[2,90],82:[2,90],83:[2,90],84:[2,90],85:[2,90]},{20:74,33:[2,80],50:71,63:72,64:75,65:[1,43],69:73,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{72:[1,79]},{23:[2,42],33:[2,42],54:[2,42],65:[2,42],68:[2,42],72:[2,42],75:[2,42],80:[2,42],81:[2,42],82:[2,42],83:[2,42],84:[2,42],85:[2,42],87:[1,50]},{20:74,53:80,54:[2,84],63:81,64:75,65:[1,43],69:82,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:83,47:[1,66]},{47:[2,55]},{4:84,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{47:[2,20]},{20:85,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:86,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{26:87,47:[1,66]},{47:[2,57]},{5:[2,11],14:[2,11],15:[2,11],19:[2,11],29:[2,11],34:[2,11],39:[2,11],44:[2,11],47:[2,11],48:[2,11],51:[2,11],55:[2,11],60:[2,11]},{15:[2,49],18:[2,49]},{20:74,33:[2,88],58:88,63:89,64:75,65:[1,43],69:90,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{65:[2,94],66:91,68:[2,94],72:[2,94],80:[2,94],81:[2,94],82:[2,94],83:[2,94],84:[2,94],85:[2,94]},{5:[2,25],14:[2,25],15:[2,25],19:[2,25],29:[2,25],34:[2,25],39:[2,25],44:[2,25],47:[2,25],48:[2,25],51:[2,25],55:[2,25],60:[2,25]},{20:92,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,31:93,33:[2,60],63:94,64:75,65:[1,43],69:95,70:76,71:77,72:[1,78],75:[2,60],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,66],36:96,63:97,64:75,65:[1,43],69:98,70:76,71:77,72:[1,78],75:[2,66],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,22:99,23:[2,52],63:100,64:75,65:[1,43],69:101,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,92],62:102,63:103,64:75,65:[1,43],69:104,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,105]},{33:[2,79],65:[2,79],72:[2,79],80:[2,79],81:[2,79],82:[2,79],83:[2,79],84:[2,79],85:[2,79]},{33:[2,81]},{23:[2,27],33:[2,27],54:[2,27],65:[2,27],68:[2,27],72:[2,27],75:[2,27],80:[2,27],81:[2,27],82:[2,27],83:[2,27],84:[2,27],85:[2,27]},{23:[2,28],33:[2,28],54:[2,28],65:[2,28],68:[2,28],72:[2,28],75:[2,28],80:[2,28],81:[2,28],82:[2,28],83:[2,28],84:[2,28],85:[2,28]},{23:[2,30],33:[2,30],54:[2,30],68:[2,30],71:106,72:[1,107],75:[2,30]},{23:[2,98],33:[2,98],54:[2,98],68:[2,98],72:[2,98],75:[2,98]},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],73:[1,108],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{23:[2,44],33:[2,44],54:[2,44],65:[2,44],68:[2,44],72:[2,44],75:[2,44],80:[2,44],81:[2,44],82:[2,44],83:[2,44],84:[2,44],85:[2,44],87:[2,44]},{54:[1,109]},{54:[2,83],65:[2,83],72:[2,83],80:[2,83],81:[2,83],82:[2,83],83:[2,83],84:[2,83],85:[2,83]},{54:[2,85]},{5:[2,13],14:[2,13],15:[2,13],19:[2,13],29:[2,13],34:[2,13],39:[2,13],44:[2,13],47:[2,13],48:[2,13],51:[2,13],55:[2,13],60:[2,13]},{38:55,39:[1,57],43:56,44:[1,58],45:111,46:110,47:[2,76]},{33:[2,70],40:112,65:[2,70],72:[2,70],75:[2,70],80:[2,70],81:[2,70],82:[2,70],83:[2,70],84:[2,70],85:[2,70]},{47:[2,18]},{5:[2,14],14:[2,14],15:[2,14],19:[2,14],29:[2,14],34:[2,14],39:[2,14],44:[2,14],47:[2,14],48:[2,14],51:[2,14],55:[2,14],60:[2,14]},{33:[1,113]},{33:[2,87],65:[2,87],72:[2,87],80:[2,87],81:[2,87],82:[2,87],83:[2,87],84:[2,87],85:[2,87]},{33:[2,89]},{20:74,63:115,64:75,65:[1,43],67:114,68:[2,96],69:116,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,117]},{32:118,33:[2,62],74:119,75:[1,120]},{33:[2,59],65:[2,59],72:[2,59],75:[2,59],80:[2,59],81:[2,59],82:[2,59],83:[2,59],84:[2,59],85:[2,59]},{33:[2,61],75:[2,61]},{33:[2,68],37:121,74:122,75:[1,120]},{33:[2,65],65:[2,65],72:[2,65],75:[2,65],80:[2,65],81:[2,65],82:[2,65],83:[2,65],84:[2,65],85:[2,65]},{33:[2,67],75:[2,67]},{23:[1,123]},{23:[2,51],65:[2,51],72:[2,51],80:[2,51],81:[2,51],82:[2,51],83:[2,51],84:[2,51],85:[2,51]},{23:[2,53]},{33:[1,124]},{33:[2,91],65:[2,91],72:[2,91],80:[2,91],81:[2,91],82:[2,91],83:[2,91],84:[2,91],85:[2,91]},{33:[2,93]},{5:[2,22],14:[2,22],15:[2,22],19:[2,22],29:[2,22],34:[2,22],39:[2,22],44:[2,22],47:[2,22],48:[2,22],51:[2,22],55:[2,22],60:[2,22]},{23:[2,99],33:[2,99],54:[2,99],68:[2,99],72:[2,99],75:[2,99]},{73:[1,108]},{20:74,63:125,64:75,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,23],14:[2,23],15:[2,23],19:[2,23],29:[2,23],34:[2,23],39:[2,23],44:[2,23],47:[2,23],48:[2,23],51:[2,23],55:[2,23],60:[2,23]},{47:[2,19]},{47:[2,77]},{20:74,33:[2,72],41:126,63:127,64:75,65:[1,43],69:128,70:76,71:77,72:[1,78],75:[2,72],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,24],14:[2,24],15:[2,24],19:[2,24],29:[2,24],34:[2,24],39:[2,24],44:[2,24],47:[2,24],48:[2,24],51:[2,24],55:[2,24],60:[2,24]},{68:[1,129]},{65:[2,95],68:[2,95],72:[2,95],80:[2,95],81:[2,95],82:[2,95],83:[2,95],84:[2,95],85:[2,95]},{68:[2,97]},{5:[2,21],14:[2,21],15:[2,21],19:[2,21],29:[2,21],34:[2,21],39:[2,21],44:[2,21],47:[2,21],48:[2,21],51:[2,21],55:[2,21],60:[2,21]},{33:[1,130]},{33:[2,63]},{72:[1,132],76:131},{33:[1,133]},{33:[2,69]},{15:[2,12],18:[2,12]},{14:[2,26],15:[2,26],19:[2,26],29:[2,26],34:[2,26],47:[2,26],48:[2,26],51:[2,26],55:[2,26],60:[2,26]},{23:[2,31],33:[2,31],54:[2,31],68:[2,31],72:[2,31],75:[2,31]},{33:[2,74],42:134,74:135,75:[1,120]},{33:[2,71],65:[2,71],72:[2,71],75:[2,71],80:[2,71],81:[2,71],82:[2,71],83:[2,71],84:[2,71],85:[2,71]},{33:[2,73],75:[2,73]},{23:[2,29],33:[2,29],54:[2,29],65:[2,29],68:[2,29],72:[2,29],75:[2,29],80:[2,29],81:[2,29],82:[2,29],83:[2,29],84:[2,29],85:[2,29]},{14:[2,15],15:[2,15],19:[2,15],29:[2,15],34:[2,15],39:[2,15],44:[2,15],47:[2,15],48:[2,15],51:[2,15],55:[2,15],60:[2,15]},{72:[1,137],77:[1,136]},{72:[2,100],77:[2,100]},{14:[2,16],15:[2,16],19:[2,16],29:[2,16],34:[2,16],44:[2,16],47:[2,16],48:[2,16],51:[2,16],55:[2,16],60:[2,16]},{33:[1,138]},{33:[2,75]},{33:[2,32]},{72:[2,101],77:[2,101]},{14:[2,17],15:[2,17],19:[2,17],29:[2,17],34:[2,17],39:[2,17],44:[2,17],47:[2,17],48:[2,17],51:[2,17],55:[2,17],60:[2,17]}],defaultActions:{4:[2,1],54:[2,55],56:[2,20],60:[2,57],73:[2,81],82:[2,85],86:[2,18],90:[2,89],101:[2,53],104:[2,93],110:[2,19],111:[2,77],116:[2,97],119:[2,63],122:[2,69],135:[2,75],136:[2,32]},parseError:function(o,i){throw new Error(o)},parse:function(o){var i=this,c=[0],l=[null],p=[],u=this.table,d="",h=0,m=0;this.lexer.setInput(o),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,this.lexer.yylloc===void 0&&(this.lexer.yylloc={});var y=this.lexer.yylloc;p.push(y);var g=this.lexer.options&&this.lexer.options.ranges;typeof this.yy.parseError=="function"&&(this.parseError=this.yy.parseError);for(var v,E,_,T,w,I,N,F,M,k={};;){if(E=c[c.length-1],this.defaultActions[E]?_=this.defaultActions[E]:(v==null&&(M=void 0,typeof(M=i.lexer.lex()||1)!="number"&&(M=i.symbols_[M]||M),v=M),_=u[E]&&u[E][v]),_===void 0||!_.length||!_[0]){var ne="";for(w in F=[],u[E])this.terminals_[w]&&w>2&&F.push("'"+this.terminals_[w]+"'");ne=this.lexer.showPosition?"Parse error on line "+(h+1)+`:
`+this.lexer.showPosition()+`
Expecting `+F.join(", ")+", got '"+(this.terminals_[v]||v)+"'":"Parse error on line "+(h+1)+": Unexpected "+(v==1?"end of input":"'"+(this.terminals_[v]||v)+"'"),this.parseError(ne,{text:this.lexer.match,token:this.terminals_[v]||v,line:this.lexer.yylineno,loc:y,expected:F})}if(_[0]instanceof Array&&_.length>1)throw new Error("Parse Error: multiple actions possible at state: "+E+", token: "+v);switch(_[0]){case 1:c.push(v),l.push(this.lexer.yytext),p.push(this.lexer.yylloc),c.push(_[1]),v=null,m=this.lexer.yyleng,d=this.lexer.yytext,h=this.lexer.yylineno,y=this.lexer.yylloc;break;case 2:if(I=this.productions_[_[1]][1],k.$=l[l.length-I],k._$={first_line:p[p.length-(I||1)].first_line,last_line:p[p.length-1].last_line,first_column:p[p.length-(I||1)].first_column,last_column:p[p.length-1].last_column},g&&(k._$.range=[p[p.length-(I||1)].range[0],p[p.length-1].range[1]]),(T=this.performAction.call(k,d,m,h,this.yy,_[1],l,p))!==void 0)return T;I&&(c=c.slice(0,-1*I*2),l=l.slice(0,-1*I),p=p.slice(0,-1*I)),c.push(this.productions_[_[1]][0]),l.push(k.$),p.push(k._$),N=u[c[c.length-2]][c[c.length-1]],c.push(N);break;case 3:return!0}}return!0}},a=function(){var o={EOF:1,parseError:function(i,c){if(!this.yy.parser)throw new Error(i);this.yy.parser.parseError(i,c)},setInput:function(i){return this._input=i,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var i=this._input[0];return this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i,i.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},unput:function(i){var c=i.length,l=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-c-1),this.offset-=c;var p=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),l.length-1&&(this.yylineno-=l.length-1);var u=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:l?(l.length===p.length?this.yylloc.first_column:0)+p[p.length-l.length].length-l[0].length:this.yylloc.first_column-c},this.options.ranges&&(this.yylloc.range=[u[0],u[0]+this.yyleng-c]),this},more:function(){return this._more=!0,this},less:function(i){this.unput(this.match.slice(i))},pastInput:function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var i=this.pastInput(),c=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+c+"^"},next:function(){if(this.done)return this.EOF;var i,c,l,p,u;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var d=this._currentRules(),h=0;h<d.length&&(!(l=this._input.match(this.rules[d[h]]))||c&&!(l[0].length>c[0].length)||(c=l,p=h,this.options.flex));h++);return c?((u=c[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+c[0].length},this.yytext+=c[0],this.match+=c[0],this.matches=c,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(c[0].length),this.matched+=c[0],i=this.performAction.call(this,this.yy,this,d[p],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),i||void 0):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var i=this.next();return i!==void 0?i:this.lex()},begin:function(i){this.conditionStack.push(i)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(i){this.begin(i)},options:{},performAction:function(i,c,l,p){function u(d,h){return c.yytext=c.yytext.substring(d,c.yyleng-h+d)}switch(l){case 0:if(c.yytext.slice(-2)==="\\\\"?(u(0,1),this.begin("mu")):c.yytext.slice(-1)==="\\"?(u(0,1),this.begin("emu")):this.begin("mu"),c.yytext)return 15;break;case 1:case 5:return 15;case 2:return this.popState(),15;case 3:return this.begin("raw"),15;case 4:return this.popState(),this.conditionStack[this.conditionStack.length-1]==="raw"?15:(u(5,9),"END_RAW_BLOCK");case 6:case 22:return this.popState(),14;case 7:return 65;case 8:return 68;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;case 11:return 55;case 12:return 60;case 13:return 29;case 14:return 47;case 15:case 16:return this.popState(),44;case 17:return 34;case 18:return 39;case 19:return 51;case 20:case 23:return 48;case 21:this.unput(c.yytext),this.popState(),this.begin("com");break;case 24:return 73;case 25:case 26:case 41:return 72;case 27:return 87;case 28:break;case 29:return this.popState(),54;case 30:return this.popState(),33;case 31:return c.yytext=u(1,2).replace(/\\"/g,'"'),80;case 32:return c.yytext=u(1,2).replace(/\\'/g,"'"),80;case 33:return 85;case 34:case 35:return 82;case 36:return 83;case 37:return 84;case 38:return 81;case 39:return 75;case 40:return 77;case 42:return c.yytext=c.yytext.replace(/\\([\\\]])/g,"$1"),72;case 43:return"INVALID";case 44:return 5}},rules:[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],conditions:{mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}}};return o}();function s(){this.yy={}}return r.lexer=a,s.prototype=r,r.Parser=s,new s}();t.default=n,e.exports=t.default})(Or,Or.exports);var nd=Or.exports,Mr={exports:{}},Pr={exports:{}};(function(e,t){t.__esModule=!0;var n=function(i){return i&&i.__esModule?i:{default:i}}(he);function r(){this.parents=[]}function a(i){this.acceptRequired(i,"path"),this.acceptArray(i.params),this.acceptKey(i,"hash")}function s(i){a.call(this,i),this.acceptKey(i,"program"),this.acceptKey(i,"inverse")}function o(i){this.acceptRequired(i,"name"),this.acceptArray(i.params),this.acceptKey(i,"hash")}r.prototype={constructor:r,mutating:!1,acceptKey:function(i,c){var l=this.accept(i[c]);if(this.mutating){if(l&&!r.prototype[l.type])throw new n.default('Unexpected node type "'+l.type+'" found when accepting '+c+" on "+i.type);i[c]=l}},acceptRequired:function(i,c){if(this.acceptKey(i,c),!i[c])throw new n.default(i.type+" requires "+c)},acceptArray:function(i){for(var c=0,l=i.length;c<l;c++)this.acceptKey(i,c),i[c]||(i.splice(c,1),c--,l--)},accept:function(i){if(i){if(!this[i.type])throw new n.default("Unknown type: "+i.type,i);this.current&&this.parents.unshift(this.current),this.current=i;var c=this[i.type](i);return this.current=this.parents.shift(),!this.mutating||c?c:c!==!1?i:void 0}},Program:function(i){this.acceptArray(i.body)},MustacheStatement:a,Decorator:a,BlockStatement:s,DecoratorBlock:s,PartialStatement:o,PartialBlockStatement:function(i){o.call(this,i),this.acceptKey(i,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:a,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(i){this.acceptArray(i.pairs)},HashPair:function(i){this.acceptRequired(i,"value")}},t.default=r,e.exports=t.default})(Pr,Pr.exports);var bi=Pr.exports;(function(e,t){t.__esModule=!0;var n=function(c){return c&&c.__esModule?c:{default:c}}(bi);function r(){var c=arguments.length<=0||arguments[0]===void 0?{}:arguments[0];this.options=c}function a(c,l,p){l===void 0&&(l=c.length);var u=c[l-1],d=c[l-2];return u?u.type==="ContentStatement"?(d||!p?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(u.original):void 0:p}function s(c,l,p){l===void 0&&(l=-1);var u=c[l+1],d=c[l+2];return u?u.type==="ContentStatement"?(d||!p?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(u.original):void 0:p}function o(c,l,p){var u=c[l==null?0:l+1];if(u&&u.type==="ContentStatement"&&(p||!u.rightStripped)){var d=u.value;u.value=u.value.replace(p?/^\s+/:/^[ \t]*\r?\n?/,""),u.rightStripped=u.value!==d}}function i(c,l,p){var u=c[l==null?c.length-1:l-1];if(u&&u.type==="ContentStatement"&&(p||!u.leftStripped)){var d=u.value;return u.value=u.value.replace(p?/\s+$/:/[ \t]+$/,""),u.leftStripped=u.value!==d,u.leftStripped}}r.prototype=new n.default,r.prototype.Program=function(c){var l=!this.options.ignoreStandalone,p=!this.isRootSeen;this.isRootSeen=!0;for(var u=c.body,d=0,h=u.length;d<h;d++){var m=u[d],y=this.accept(m);if(y){var g=a(u,d,p),v=s(u,d,p),E=y.openStandalone&&g,_=y.closeStandalone&&v,T=y.inlineStandalone&&g&&v;y.close&&o(u,d,!0),y.open&&i(u,d,!0),l&&T&&(o(u,d),i(u,d)&&m.type==="PartialStatement"&&(m.indent=/([ \t]+$)/.exec(u[d-1].original)[1])),l&&E&&(o((m.program||m.inverse).body),i(u,d)),l&&_&&(o(u,d),i((m.inverse||m.program).body))}}return c},r.prototype.BlockStatement=r.prototype.DecoratorBlock=r.prototype.PartialBlockStatement=function(c){this.accept(c.program),this.accept(c.inverse);var l=c.program||c.inverse,p=c.program&&c.inverse,u=p,d=p;if(p&&p.chained)for(u=p.body[0].program;d.chained;)d=d.body[d.body.length-1].program;var h={open:c.openStrip.open,close:c.closeStrip.close,openStandalone:s(l.body),closeStandalone:a((u||l).body)};if(c.openStrip.close&&o(l.body,null,!0),p){var m=c.inverseStrip;m.open&&i(l.body,null,!0),m.close&&o(u.body,null,!0),c.closeStrip.open&&i(d.body,null,!0),!this.options.ignoreStandalone&&a(l.body)&&s(u.body)&&(i(l.body),o(u.body))}else c.closeStrip.open&&i(l.body,null,!0);return h},r.prototype.Decorator=r.prototype.MustacheStatement=function(c){return c.strip},r.prototype.PartialStatement=r.prototype.CommentStatement=function(c){var l=c.strip||{};return{inlineStandalone:!0,open:l.open,close:l.close}},t.default=r,e.exports=t.default})(Mr,Mr.exports);var rd=Mr.exports,re={};re.__esModule=!0,re.SourceLocation=function(e,t){this.source=e,this.start={line:t.first_line,column:t.first_column},this.end={line:t.last_line,column:t.last_column}},re.id=function(e){return/^\[.*\]$/.test(e)?e.substring(1,e.length-1):e},re.stripFlags=function(e,t){return{open:e.charAt(2)==="~",close:t.charAt(t.length-3)==="~"}},re.stripComment=function(e){return e.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")},re.preparePath=function(e,t,n){n=this.locInfo(n);for(var r=e?"@":"",a=[],s=0,o=0,i=t.length;o<i;o++){var c=t[o].part,l=t[o].original!==c;if(r+=(t[o].separator||"")+c,l||c!==".."&&c!=="."&&c!=="this")a.push(c);else{if(a.length>0)throw new xr.default("Invalid path: "+r,{loc:n});c===".."&&s++}}return{type:"PathExpression",data:e,depth:s,parts:a,original:r,loc:n}},re.prepareMustache=function(e,t,n,r,a,s){var o=r.charAt(3)||r.charAt(2),i=o!=="{"&&o!=="&";return{type:/\*/.test(r)?"Decorator":"MustacheStatement",path:e,params:t,hash:n,escaped:i,strip:a,loc:this.locInfo(s)}},re.prepareRawBlock=function(e,t,n,r){Hn(e,n),r=this.locInfo(r);var a={type:"Program",body:t,strip:{},loc:r};return{type:"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:a,openStrip:{},inverseStrip:{},closeStrip:{},loc:r}},re.prepareBlock=function(e,t,n,r,a,s){r&&r.path&&Hn(e,r);var o=/\*/.test(e.open);t.blockParams=e.blockParams;var i=void 0,c=void 0;if(n){if(o)throw new xr.default("Unexpected inverse block on decorator",n);n.chain&&(n.program.body[0].closeStrip=r.strip),c=n.strip,i=n.program}return a&&(a=i,i=t,t=a),{type:o?"DecoratorBlock":"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:t,inverse:i,openStrip:e.strip,inverseStrip:c,closeStrip:r&&r.strip,loc:this.locInfo(s)}},re.prepareProgram=function(e,t){if(!t&&e.length){var n=e[0].loc,r=e[e.length-1].loc;n&&r&&(t={source:n.source,start:{line:n.start.line,column:n.start.column},end:{line:r.end.line,column:r.end.column}})}return{type:"Program",body:e,strip:{},loc:t}},re.preparePartialBlock=function(e,t,n,r){return Hn(e,n),{type:"PartialBlockStatement",name:e.path,params:e.params,hash:e.hash,program:t,openStrip:e.strip,closeStrip:n&&n.strip,loc:this.locInfo(r)}};var xr=function(e){return e&&e.__esModule?e:{default:e}}(he);function Hn(e,t){if(t=t.path?t.path.original:t,e.path.original!==t){var n={loc:e.path.loc};throw new xr.default(e.path.original+" doesn't match "+t,n)}}function vi(e){return e&&e.__esModule?e:{default:e}}Et.__esModule=!0,Et.parseWithoutProcessing=xa,Et.parse=function(e,t){var n=xa(e,t);return new sd.default(t).accept(n)};var Lr=vi(nd),sd=vi(rd),ad=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(re),od=O;Et.parser=Lr.default;var Zt={};function xa(e,t){return e.type==="Program"?e:(Lr.default.yy=Zt,Zt.locInfo=function(n){return new Zt.SourceLocation(t&&t.srcName,n)},Lr.default.parse(e))}od.extend(Zt,ad);var mt={};function _i(e){return e&&e.__esModule?e:{default:e}}mt.__esModule=!0,mt.Compiler=Dr,mt.precompile=function(e,t,n){if(e==null||typeof e!="string"&&e.type!=="Program")throw new St.default("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+e);"data"in(t=t||{})||(t.data=!0),t.compat&&(t.useDepths=!0);var r=n.parse(e,t),a=new n.Compiler().compile(r,t);return new n.JavaScriptCompiler().compile(a,t)},mt.compile=function(e,t,n){if(t===void 0&&(t={}),e==null||typeof e!="string"&&e.type!=="Program")throw new St.default("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+e);"data"in(t=Rt.extend({},t))||(t.data=!0),t.compat&&(t.useDepths=!0);var r=void 0;function a(){var o=n.parse(e,t),i=new n.Compiler().compile(o,t),c=new n.JavaScriptCompiler().compile(i,t,void 0,!0);return n.template(c)}function s(o,i){return r||(r=a()),r.call(this,o,i)}return s._setup=function(o){return r||(r=a()),r._setup(o)},s._child=function(o,i,c,l){return r||(r=a()),r._child(o,i,c,l)},s};var St=_i(he),Rt=O,ut=_i(yi),id=[].slice;function Dr(){}function Ei(e,t){if(e===t)return!0;if(Rt.isArray(e)&&Rt.isArray(t)&&e.length===t.length){for(var n=0;n<e.length;n++)if(!Ei(e[n],t[n]))return!1;return!0}}function La(e){if(!e.path.parts){var t=e.path;e.path={type:"PathExpression",data:!1,depth:0,parts:[t.original+""],original:t.original+"",loc:t.loc}}}Dr.prototype={compiler:Dr,equals:function(e){var t=this.opcodes.length;if(e.opcodes.length!==t)return!1;for(var n=0;n<t;n++){var r=this.opcodes[n],a=e.opcodes[n];if(r.opcode!==a.opcode||!Ei(r.args,a.args))return!1}for(t=this.children.length,n=0;n<t;n++)if(!this.children[n].equals(e.children[n]))return!1;return!0},guid:0,compile:function(e,t){return this.sourceNode=[],this.opcodes=[],this.children=[],this.options=t,this.stringParams=t.stringParams,this.trackIds=t.trackIds,t.blockParams=t.blockParams||[],t.knownHelpers=Rt.extend(Object.create(null),{helperMissing:!0,blockHelperMissing:!0,each:!0,if:!0,unless:!0,with:!0,log:!0,lookup:!0},t.knownHelpers),this.accept(e)},compileProgram:function(e){var t=new this.compiler().compile(e,this.options),n=this.guid++;return this.usePartial=this.usePartial||t.usePartial,this.children[n]=t,this.useDepths=this.useDepths||t.useDepths,n},accept:function(e){if(!this[e.type])throw new St.default("Unknown type: "+e.type,e);this.sourceNode.unshift(e);var t=this[e.type](e);return this.sourceNode.shift(),t},Program:function(e){this.options.blockParams.unshift(e.blockParams);for(var t=e.body,n=t.length,r=0;r<n;r++)this.accept(t[r]);return this.options.blockParams.shift(),this.isSimple=n===1,this.blockParams=e.blockParams?e.blockParams.length:0,this},BlockStatement:function(e){La(e);var t=e.program,n=e.inverse;t=t&&this.compileProgram(t),n=n&&this.compileProgram(n);var r=this.classifySexpr(e);r==="helper"?this.helperSexpr(e,t,n):r==="simple"?(this.simpleSexpr(e),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("blockValue",e.path.original)):(this.ambiguousSexpr(e,t,n),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},DecoratorBlock:function(e){var t=e.program&&this.compileProgram(e.program),n=this.setupFullMustacheParams(e,t,void 0),r=e.path;this.useDecorators=!0,this.opcode("registerDecorator",n.length,r.original)},PartialStatement:function(e){this.usePartial=!0;var t=e.program;t&&(t=this.compileProgram(e.program));var n=e.params;if(n.length>1)throw new St.default("Unsupported number of partial arguments: "+n.length,e);n.length||(this.options.explicitPartialContext?this.opcode("pushLiteral","undefined"):n.push({type:"PathExpression",parts:[],depth:0}));var r=e.name.original,a=e.name.type==="SubExpression";a&&this.accept(e.name),this.setupFullMustacheParams(e,t,void 0,!0);var s=e.indent||"";this.options.preventIndent&&s&&(this.opcode("appendContent",s),s=""),this.opcode("invokePartial",a,r,s),this.opcode("append")},PartialBlockStatement:function(e){this.PartialStatement(e)},MustacheStatement:function(e){this.SubExpression(e),e.escaped&&!this.options.noEscape?this.opcode("appendEscaped"):this.opcode("append")},Decorator:function(e){this.DecoratorBlock(e)},ContentStatement:function(e){e.value&&this.opcode("appendContent",e.value)},CommentStatement:function(){},SubExpression:function(e){La(e);var t=this.classifySexpr(e);t==="simple"?this.simpleSexpr(e):t==="helper"?this.helperSexpr(e):this.ambiguousSexpr(e)},ambiguousSexpr:function(e,t,n){var r=e.path,a=r.parts[0],s=t!=null||n!=null;this.opcode("getContext",r.depth),this.opcode("pushProgram",t),this.opcode("pushProgram",n),r.strict=!0,this.accept(r),this.opcode("invokeAmbiguous",a,s)},simpleSexpr:function(e){var t=e.path;t.strict=!0,this.accept(t),this.opcode("resolvePossibleLambda")},helperSexpr:function(e,t,n){var r=this.setupFullMustacheParams(e,t,n),a=e.path,s=a.parts[0];if(this.options.knownHelpers[s])this.opcode("invokeKnownHelper",r.length,s);else{if(this.options.knownHelpersOnly)throw new St.default("You specified knownHelpersOnly, but used the unknown helper "+s,e);a.strict=!0,a.falsy=!0,this.accept(a),this.opcode("invokeHelper",r.length,a.original,ut.default.helpers.simpleId(a))}},PathExpression:function(e){this.addDepth(e.depth),this.opcode("getContext",e.depth);var t=e.parts[0],n=ut.default.helpers.scopedId(e),r=!e.depth&&!n&&this.blockParamIndex(t);r?this.opcode("lookupBlockParam",r,e.parts):t?e.data?(this.options.data=!0,this.opcode("lookupData",e.depth,e.parts,e.strict)):this.opcode("lookupOnContext",e.parts,e.falsy,e.strict,n):this.opcode("pushContext")},StringLiteral:function(e){this.opcode("pushString",e.value)},NumberLiteral:function(e){this.opcode("pushLiteral",e.value)},BooleanLiteral:function(e){this.opcode("pushLiteral",e.value)},UndefinedLiteral:function(){this.opcode("pushLiteral","undefined")},NullLiteral:function(){this.opcode("pushLiteral","null")},Hash:function(e){var t=e.pairs,n=0,r=t.length;for(this.opcode("pushHash");n<r;n++)this.pushParam(t[n].value);for(;n--;)this.opcode("assignToHash",t[n].key);this.opcode("popHash")},opcode:function(e){this.opcodes.push({opcode:e,args:id.call(arguments,1),loc:this.sourceNode[0].loc})},addDepth:function(e){e&&(this.useDepths=!0)},classifySexpr:function(e){var t=ut.default.helpers.simpleId(e.path),n=t&&!!this.blockParamIndex(e.path.parts[0]),r=!n&&ut.default.helpers.helperExpression(e),a=!n&&(r||t);if(a&&!r){var s=e.path.parts[0],o=this.options;o.knownHelpers[s]?r=!0:o.knownHelpersOnly&&(a=!1)}return r?"helper":a?"ambiguous":"simple"},pushParams:function(e){for(var t=0,n=e.length;t<n;t++)this.pushParam(e[t])},pushParam:function(e){var t=e.value!=null?e.value:e.original||"";if(this.stringParams)t.replace&&(t=t.replace(/^(\.?\.\/)*/g,"").replace(/\//g,".")),e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",t,e.type),e.type==="SubExpression"&&this.accept(e);else{if(this.trackIds){var n=void 0;if(!e.parts||ut.default.helpers.scopedId(e)||e.depth||(n=this.blockParamIndex(e.parts[0])),n){var r=e.parts.slice(1).join(".");this.opcode("pushId","BlockParam",n,r)}else(t=e.original||t).replace&&(t=t.replace(/^this(?:\.|$)/,"").replace(/^\.\//,"").replace(/^\.$/,"")),this.opcode("pushId",e.type,t)}this.accept(e)}},setupFullMustacheParams:function(e,t,n,r){var a=e.params;return this.pushParams(a),this.opcode("pushProgram",t),this.opcode("pushProgram",n),e.hash?this.accept(e.hash):this.opcode("emptyHash",r),a},blockParamIndex:function(e){for(var t=0,n=this.options.blockParams.length;t<n;t++){var r=this.options.blockParams[t],a=r&&Rt.indexOf(r,e);if(r&&a>=0)return[t,a]}}};var Da,Fa,Fr={exports:{}},Ur={exports:{}},Bt={},Wn={},Vt={},Gt={};function ld(){if(Da)return Gt;Da=1;var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");return Gt.encode=function(t){if(0<=t&&t<e.length)return e[t];throw new TypeError("Must be between 0 and 63: "+t)},Gt.decode=function(t){return 65<=t&&t<=90?t-65:97<=t&&t<=122?t-97+26:48<=t&&t<=57?t-48+52:t==43?62:t==47?63:-1},Gt}function Si(){if(Fa)return Vt;Fa=1;var e=ld();return Vt.encode=function(t){var n,r="",a=function(s){return s<0?1+(-s<<1):0+(s<<1)}(t);do n=31&a,(a>>>=5)>0&&(n|=32),r+=e.encode(n);while(a>0);return r},Vt.decode=function(t,n,r){var a,s,o,i,c=t.length,l=0,p=0;do{if(n>=c)throw new Error("Expected more digits in base 64 VLQ value.");if((s=e.decode(t.charCodeAt(n++)))===-1)throw new Error("Invalid base64 digit: "+t.charAt(n-1));a=!!(32&s),l+=(s&=31)<<p,p+=5}while(a);r.value=(i=(o=l)>>1,1&~o?i:-i),r.rest=n},Vt}var Ua,$a={};function Ct(){return Ua||(Ua=1,function(e){e.getArg=function(u,d,h){if(d in u)return u[d];if(arguments.length===3)return h;throw new Error('"'+d+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,n=/^data:.+\,.+$/;function r(u){var d=u.match(t);return d?{scheme:d[1],auth:d[2],host:d[3],port:d[4],path:d[5]}:null}function a(u){var d="";return u.scheme&&(d+=u.scheme+":"),d+="//",u.auth&&(d+=u.auth+"@"),u.host&&(d+=u.host),u.port&&(d+=":"+u.port),u.path&&(d+=u.path),d}function s(u){var d=u,h=r(u);if(h){if(!h.path)return u;d=h.path}for(var m,y=e.isAbsolute(d),g=d.split(/\/+/),v=0,E=g.length-1;E>=0;E--)(m=g[E])==="."?g.splice(E,1):m===".."?v++:v>0&&(m===""?(g.splice(E+1,v),v=0):(g.splice(E,2),v--));return(d=g.join("/"))===""&&(d=y?"/":"."),h?(h.path=d,a(h)):d}function o(u,d){u===""&&(u="."),d===""&&(d=".");var h=r(d),m=r(u);if(m&&(u=m.path||"/"),h&&!h.scheme)return m&&(h.scheme=m.scheme),a(h);if(h||d.match(n))return d;if(m&&!m.host&&!m.path)return m.host=d,a(m);var y=d.charAt(0)==="/"?d:s(u.replace(/\/+$/,"")+"/"+d);return m?(m.path=y,a(m)):y}e.urlParse=r,e.urlGenerate=a,e.normalize=s,e.join=o,e.isAbsolute=function(u){return u.charAt(0)==="/"||t.test(u)},e.relative=function(u,d){u===""&&(u="."),u=u.replace(/\/$/,"");for(var h=0;d.indexOf(u+"/")!==0;){var m=u.lastIndexOf("/");if(m<0||(u=u.slice(0,m)).match(/^([^\/]+:\/)?\/*$/))return d;++h}return Array(h+1).join("../")+d.substr(u.length+1)};var i=!("__proto__"in Object.create(null));function c(u){return u}function l(u){if(!u)return!1;var d=u.length;if(d<9||u.charCodeAt(d-1)!==95||u.charCodeAt(d-2)!==95||u.charCodeAt(d-3)!==111||u.charCodeAt(d-4)!==116||u.charCodeAt(d-5)!==111||u.charCodeAt(d-6)!==114||u.charCodeAt(d-7)!==112||u.charCodeAt(d-8)!==95||u.charCodeAt(d-9)!==95)return!1;for(var h=d-10;h>=0;h--)if(u.charCodeAt(h)!==36)return!1;return!0}function p(u,d){return u===d?0:u===null?1:d===null?-1:u>d?1:-1}e.toSetString=i?c:function(u){return l(u)?"$"+u:u},e.fromSetString=i?c:function(u){return l(u)?u.slice(1):u},e.compareByOriginalPositions=function(u,d,h){var m=p(u.source,d.source);return m!==0||(m=u.originalLine-d.originalLine)!==0||(m=u.originalColumn-d.originalColumn)!==0||h||(m=u.generatedColumn-d.generatedColumn)!==0||(m=u.generatedLine-d.generatedLine)!==0?m:p(u.name,d.name)},e.compareByGeneratedPositionsDeflated=function(u,d,h){var m=u.generatedLine-d.generatedLine;return m!==0||(m=u.generatedColumn-d.generatedColumn)!==0||h||(m=p(u.source,d.source))!==0||(m=u.originalLine-d.originalLine)!==0||(m=u.originalColumn-d.originalColumn)!==0?m:p(u.name,d.name)},e.compareByGeneratedPositionsInflated=function(u,d){var h=u.generatedLine-d.generatedLine;return h!==0||(h=u.generatedColumn-d.generatedColumn)!==0||(h=p(u.source,d.source))!==0||(h=u.originalLine-d.originalLine)!==0||(h=u.originalColumn-d.originalColumn)!==0?h:p(u.name,d.name)},e.parseSourceMapInput=function(u){return JSON.parse(u.replace(/^\)]}'[^\n]*\n/,""))},e.computeSourceURL=function(u,d,h){if(d=d||"",u&&(u[u.length-1]!=="/"&&d[0]!=="/"&&(u+="/"),d=u+d),h){var m=r(h);if(!m)throw new Error("sourceMapURL could not be parsed");if(m.path){var y=m.path.lastIndexOf("/");y>=0&&(m.path=m.path.substring(0,y+1))}d=o(a(m),d)}return s(d)}}($a)),$a}var Ba,Xn={};function Ti(){if(Ba)return Xn;Ba=1;var e=Ct(),t=Object.prototype.hasOwnProperty,n=typeof Map<"u";function r(){this._array=[],this._set=n?new Map:Object.create(null)}return r.fromArray=function(a,s){for(var o=new r,i=0,c=a.length;i<c;i++)o.add(a[i],s);return o},r.prototype.size=function(){return n?this._set.size:Object.getOwnPropertyNames(this._set).length},r.prototype.add=function(a,s){var o=n?a:e.toSetString(a),i=n?this.has(a):t.call(this._set,o),c=this._array.length;i&&!s||this._array.push(a),i||(n?this._set.set(a,c):this._set[o]=c)},r.prototype.has=function(a){if(n)return this._set.has(a);var s=e.toSetString(a);return t.call(this._set,s)},r.prototype.indexOf=function(a){if(n){var s=this._set.get(a);if(s>=0)return s}else{var o=e.toSetString(a);if(t.call(this._set,o))return this._set[o]}throw new Error('"'+a+'" is not in the set.')},r.prototype.at=function(a){if(a>=0&&a<this._array.length)return this._array[a];throw new Error("No element indexed by "+a)},r.prototype.toArray=function(){return this._array.slice()},Xn.ArraySet=r,Xn}var Va,Ga,zn={};function ud(){if(Va)return zn;Va=1;var e=Ct();function t(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}return t.prototype.unsortedForEach=function(n,r){this._array.forEach(n,r)},t.prototype.add=function(n){var r,a,s,o,i,c;r=this._last,a=n,s=r.generatedLine,o=a.generatedLine,i=r.generatedColumn,c=a.generatedColumn,o>s||o==s&&c>=i||e.compareByGeneratedPositionsInflated(r,a)<=0?(this._last=n,this._array.push(n)):(this._sorted=!1,this._array.push(n))},t.prototype.toArray=function(){return this._sorted||(this._array.sort(e.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},zn.MappingList=t,zn}function ja(){if(Ga)return Wn;Ga=1;var e=Si(),t=Ct(),n=Ti().ArraySet,r=ud().MappingList;function a(s){s||(s={}),this._file=t.getArg(s,"file",null),this._sourceRoot=t.getArg(s,"sourceRoot",null),this._skipValidation=t.getArg(s,"skipValidation",!1),this._sources=new n,this._names=new n,this._mappings=new r,this._sourcesContents=null}return a.prototype._version=3,a.fromSourceMap=function(s){var o=s.sourceRoot,i=new a({file:s.file,sourceRoot:o});return s.eachMapping(function(c){var l={generated:{line:c.generatedLine,column:c.generatedColumn}};c.source!=null&&(l.source=c.source,o!=null&&(l.source=t.relative(o,l.source)),l.original={line:c.originalLine,column:c.originalColumn},c.name!=null&&(l.name=c.name)),i.addMapping(l)}),s.sources.forEach(function(c){var l=c;o!==null&&(l=t.relative(o,c)),i._sources.has(l)||i._sources.add(l);var p=s.sourceContentFor(c);p!=null&&i.setSourceContent(c,p)}),i},a.prototype.addMapping=function(s){var o=t.getArg(s,"generated"),i=t.getArg(s,"original",null),c=t.getArg(s,"source",null),l=t.getArg(s,"name",null);this._skipValidation||this._validateMapping(o,i,c,l),c!=null&&(c=String(c),this._sources.has(c)||this._sources.add(c)),l!=null&&(l=String(l),this._names.has(l)||this._names.add(l)),this._mappings.add({generatedLine:o.line,generatedColumn:o.column,originalLine:i!=null&&i.line,originalColumn:i!=null&&i.column,source:c,name:l})},a.prototype.setSourceContent=function(s,o){var i=s;this._sourceRoot!=null&&(i=t.relative(this._sourceRoot,i)),o!=null?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[t.toSetString(i)]=o):this._sourcesContents&&(delete this._sourcesContents[t.toSetString(i)],Object.keys(this._sourcesContents).length===0&&(this._sourcesContents=null))},a.prototype.applySourceMap=function(s,o,i){var c=o;if(o==null){if(s.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);c=s.file}var l=this._sourceRoot;l!=null&&(c=t.relative(l,c));var p=new n,u=new n;this._mappings.unsortedForEach(function(d){if(d.source===c&&d.originalLine!=null){var h=s.originalPositionFor({line:d.originalLine,column:d.originalColumn});h.source!=null&&(d.source=h.source,i!=null&&(d.source=t.join(i,d.source)),l!=null&&(d.source=t.relative(l,d.source)),d.originalLine=h.line,d.originalColumn=h.column,h.name!=null&&(d.name=h.name))}var m=d.source;m==null||p.has(m)||p.add(m);var y=d.name;y==null||u.has(y)||u.add(y)},this),this._sources=p,this._names=u,s.sources.forEach(function(d){var h=s.sourceContentFor(d);h!=null&&(i!=null&&(d=t.join(i,d)),l!=null&&(d=t.relative(l,d)),this.setSourceContent(d,h))},this)},a.prototype._validateMapping=function(s,o,i,c){if(o&&typeof o.line!="number"&&typeof o.column!="number")throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(s&&"line"in s&&"column"in s&&s.line>0&&s.column>=0)||o||i||c)&&!(s&&"line"in s&&"column"in s&&o&&"line"in o&&"column"in o&&s.line>0&&s.column>=0&&o.line>0&&o.column>=0&&i))throw new Error("Invalid mapping: "+JSON.stringify({generated:s,source:i,original:o,name:c}))},a.prototype._serializeMappings=function(){for(var s,o,i,c,l=0,p=1,u=0,d=0,h=0,m=0,y="",g=this._mappings.toArray(),v=0,E=g.length;v<E;v++){if(s="",(o=g[v]).generatedLine!==p)for(l=0;o.generatedLine!==p;)s+=";",p++;else if(v>0){if(!t.compareByGeneratedPositionsInflated(o,g[v-1]))continue;s+=","}s+=e.encode(o.generatedColumn-l),l=o.generatedColumn,o.source!=null&&(c=this._sources.indexOf(o.source),s+=e.encode(c-m),m=c,s+=e.encode(o.originalLine-1-d),d=o.originalLine-1,s+=e.encode(o.originalColumn-u),u=o.originalColumn,o.name!=null&&(i=this._names.indexOf(o.name),s+=e.encode(i-h),h=i)),y+=s}return y},a.prototype._generateSourcesContent=function(s,o){return s.map(function(i){if(!this._sourcesContents)return null;o!=null&&(i=t.relative(o,i));var c=t.toSetString(i);return Object.prototype.hasOwnProperty.call(this._sourcesContents,c)?this._sourcesContents[c]:null},this)},a.prototype.toJSON=function(){var s={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return this._file!=null&&(s.file=this._file),this._sourceRoot!=null&&(s.sourceRoot=this._sourceRoot),this._sourcesContents&&(s.sourcesContent=this._generateSourcesContent(s.sources,s.sourceRoot)),s},a.prototype.toString=function(){return JSON.stringify(this.toJSON())},Wn.SourceMapGenerator=a,Wn}var qa,ct={},Ya={};function cd(){return qa||(qa=1,function(e){function t(n,r,a,s,o,i){var c=Math.floor((r-n)/2)+n,l=o(a,s[c],!0);return l===0?c:l>0?r-c>1?t(c,r,a,s,o,i):i==e.LEAST_UPPER_BOUND?r<s.length?r:-1:c:c-n>1?t(n,c,a,s,o,i):i==e.LEAST_UPPER_BOUND?c:n<0?-1:n}e.GREATEST_LOWER_BOUND=1,e.LEAST_UPPER_BOUND=2,e.search=function(n,r,a,s){if(r.length===0)return-1;var o=t(-1,r.length,n,r,a,s||e.GREATEST_LOWER_BOUND);if(o<0)return-1;for(;o-1>=0&&a(r[o],r[o-1],!0)===0;)--o;return o}}(Ya)),Ya}var Ka,Ha,Jn={};function dd(){if(Ka)return Jn;function e(n,r,a){var s=n[r];n[r]=n[a],n[a]=s}function t(n,r,a,s){if(a<s){var o=a-1;e(n,(p=a,u=s,Math.round(p+Math.random()*(u-p))),s);for(var i=n[s],c=a;c<s;c++)r(n[c],i)<=0&&e(n,o+=1,c);e(n,o+1,c);var l=o+1;t(n,r,a,l-1),t(n,r,l+1,s)}var p,u}return Ka=1,Jn.quickSort=function(n,r){t(n,r,0,n.length-1)},Jn}var Wa,Xa,Zn={};function pd(){return Xa||(Xa=1,Bt.SourceMapGenerator=ja().SourceMapGenerator,Bt.SourceMapConsumer=function(){if(Ha)return ct;Ha=1;var e=Ct(),t=cd(),n=Ti().ArraySet,r=Si(),a=dd().quickSort;function s(l,p){var u=l;return typeof l=="string"&&(u=e.parseSourceMapInput(l)),u.sections!=null?new c(u,p):new o(u,p)}function o(l,p){var u=l;typeof l=="string"&&(u=e.parseSourceMapInput(l));var d=e.getArg(u,"version"),h=e.getArg(u,"sources"),m=e.getArg(u,"names",[]),y=e.getArg(u,"sourceRoot",null),g=e.getArg(u,"sourcesContent",null),v=e.getArg(u,"mappings"),E=e.getArg(u,"file",null);if(d!=this._version)throw new Error("Unsupported version: "+d);y&&(y=e.normalize(y)),h=h.map(String).map(e.normalize).map(function(_){return y&&e.isAbsolute(y)&&e.isAbsolute(_)?e.relative(y,_):_}),this._names=n.fromArray(m.map(String),!0),this._sources=n.fromArray(h,!0),this._absoluteSources=this._sources.toArray().map(function(_){return e.computeSourceURL(y,_,p)}),this.sourceRoot=y,this.sourcesContent=g,this._mappings=v,this._sourceMapURL=p,this.file=E}function i(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function c(l,p){var u=l;typeof l=="string"&&(u=e.parseSourceMapInput(l));var d=e.getArg(u,"version"),h=e.getArg(u,"sections");if(d!=this._version)throw new Error("Unsupported version: "+d);this._sources=new n,this._names=new n;var m={line:-1,column:0};this._sections=h.map(function(y){if(y.url)throw new Error("Support for url field in sections not implemented.");var g=e.getArg(y,"offset"),v=e.getArg(g,"line"),E=e.getArg(g,"column");if(v<m.line||v===m.line&&E<m.column)throw new Error("Section offsets must be ordered and non-overlapping.");return m=g,{generatedOffset:{generatedLine:v+1,generatedColumn:E+1},consumer:new s(e.getArg(y,"map"),p)}})}return s.fromSourceMap=function(l,p){return o.fromSourceMap(l,p)},s.prototype._version=3,s.prototype.__generatedMappings=null,Object.defineProperty(s.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),s.prototype.__originalMappings=null,Object.defineProperty(s.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),s.prototype._charIsMappingSeparator=function(l,p){var u=l.charAt(p);return u===";"||u===","},s.prototype._parseMappings=function(l,p){throw new Error("Subclasses must implement _parseMappings")},s.GENERATED_ORDER=1,s.ORIGINAL_ORDER=2,s.GREATEST_LOWER_BOUND=1,s.LEAST_UPPER_BOUND=2,s.prototype.eachMapping=function(l,p,u){var d,h=p||null;switch(u||s.GENERATED_ORDER){case s.GENERATED_ORDER:d=this._generatedMappings;break;case s.ORIGINAL_ORDER:d=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var m=this.sourceRoot;d.map(function(y){var g=y.source===null?null:this._sources.at(y.source);return{source:g=e.computeSourceURL(m,g,this._sourceMapURL),generatedLine:y.generatedLine,generatedColumn:y.generatedColumn,originalLine:y.originalLine,originalColumn:y.originalColumn,name:y.name===null?null:this._names.at(y.name)}},this).forEach(l,h)},s.prototype.allGeneratedPositionsFor=function(l){var p=e.getArg(l,"line"),u={source:e.getArg(l,"source"),originalLine:p,originalColumn:e.getArg(l,"column",0)};if(u.source=this._findSourceIndex(u.source),u.source<0)return[];var d=[],h=this._findMapping(u,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,t.LEAST_UPPER_BOUND);if(h>=0){var m=this._originalMappings[h];if(l.column===void 0)for(var y=m.originalLine;m&&m.originalLine===y;)d.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++h];else for(var g=m.originalColumn;m&&m.originalLine===p&&m.originalColumn==g;)d.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++h]}return d},ct.SourceMapConsumer=s,o.prototype=Object.create(s.prototype),o.prototype.consumer=s,o.prototype._findSourceIndex=function(l){var p,u=l;if(this.sourceRoot!=null&&(u=e.relative(this.sourceRoot,u)),this._sources.has(u))return this._sources.indexOf(u);for(p=0;p<this._absoluteSources.length;++p)if(this._absoluteSources[p]==l)return p;return-1},o.fromSourceMap=function(l,p){var u=Object.create(o.prototype),d=u._names=n.fromArray(l._names.toArray(),!0),h=u._sources=n.fromArray(l._sources.toArray(),!0);u.sourceRoot=l._sourceRoot,u.sourcesContent=l._generateSourcesContent(u._sources.toArray(),u.sourceRoot),u.file=l._file,u._sourceMapURL=p,u._absoluteSources=u._sources.toArray().map(function(w){return e.computeSourceURL(u.sourceRoot,w,p)});for(var m=l._mappings.toArray().slice(),y=u.__generatedMappings=[],g=u.__originalMappings=[],v=0,E=m.length;v<E;v++){var _=m[v],T=new i;T.generatedLine=_.generatedLine,T.generatedColumn=_.generatedColumn,_.source&&(T.source=h.indexOf(_.source),T.originalLine=_.originalLine,T.originalColumn=_.originalColumn,_.name&&(T.name=d.indexOf(_.name)),g.push(T)),y.push(T)}return a(u.__originalMappings,e.compareByOriginalPositions),u},o.prototype._version=3,Object.defineProperty(o.prototype,"sources",{get:function(){return this._absoluteSources.slice()}}),o.prototype._parseMappings=function(l,p){for(var u,d,h,m,y,g=1,v=0,E=0,_=0,T=0,w=0,I=l.length,N=0,F={},M={},k=[],ne=[];N<I;)if(l.charAt(N)===";")g++,N++,v=0;else if(l.charAt(N)===",")N++;else{for((u=new i).generatedLine=g,m=N;m<I&&!this._charIsMappingSeparator(l,m);m++);if(h=F[d=l.slice(N,m)])N+=d.length;else{for(h=[];N<m;)r.decode(l,N,M),y=M.value,N=M.rest,h.push(y);if(h.length===2)throw new Error("Found a source, but no line and column");if(h.length===3)throw new Error("Found a source and line, but no column");F[d]=h}u.generatedColumn=v+h[0],v=u.generatedColumn,h.length>1&&(u.source=T+h[1],T+=h[1],u.originalLine=E+h[2],E=u.originalLine,u.originalLine+=1,u.originalColumn=_+h[3],_=u.originalColumn,h.length>4&&(u.name=w+h[4],w+=h[4])),ne.push(u),typeof u.originalLine=="number"&&k.push(u)}a(ne,e.compareByGeneratedPositionsDeflated),this.__generatedMappings=ne,a(k,e.compareByOriginalPositions),this.__originalMappings=k},o.prototype._findMapping=function(l,p,u,d,h,m){if(l[u]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+l[u]);if(l[d]<0)throw new TypeError("Column must be greater than or equal to 0, got "+l[d]);return t.search(l,p,h,m)},o.prototype.computeColumnSpans=function(){for(var l=0;l<this._generatedMappings.length;++l){var p=this._generatedMappings[l];if(l+1<this._generatedMappings.length){var u=this._generatedMappings[l+1];if(p.generatedLine===u.generatedLine){p.lastGeneratedColumn=u.generatedColumn-1;continue}}p.lastGeneratedColumn=1/0}},o.prototype.originalPositionFor=function(l){var p={generatedLine:e.getArg(l,"line"),generatedColumn:e.getArg(l,"column")},u=this._findMapping(p,this._generatedMappings,"generatedLine","generatedColumn",e.compareByGeneratedPositionsDeflated,e.getArg(l,"bias",s.GREATEST_LOWER_BOUND));if(u>=0){var d=this._generatedMappings[u];if(d.generatedLine===p.generatedLine){var h=e.getArg(d,"source",null);h!==null&&(h=this._sources.at(h),h=e.computeSourceURL(this.sourceRoot,h,this._sourceMapURL));var m=e.getArg(d,"name",null);return m!==null&&(m=this._names.at(m)),{source:h,line:e.getArg(d,"originalLine",null),column:e.getArg(d,"originalColumn",null),name:m}}}return{source:null,line:null,column:null,name:null}},o.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(l){return l==null})},o.prototype.sourceContentFor=function(l,p){if(!this.sourcesContent)return null;var u=this._findSourceIndex(l);if(u>=0)return this.sourcesContent[u];var d,h=l;if(this.sourceRoot!=null&&(h=e.relative(this.sourceRoot,h)),this.sourceRoot!=null&&(d=e.urlParse(this.sourceRoot))){var m=h.replace(/^file:\/\//,"");if(d.scheme=="file"&&this._sources.has(m))return this.sourcesContent[this._sources.indexOf(m)];if((!d.path||d.path=="/")&&this._sources.has("/"+h))return this.sourcesContent[this._sources.indexOf("/"+h)]}if(p)return null;throw new Error('"'+h+'" is not in the SourceMap.')},o.prototype.generatedPositionFor=function(l){var p=e.getArg(l,"source");if((p=this._findSourceIndex(p))<0)return{line:null,column:null,lastColumn:null};var u={source:p,originalLine:e.getArg(l,"line"),originalColumn:e.getArg(l,"column")},d=this._findMapping(u,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,e.getArg(l,"bias",s.GREATEST_LOWER_BOUND));if(d>=0){var h=this._originalMappings[d];if(h.source===u.source)return{line:e.getArg(h,"generatedLine",null),column:e.getArg(h,"generatedColumn",null),lastColumn:e.getArg(h,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},ct.BasicSourceMapConsumer=o,c.prototype=Object.create(s.prototype),c.prototype.constructor=s,c.prototype._version=3,Object.defineProperty(c.prototype,"sources",{get:function(){for(var l=[],p=0;p<this._sections.length;p++)for(var u=0;u<this._sections[p].consumer.sources.length;u++)l.push(this._sections[p].consumer.sources[u]);return l}}),c.prototype.originalPositionFor=function(l){var p={generatedLine:e.getArg(l,"line"),generatedColumn:e.getArg(l,"column")},u=t.search(p,this._sections,function(h,m){return h.generatedLine-m.generatedOffset.generatedLine||h.generatedColumn-m.generatedOffset.generatedColumn}),d=this._sections[u];return d?d.consumer.originalPositionFor({line:p.generatedLine-(d.generatedOffset.generatedLine-1),column:p.generatedColumn-(d.generatedOffset.generatedLine===p.generatedLine?d.generatedOffset.generatedColumn-1:0),bias:l.bias}):{source:null,line:null,column:null,name:null}},c.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(l){return l.consumer.hasContentsOfAllSources()})},c.prototype.sourceContentFor=function(l,p){for(var u=0;u<this._sections.length;u++){var d=this._sections[u].consumer.sourceContentFor(l,!0);if(d)return d}if(p)return null;throw new Error('"'+l+'" is not in the SourceMap.')},c.prototype.generatedPositionFor=function(l){for(var p=0;p<this._sections.length;p++){var u=this._sections[p];if(u.consumer._findSourceIndex(e.getArg(l,"source"))!==-1){var d=u.consumer.generatedPositionFor(l);if(d)return{line:d.line+(u.generatedOffset.generatedLine-1),column:d.column+(u.generatedOffset.generatedLine===d.line?u.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},c.prototype._parseMappings=function(l,p){this.__generatedMappings=[],this.__originalMappings=[];for(var u=0;u<this._sections.length;u++)for(var d=this._sections[u],h=d.consumer._generatedMappings,m=0;m<h.length;m++){var y=h[m],g=d.consumer._sources.at(y.source);g=e.computeSourceURL(d.consumer.sourceRoot,g,this._sourceMapURL),this._sources.add(g),g=this._sources.indexOf(g);var v=null;y.name&&(v=d.consumer._names.at(y.name),this._names.add(v),v=this._names.indexOf(v));var E={source:g,generatedLine:y.generatedLine+(d.generatedOffset.generatedLine-1),generatedColumn:y.generatedColumn+(d.generatedOffset.generatedLine===y.generatedLine?d.generatedOffset.generatedColumn-1:0),originalLine:y.originalLine,originalColumn:y.originalColumn,name:v};this.__generatedMappings.push(E),typeof E.originalLine=="number"&&this.__originalMappings.push(E)}a(this.__generatedMappings,e.compareByGeneratedPositionsDeflated),a(this.__originalMappings,e.compareByOriginalPositions)},ct.IndexedSourceMapConsumer=c,ct}().SourceMapConsumer,Bt.SourceNode=function(){if(Wa)return Zn;Wa=1;var e=ja().SourceMapGenerator,t=Ct(),n=/(\r?\n)/,r="$$$isSourceNode$$$";function a(s,o,i,c,l){this.children=[],this.sourceContents={},this.line=s??null,this.column=o??null,this.source=i??null,this.name=l??null,this[r]=!0,c!=null&&this.add(c)}return a.fromStringWithSourceMap=function(s,o,i){var c=new a,l=s.split(n),p=0,u=function(){return g()+(g()||"");function g(){return p<l.length?l[p++]:void 0}},d=1,h=0,m=null;return o.eachMapping(function(g){if(m!==null){if(!(d<g.generatedLine)){var v=(E=l[p]||"").substr(0,g.generatedColumn-h);return l[p]=E.substr(g.generatedColumn-h),h=g.generatedColumn,y(m,v),void(m=g)}y(m,u()),d++,h=0}for(;d<g.generatedLine;)c.add(u()),d++;if(h<g.generatedColumn){var E=l[p]||"";c.add(E.substr(0,g.generatedColumn)),l[p]=E.substr(g.generatedColumn),h=g.generatedColumn}m=g},this),p<l.length&&(m&&y(m,u()),c.add(l.splice(p).join(""))),o.sources.forEach(function(g){var v=o.sourceContentFor(g);v!=null&&(i!=null&&(g=t.join(i,g)),c.setSourceContent(g,v))}),c;function y(g,v){if(g===null||g.source===void 0)c.add(v);else{var E=i?t.join(i,g.source):g.source;c.add(new a(g.originalLine,g.originalColumn,E,v,g.name))}}},a.prototype.add=function(s){if(Array.isArray(s))s.forEach(function(o){this.add(o)},this);else{if(!s[r]&&typeof s!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+s);s&&this.children.push(s)}return this},a.prototype.prepend=function(s){if(Array.isArray(s))for(var o=s.length-1;o>=0;o--)this.prepend(s[o]);else{if(!s[r]&&typeof s!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+s);this.children.unshift(s)}return this},a.prototype.walk=function(s){for(var o,i=0,c=this.children.length;i<c;i++)(o=this.children[i])[r]?o.walk(s):o!==""&&s(o,{source:this.source,line:this.line,column:this.column,name:this.name})},a.prototype.join=function(s){var o,i,c=this.children.length;if(c>0){for(o=[],i=0;i<c-1;i++)o.push(this.children[i]),o.push(s);o.push(this.children[i]),this.children=o}return this},a.prototype.replaceRight=function(s,o){var i=this.children[this.children.length-1];return i[r]?i.replaceRight(s,o):typeof i=="string"?this.children[this.children.length-1]=i.replace(s,o):this.children.push("".replace(s,o)),this},a.prototype.setSourceContent=function(s,o){this.sourceContents[t.toSetString(s)]=o},a.prototype.walkSourceContents=function(s){for(var o=0,i=this.children.length;o<i;o++)this.children[o][r]&&this.children[o].walkSourceContents(s);var c=Object.keys(this.sourceContents);for(o=0,i=c.length;o<i;o++)s(t.fromSetString(c[o]),this.sourceContents[c[o]])},a.prototype.toString=function(){var s="";return this.walk(function(o){s+=o}),s},a.prototype.toStringWithSourceMap=function(s){var o={code:"",line:1,column:0},i=new e(s),c=!1,l=null,p=null,u=null,d=null;return this.walk(function(h,m){o.code+=h,m.source!==null&&m.line!==null&&m.column!==null?(l===m.source&&p===m.line&&u===m.column&&d===m.name||i.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:o.line,column:o.column},name:m.name}),l=m.source,p=m.line,u=m.column,d=m.name,c=!0):c&&(i.addMapping({generated:{line:o.line,column:o.column}}),l=null,c=!1);for(var y=0,g=h.length;y<g;y++)h.charCodeAt(y)===10?(o.line++,o.column=0,y+1===g?(l=null,c=!1):c&&i.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:o.line,column:o.column},name:m.name})):o.column++}),this.walkSourceContents(function(h,m){i.setSourceContent(h,m)}),{code:o.code,map:i}},Zn.SourceNode=a,Zn}().SourceNode),Bt}(function(e,t){t.__esModule=!0;var n=O,r=void 0;try{var a=pd();r=a.SourceNode}catch{}function s(i,c,l){if(n.isArray(i)){for(var p=[],u=0,d=i.length;u<d;u++)p.push(c.wrap(i[u],l));return p}return typeof i=="boolean"||typeof i=="number"?i+"":i}function o(i){this.srcFile=i,this.source=[]}r||((r=function(i,c,l,p){this.src="",p&&this.add(p)}).prototype={add:function(i){n.isArray(i)&&(i=i.join("")),this.src+=i},prepend:function(i){n.isArray(i)&&(i=i.join("")),this.src=i+this.src},toStringWithSourceMap:function(){return{code:this.toString()}},toString:function(){return this.src}}),o.prototype={isEmpty:function(){return!this.source.length},prepend:function(i,c){this.source.unshift(this.wrap(i,c))},push:function(i,c){this.source.push(this.wrap(i,c))},merge:function(){var i=this.empty();return this.each(function(c){i.add(["  ",c,`
`])}),i},each:function(i){for(var c=0,l=this.source.length;c<l;c++)i(this.source[c])},empty:function(){var i=this.currentLocation||{start:{}};return new r(i.start.line,i.start.column,this.srcFile)},wrap:function(i){var c=arguments.length<=1||arguments[1]===void 0?this.currentLocation||{start:{}}:arguments[1];return i instanceof r?i:(i=s(i,this,c),new r(c.start.line,c.start.column,this.srcFile,i))},functionCall:function(i,c,l){return l=this.generateList(l),this.wrap([i,c?"."+c+"(":"(",l,")"])},quotedString:function(i){return'"'+(i+"").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},objectLiteral:function(i){var c=this,l=[];Object.keys(i).forEach(function(u){var d=s(i[u],c);d!=="undefined"&&l.push([c.quotedString(u),":",d])});var p=this.generateList(l);return p.prepend("{"),p.add("}"),p},generateList:function(i){for(var c=this.empty(),l=0,p=i.length;l<p;l++)l&&c.add(","),c.add(s(i[l],this));return c},generateArray:function(i){var c=this.generateList(i);return c.prepend("["),c.add("]"),c}},t.default=o,e.exports=t.default})(Ur,Ur.exports);var hd=Ur.exports;(function(e,t){function n(l){return l&&l.__esModule?l:{default:l}}t.__esModule=!0;var r=le,a=n(he),s=O,o=n(hd);function i(l){this.value=l}function c(){}c.prototype={nameLookup:function(l,p){return this.internalNameLookup(l,p)},depthedLookup:function(l){return[this.aliasable("container.lookup"),"(depths, ",JSON.stringify(l),")"]},compilerInfo:function(){var l=r.COMPILER_REVISION;return[l,r.REVISION_CHANGES[l]]},appendToBuffer:function(l,p,u){return s.isArray(l)||(l=[l]),l=this.source.wrap(l,p),this.environment.isSimple?["return ",l,";"]:u?["buffer += ",l,";"]:(l.appendToBuffer=!0,l)},initializeBuffer:function(){return this.quotedString("")},internalNameLookup:function(l,p){return this.lookupPropertyFunctionIsUsed=!0,["lookupProperty(",l,",",JSON.stringify(p),")"]},lookupPropertyFunctionIsUsed:!1,compile:function(l,p,u,d){this.environment=l,this.options=p,this.stringParams=this.options.stringParams,this.trackIds=this.options.trackIds,this.precompile=!d,this.name=this.environment.name,this.isChild=!!u,this.context=u||{decorators:[],programs:[],environments:[]},this.preamble(),this.stackSlot=0,this.stackVars=[],this.aliases={},this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.blockParams=[],this.compileChildren(l,p),this.useDepths=this.useDepths||l.useDepths||l.useDecorators||this.options.compat,this.useBlockParams=this.useBlockParams||l.useBlockParams;var h=l.opcodes,m=void 0,y=void 0,g=void 0,v=void 0;for(g=0,v=h.length;g<v;g++)m=h[g],this.source.currentLocation=m.loc,y=y||m.loc,this[m.opcode].apply(this,m.args);if(this.source.currentLocation=y,this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new a.default("Compile completed with content left on stack");this.decorators.isEmpty()?this.decorators=void 0:(this.useDecorators=!0,this.decorators.prepend(["var decorators = container.decorators, ",this.lookupPropertyFunctionVarDeclaration(),`;
`]),this.decorators.push("return fn;"),d?this.decorators=Function.apply(this,["fn","props","container","depth0","data","blockParams","depths",this.decorators.merge()]):(this.decorators.prepend(`function(fn, props, container, depth0, data, blockParams, depths) {
`),this.decorators.push(`}
`),this.decorators=this.decorators.merge()));var E=this.createFunctionContext(d);if(this.isChild)return E;var _={compiler:this.compilerInfo(),main:E};this.decorators&&(_.main_d=this.decorators,_.useDecorators=!0);var T=this.context,w=T.programs,I=T.decorators;for(g=0,v=w.length;g<v;g++)w[g]&&(_[g]=w[g],I[g]&&(_[g+"_d"]=I[g],_.useDecorators=!0));return this.environment.usePartial&&(_.usePartial=!0),this.options.data&&(_.useData=!0),this.useDepths&&(_.useDepths=!0),this.useBlockParams&&(_.useBlockParams=!0),this.options.compat&&(_.compat=!0),d?_.compilerOptions=this.options:(_.compiler=JSON.stringify(_.compiler),this.source.currentLocation={start:{line:1,column:0}},_=this.objectLiteral(_),p.srcName?(_=_.toStringWithSourceMap({file:p.destName})).map=_.map&&_.map.toString():_=_.toString()),_},preamble:function(){this.lastContext=0,this.source=new o.default(this.options.srcName),this.decorators=new o.default(this.options.srcName)},createFunctionContext:function(l){var p=this,u="",d=this.stackVars.concat(this.registers.list);d.length>0&&(u+=", "+d.join(", "));var h=0;Object.keys(this.aliases).forEach(function(g){var v=p.aliases[g];v.children&&v.referenceCount>1&&(u+=", alias"+ ++h+"="+g,v.children[0]="alias"+h)}),this.lookupPropertyFunctionIsUsed&&(u+=", "+this.lookupPropertyFunctionVarDeclaration());var m=["container","depth0","helpers","partials","data"];(this.useBlockParams||this.useDepths)&&m.push("blockParams"),this.useDepths&&m.push("depths");var y=this.mergeSource(u);return l?(m.push(y),Function.apply(this,m)):this.source.wrap(["function(",m.join(","),`) {
  `,y,"}"])},mergeSource:function(l){var p=this.environment.isSimple,u=!this.forceBuffer,d=void 0,h=void 0,m=void 0,y=void 0;return this.source.each(function(g){g.appendToBuffer?(m?g.prepend("  + "):m=g,y=g):(m&&(h?m.prepend("buffer += "):d=!0,y.add(";"),m=y=void 0),h=!0,p||(u=!1))}),u?m?(m.prepend("return "),y.add(";")):h||this.source.push('return "";'):(l+=", buffer = "+(d?"":this.initializeBuffer()),m?(m.prepend("return buffer + "),y.add(";")):this.source.push("return buffer;")),l&&this.source.prepend("var "+l.substring(2)+(d?"":`;
`)),this.source.merge()},lookupPropertyFunctionVarDeclaration:function(){return`
      lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    }
    `.trim()},blockValue:function(l){var p=this.aliasable("container.hooks.blockHelperMissing"),u=[this.contextName(0)];this.setupHelperArgs(l,0,u);var d=this.popStack();u.splice(1,0,d),this.push(this.source.functionCall(p,"call",u))},ambiguousBlockValue:function(){var l=this.aliasable("container.hooks.blockHelperMissing"),p=[this.contextName(0)];this.setupHelperArgs("",0,p,!0),this.flushInline();var u=this.topStack();p.splice(1,0,u),this.pushSource(["if (!",this.lastHelper,") { ",u," = ",this.source.functionCall(l,"call",p),"}"])},appendContent:function(l){this.pendingContent?l=this.pendingContent+l:this.pendingLocation=this.source.currentLocation,this.pendingContent=l},append:function(){if(this.isInline())this.replaceStack(function(p){return[" != null ? ",p,' : ""']}),this.pushSource(this.appendToBuffer(this.popStack()));else{var l=this.popStack();this.pushSource(["if (",l," != null) { ",this.appendToBuffer(l,void 0,!0)," }"]),this.environment.isSimple&&this.pushSource(["else { ",this.appendToBuffer("''",void 0,!0)," }"])}},appendEscaped:function(){this.pushSource(this.appendToBuffer([this.aliasable("container.escapeExpression"),"(",this.popStack(),")"]))},getContext:function(l){this.lastContext=l},pushContext:function(){this.pushStackLiteral(this.contextName(this.lastContext))},lookupOnContext:function(l,p,u,d){var h=0;d||!this.options.compat||this.lastContext?this.pushContext():this.push(this.depthedLookup(l[h++])),this.resolvePath("context",l,h,p,u)},lookupBlockParam:function(l,p){this.useBlockParams=!0,this.push(["blockParams[",l[0],"][",l[1],"]"]),this.resolvePath("context",p,1)},lookupData:function(l,p,u){l?this.pushStackLiteral("container.data(data, "+l+")"):this.pushStackLiteral("data"),this.resolvePath("data",p,0,!0,u)},resolvePath:function(l,p,u,d,h){var m=this;if(this.options.strict||this.options.assumeObjects)this.push(function(g,v,E,_,T){var w=v.popStack(),I=E.length;for(g&&I--;_<I;_++)w=v.nameLookup(w,E[_],T);return g?[v.aliasable("container.strict"),"(",w,", ",v.quotedString(E[_]),", ",JSON.stringify(v.source.currentLocation)," )"]:w}(this.options.strict&&h,this,p,u,l));else for(var y=p.length;u<y;u++)this.replaceStack(function(g){var v=m.nameLookup(g,p[u],l);return d?[" && ",v]:[" != null ? ",v," : ",g]})},resolvePossibleLambda:function(){this.push([this.aliasable("container.lambda"),"(",this.popStack(),", ",this.contextName(0),")"])},pushStringParam:function(l,p){this.pushContext(),this.pushString(p),p!=="SubExpression"&&(typeof l=="string"?this.pushString(l):this.pushStackLiteral(l))},emptyHash:function(l){this.trackIds&&this.push("{}"),this.stringParams&&(this.push("{}"),this.push("{}")),this.pushStackLiteral(l?"undefined":"{}")},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:{},types:[],contexts:[],ids:[]}},popHash:function(){var l=this.hash;this.hash=this.hashes.pop(),this.trackIds&&this.push(this.objectLiteral(l.ids)),this.stringParams&&(this.push(this.objectLiteral(l.contexts)),this.push(this.objectLiteral(l.types))),this.push(this.objectLiteral(l.values))},pushString:function(l){this.pushStackLiteral(this.quotedString(l))},pushLiteral:function(l){this.pushStackLiteral(l)},pushProgram:function(l){l!=null?this.pushStackLiteral(this.programExpression(l)):this.pushStackLiteral(null)},registerDecorator:function(l,p){var u=this.nameLookup("decorators",p,"decorator"),d=this.setupHelperArgs(p,l);this.decorators.push(["fn = ",this.decorators.functionCall(u,"",["fn","props","container",d])," || fn;"])},invokeHelper:function(l,p,u){var d=this.popStack(),h=this.setupHelper(l,p),m=[];u&&m.push(h.name),m.push(d),this.options.strict||m.push(this.aliasable("container.hooks.helperMissing"));var y=["(",this.itemsSeparatedBy(m,"||"),")"],g=this.source.functionCall(y,"call",h.callParams);this.push(g)},itemsSeparatedBy:function(l,p){var u=[];u.push(l[0]);for(var d=1;d<l.length;d++)u.push(p,l[d]);return u},invokeKnownHelper:function(l,p){var u=this.setupHelper(l,p);this.push(this.source.functionCall(u.name,"call",u.callParams))},invokeAmbiguous:function(l,p){this.useRegister("helper");var u=this.popStack();this.emptyHash();var d=this.setupHelper(0,l,p),h=["(","(helper = ",this.lastHelper=this.nameLookup("helpers",l,"helper")," || ",u,")"];this.options.strict||(h[0]="(helper = ",h.push(" != null ? helper : ",this.aliasable("container.hooks.helperMissing"))),this.push(["(",h,d.paramsInit?["),(",d.paramsInit]:[],"),","(typeof helper === ",this.aliasable('"function"')," ? ",this.source.functionCall("helper","call",d.callParams)," : helper))"])},invokePartial:function(l,p,u){var d=[],h=this.setupParams(p,1,d);l&&(p=this.popStack(),delete h.name),u&&(h.indent=JSON.stringify(u)),h.helpers="helpers",h.partials="partials",h.decorators="container.decorators",l?d.unshift(p):d.unshift(this.nameLookup("partials",p,"partial")),this.options.compat&&(h.depths="depths"),h=this.objectLiteral(h),d.push(h),this.push(this.source.functionCall("container.invokePartial","",d))},assignToHash:function(l){var p=this.popStack(),u=void 0,d=void 0,h=void 0;this.trackIds&&(h=this.popStack()),this.stringParams&&(d=this.popStack(),u=this.popStack());var m=this.hash;u&&(m.contexts[l]=u),d&&(m.types[l]=d),h&&(m.ids[l]=h),m.values[l]=p},pushId:function(l,p,u){l==="BlockParam"?this.pushStackLiteral("blockParams["+p[0]+"].path["+p[1]+"]"+(u?" + "+JSON.stringify("."+u):"")):l==="PathExpression"?this.pushString(p):l==="SubExpression"?this.pushStackLiteral("true"):this.pushStackLiteral("null")},compiler:c,compileChildren:function(l,p){for(var u=l.children,d=void 0,h=void 0,m=0,y=u.length;m<y;m++){d=u[m],h=new this.compiler;var g=this.matchExistingProgram(d);if(g==null){this.context.programs.push("");var v=this.context.programs.length;d.index=v,d.name="program"+v,this.context.programs[v]=h.compile(d,p,this.context,!this.precompile),this.context.decorators[v]=h.decorators,this.context.environments[v]=d,this.useDepths=this.useDepths||h.useDepths,this.useBlockParams=this.useBlockParams||h.useBlockParams,d.useDepths=this.useDepths,d.useBlockParams=this.useBlockParams}else d.index=g.index,d.name="program"+g.index,this.useDepths=this.useDepths||g.useDepths,this.useBlockParams=this.useBlockParams||g.useBlockParams}},matchExistingProgram:function(l){for(var p=0,u=this.context.environments.length;p<u;p++){var d=this.context.environments[p];if(d&&d.equals(l))return d}},programExpression:function(l){var p=this.environment.children[l],u=[p.index,"data",p.blockParams];return(this.useBlockParams||this.useDepths)&&u.push("blockParams"),this.useDepths&&u.push("depths"),"container.program("+u.join(", ")+")"},useRegister:function(l){this.registers[l]||(this.registers[l]=!0,this.registers.list.push(l))},push:function(l){return l instanceof i||(l=this.source.wrap(l)),this.inlineStack.push(l),l},pushStackLiteral:function(l){this.push(new i(l))},pushSource:function(l){this.pendingContent&&(this.source.push(this.appendToBuffer(this.source.quotedString(this.pendingContent),this.pendingLocation)),this.pendingContent=void 0),l&&this.source.push(l)},replaceStack:function(l){var p=["("],u=void 0,d=void 0,h=void 0;if(!this.isInline())throw new a.default("replaceStack on non-inline");var m=this.popStack(!0);if(m instanceof i)p=["(",u=[m.value]],h=!0;else{d=!0;var y=this.incrStack();p=["((",this.push(y)," = ",m,")"],u=this.topStack()}var g=l.call(this,u);h||this.popStack(),d&&this.stackSlot--,this.push(p.concat(g,")"))},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var l=this.inlineStack;this.inlineStack=[];for(var p=0,u=l.length;p<u;p++){var d=l[p];if(d instanceof i)this.compileStack.push(d);else{var h=this.incrStack();this.pushSource([h," = ",d,";"]),this.compileStack.push(h)}}},isInline:function(){return this.inlineStack.length},popStack:function(l){var p=this.isInline(),u=(p?this.inlineStack:this.compileStack).pop();if(!l&&u instanceof i)return u.value;if(!p){if(!this.stackSlot)throw new a.default("Invalid stack pop");this.stackSlot--}return u},topStack:function(){var l=this.isInline()?this.inlineStack:this.compileStack,p=l[l.length-1];return p instanceof i?p.value:p},contextName:function(l){return this.useDepths&&l?"depths["+l+"]":"depth"+l},quotedString:function(l){return this.source.quotedString(l)},objectLiteral:function(l){return this.source.objectLiteral(l)},aliasable:function(l){var p=this.aliases[l];return p?(p.referenceCount++,p):((p=this.aliases[l]=this.source.wrap(l)).aliasable=!0,p.referenceCount=1,p)},setupHelper:function(l,p,u){var d=[];return{params:d,paramsInit:this.setupHelperArgs(p,l,d,u),name:this.nameLookup("helpers",p,"helper"),callParams:[this.aliasable(this.contextName(0)+" != null ? "+this.contextName(0)+" : (container.nullContext || {})")].concat(d)}},setupParams:function(l,p,u){var d={},h=[],m=[],y=[],g=!u,v=void 0;g&&(u=[]),d.name=this.quotedString(l),d.hash=this.popStack(),this.trackIds&&(d.hashIds=this.popStack()),this.stringParams&&(d.hashTypes=this.popStack(),d.hashContexts=this.popStack());var E=this.popStack(),_=this.popStack();(_||E)&&(d.fn=_||"container.noop",d.inverse=E||"container.noop");for(var T=p;T--;)v=this.popStack(),u[T]=v,this.trackIds&&(y[T]=this.popStack()),this.stringParams&&(m[T]=this.popStack(),h[T]=this.popStack());return g&&(d.args=this.source.generateArray(u)),this.trackIds&&(d.ids=this.source.generateArray(y)),this.stringParams&&(d.types=this.source.generateArray(m),d.contexts=this.source.generateArray(h)),this.options.data&&(d.data="data"),this.useBlockParams&&(d.blockParams="blockParams"),d},setupHelperArgs:function(l,p,u,d){var h=this.setupParams(l,p,u);return h.loc=JSON.stringify(this.source.currentLocation),h=this.objectLiteral(h),d?(this.useRegister("options"),u.push("options"),["options=",h]):u?(u.push(h),""):h}},function(){for(var l="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield await null true false".split(" "),p=c.RESERVED_WORDS={},u=0,d=l.length;u<d;u++)p[l[u]]=!0}(),c.isValidJavaScriptVariableName=function(l){return!c.RESERVED_WORDS[l]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(l)},t.default=c,e.exports=t.default})(Fr,Fr.exports);var md=Fr.exports;(function(e,t){function n(h){return h&&h.__esModule?h:{default:h}}t.__esModule=!0;var r=n(td),a=n(yi),s=Et,o=mt,i=n(md),c=n(bi),l=n(gi),p=r.default.create;function u(){var h=p();return h.compile=function(m,y){return o.compile(m,y,h)},h.precompile=function(m,y){return o.precompile(m,y,h)},h.AST=a.default,h.Compiler=o.Compiler,h.JavaScriptCompiler=i.default,h.Parser=s.parser,h.parse=s.parse,h.parseWithoutProcessing=s.parseWithoutProcessing,h}var d=u();d.create=u,l.default(d),d.Visitor=c.default,d.default=d,t.default=d,e.exports=t.default})(hr,hr.exports);const fd=Vr(hr.exports),gd=`
<user>
{{{userMessage}}}
</user>
{{#if hasActions}}
<agent_actions>
{{#if filesModified}}
	<files_modified>
{{#each filesModified}}
		{{{this}}}
{{/each}}
	</files_modified>
{{/if}}
{{#if filesCreated}}
	<files_created>
{{#each filesCreated}}
		{{{this}}}
{{/each}}
	</files_created>
{{/if}}
{{#if filesDeleted}}
	<files_deleted>
{{#each filesDeleted}}
		{{{this}}}
{{/each}}
	</files_deleted>
{{/if}}
{{#if filesViewed}}
	<files_viewed>
{{#each filesViewed}}
		{{{this}}}
{{/each}}
	</files_viewed>
{{/if}}
{{#if terminalCommands}}
	<terminal_commands>
{{#each terminalCommands}}
		{{{this}}}
{{/each}}
	</terminal_commands>
{{/if}}
</agent_actions>
{{/if}}
{{#if agentResponse}}
<agent_response>
{{{agentResponse}}}
</agent_response>
{{else if wasInterrupted}}
<agent_was_interrupted/>
{{else if continues}}
<agent_continues/>
{{/if}}
`.trim(),Vp=fd.compile(gd);var yd=(e=>(e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink",e))(yd||{});const ms=class ms{static setClientWorkspaces(t){this._instance===void 0?this._instance=t:nu().warn("Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.")}static getClientWorkspaces(){if(this._instance===void 0)throw new Error("ClientWorkspaces not set");return this._instance}static reset(){this._instance=void 0}};ms._instance=void 0;let $r=ms;const Gp=()=>$r.getClientWorkspaces();var ge=(e=>(e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.openGuidelines="open-guidelines",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.updateRuleFileResponse="update-rule-file-response",e.getWorkspaceRoot="get-workspace-root",e.getWorkspaceRootResponse="get-workspace-root-response",e.autoImportRules="auto-import-rules",e.autoImportRulesOptionsResponse="auto-import-rules-options-response",e.autoImportRulesSelectionRequest="auto-import-rules-selection-request",e.autoImportRulesResponse="auto-import-rules-response",e.processSelectedPathsRequest="process-selected-paths-request",e.processSelectedPathsResponse="process-selected-paths-response",e))(ge||{}),Qt=(e=>(e.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",e.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",e.saveToolUseStatesRequest="save-tooluse-states-request",e.saveToolUseStatesResponse="save-tooluse-states-response",e.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",e.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",e))(Qt||{});const jp={default:{isClassifyAndDistill:!0,promptKey:"classify_and_distill_prompt"},v1_success:{isClassifyAndDistill:!0,promptKey:"classify_and_distill_success_prompt"},v2_scope:{isClassifyAndDistill:!0,promptKey:"classify_and_distill_scope_prompt"}};var wi=(e=>(e.memoryCreated="memory-created",e.memoryCreatedResponse="memory-created-response",e.memoryProcessed="memory-processed",e.getMemoriesByState="get-memories-by-state",e.getMemoriesByStateResponse="get-memories-by-state-response",e.updateMemoryState="update-memory-state",e.updateMemoryStateResponse="update-memory-state-response",e.flushPendingMemories="flush-pending-memories",e.flushPendingMemoriesResponse="flush-pending-memories-response",e))(wi||{});class qp{constructor(t,n,r){f(this,"_taskClient");f(this,"getChatInitData",async()=>{const t=await this._asyncMsgSender.send({type:S.chatLoaded},3e4);if(t.data.enableDebugFeatures)try{console.log("Running hello world test...");const n=await async function(r){return(await eu(ou,new Ho({sendMessage:s=>{r.postMessage(s)},onReceiveMessage:s=>{const o=i=>{s(i.data)};return window.addEventListener("message",o),()=>{window.removeEventListener("message",o)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",n)}catch(n){console.error("Hello world error:",n)}return t.data});f(this,"reportWebviewClientEvent",t=>{this._asyncMsgSender.send({type:S.reportWebviewClientMetric,data:{webviewName:Xo.chat,client_metric:t,value:1}})});f(this,"trackEventWithTypes",(t,n)=>{this._asyncMsgSender.sendToSidecar({type:ur.trackAnalyticsEvent,data:{eventName:t,properties:n}},5e3)});f(this,"trackExperimentViewed",(t,n,r)=>{this._asyncMsgSender.sendToSidecar({type:ur.trackExperimentViewedEvent,data:{experimentName:t,treatment:n,properties:r}},5e3)});f(this,"reportAgentSessionEvent",t=>{this._asyncMsgSender.sendToSidecar({type:U.reportAgentSessionEvent,data:t})});f(this,"reportAgentRequestEvent",t=>{this._asyncMsgSender.sendToSidecar({type:U.reportAgentRequestEvent,data:t})});f(this,"getSuggestions",async(t,n=!1)=>{const r={rootPath:"",relPath:t},a=this.findFiles(r,6),s=this.findRecentlyOpenedFiles(r,6),o=this.findFolders(r,3),i=this.findExternalSources(t,n),c=this._flags.enableRules?this.findRules(t,6):Promise.resolve([]),[l,p,u,d,h]=await Promise.all([dt(a,[]),dt(s,[]),dt(o,[]),dt(i,[]),dt(c,[])]),m=(g,v)=>({...Vu(g),[v]:g}),y=[...l.map(g=>m(g,"file")),...u.map(g=>m(g,"folder")),...p.map(g=>m(g,"recentFile")),...d.map(g=>({label:g.name,name:g.name,id:g.id,externalSource:g})),...h.map(g=>({...Gu(g),rule:g}))];if(this._flags.enablePersonalities){const g=this.getPersonalities(t);g.length>0&&y.push(...g)}return y});f(this,"getPersonalities",t=>{if(!this._flags.enablePersonalities)return[];if(t==="")return ga;const n=t.toLowerCase();return ga.filter(r=>{const a=r.personality.description.toLowerCase(),s=r.label.toLowerCase();return a.includes(n)||s.includes(n)})});f(this,"sendAction",t=>{this._host.postMessage({type:S.mainPanelPerformAction,data:t})});f(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:S.showAugmentPanel})});f(this,"showNotification",t=>{this._host.postMessage({type:S.showNotification,data:t})});f(this,"openConfirmationModal",async t=>(await this._asyncMsgSender.send({type:S.openConfirmationModal,data:t},1e9)).data.ok);f(this,"clearMetadataFor",t=>{this._host.postMessage({type:S.chatClearMetadata,data:t})});f(this,"resolvePath",async(t,n=void 0)=>{const r=await this._asyncMsgSender.send({type:S.resolveFileRequest,data:{...t,exactMatch:!0,maxResults:1,searchScope:n}},5e3);if(r.data)return r.data});f(this,"resolveSymbols",async(t,n)=>(await this._asyncMsgSender.send({type:S.findSymbolRequest,data:{query:t,searchScope:n}},3e4)).data);f(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:S.getDiagnosticsRequest},1e3)).data);f(this,"findFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:S.findFileRequest,data:{...t,maxResults:n}},5e3)).data);f(this,"findFolders",async(t,n=12)=>(await this._asyncMsgSender.send({type:S.findFolderRequest,data:{...t,maxResults:n}},5e3)).data);f(this,"findRecentlyOpenedFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:S.findRecentlyOpenedFilesRequest,data:{...t,maxResults:n}},5e3)).data);f(this,"findExternalSources",async(t,n=!1)=>this._flags.enableExternalSourcesInChat?n?[]:(await this._asyncMsgSender.send({type:S.findExternalSourcesRequest,data:{query:t,source_types:[]}},5e3)).data.sources??[]:[]);f(this,"findRules",async(t,n=12)=>(await this._asyncMsgSender.sendToSidecar({type:ge.getRulesListRequest,data:{query:t,maxResults:n}})).data.rules);f(this,"openFile",t=>{this._host.postMessage({type:S.openFile,data:t})});f(this,"saveFile",t=>this._host.postMessage({type:S.saveFile,data:t}));f(this,"loadFile",t=>this._host.postMessage({type:S.loadFile,data:t}));f(this,"openMemoriesFile",()=>{this._host.postMessage({type:S.openMemoriesFile})});f(this,"canShowTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:S.canShowTerminal,data:{terminalId:t,command:n}},5e3)).data.canShow}catch(r){return console.error("Failed to check if terminal can be shown:",r),!1}});f(this,"showTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:S.showTerminal,data:{terminalId:t,command:n}},5e3)).data.success}catch(r){return console.error("Failed to show terminal:",r),!1}});f(this,"createFile",(t,n)=>{this._host.postMessage({type:S.chatCreateFile,data:{code:t,relPath:n}})});f(this,"openScratchFile",async(t,n="shellscript")=>{await this._asyncMsgSender.send({type:S.openScratchFileRequest,data:{content:t,language:n}},1e4)});f(this,"resolveWorkspaceFileChunk",async t=>{try{return(await this._asyncMsgSender.send({type:S.resolveWorkspaceFileChunkRequest,data:t},5e3)).data}catch{return}});f(this,"smartPaste",t=>{this._host.postMessage({type:S.chatSmartPaste,data:t})});f(this,"getHydratedTask",async t=>this._taskClient.getHydratedTask(t));f(this,"updateHydratedTask",async(t,n)=>this._taskClient.updateHydratedTask(t,n));f(this,"setCurrentRootTaskUuid",t=>{this._taskClient.setCurrentRootTaskUuid(t)});f(this,"createTask",async(t,n,r)=>this._taskClient.createTask(t,n,r));f(this,"updateTask",async(t,n,r)=>this._taskClient.updateTask(t,n,r));f(this,"saveChat",async(t,n,r)=>this._asyncMsgSender.send({type:S.saveChat,data:{conversationId:t,chatHistory:n,title:r}},5e3));f(this,"updateUserGuidelines",t=>{this._host.postMessage({type:S.updateUserGuidelines,data:t})});f(this,"updateWorkspaceGuidelines",t=>{this._host.postMessage({type:S.updateWorkspaceGuidelines,data:t})});f(this,"openSettingsPage",t=>{this._host.postMessage({type:S.openSettingsPage,data:t})});f(this,"_activeRetryStreams",new Map);f(this,"cancelChatStream",async t=>{var n;(n=this._activeRetryStreams.get(t))==null||n.cancel(),await this._asyncMsgSender.send({type:S.chatUserCancel,data:{requestId:t}},1e4)});f(this,"sendUserRating",async(t,n,r,a="")=>{const s={requestId:t,rating:r,note:a,mode:n},o={type:S.chatRating,data:s};return(await this._asyncMsgSender.send(o,3e4)).data});f(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:S.usedChat})});f(this,"createProject",t=>{this._host.postMessage({type:S.mainPanelCreateProject,data:{name:t}})});f(this,"openProjectFolder",()=>{this._host.postMessage({type:S.mainPanelPerformAction,data:"open-folder"})});f(this,"closeProjectFolder",()=>{this._host.postMessage({type:S.mainPanelPerformAction,data:"close-folder"})});f(this,"cloneRepository",()=>{this._host.postMessage({type:S.mainPanelPerformAction,data:"clone-repository"})});f(this,"grantSyncPermission",()=>{this._host.postMessage({type:S.mainPanelPerformAction,data:"grant-sync-permission"})});f(this,"startRemoteMCPAuth",t=>{this._host.postMessage({type:S.startRemoteMCPAuth,data:{name:t}})});f(this,"callTool",async(t,n,r,a,s,o)=>{const i={type:S.callTool,data:{chatRequestId:t,toolUseId:n,name:r,input:a,chatHistory:s,conversationId:o}};return(await this._asyncMsgSender.send(i,0)).data});f(this,"cancelToolRun",async(t,n)=>{const r={type:S.cancelToolRun,data:{requestId:t,toolUseId:n}};await this._asyncMsgSender.send(r,0)});f(this,"checkSafe",async t=>{const n={type:zt.checkToolCallSafeRequest,data:t};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});f(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:zt.closeAllToolProcesses},0)});f(this,"getToolIdentifier",async t=>{const n={type:zt.getToolIdentifierRequest,data:{toolName:t}};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});f(this,"getChatMode",async()=>{const t={type:U.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.chatMode});f(this,"setChatMode",t=>{this._asyncMsgSender.send({type:S.chatModeChanged,data:{mode:t}})});f(this,"getAgentEditList",async(t,n)=>{const r={type:U.getEditListRequest,data:{fromTimestamp:t,toTimestamp:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data});f(this,"hasChangesSince",async t=>{const n={type:U.getEditListRequest,data:{fromTimestamp:t,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.edits.filter(r=>{var a,s;return((a=r.changesSummary)==null?void 0:a.totalAddedLines)||((s=r.changesSummary)==null?void 0:s.totalRemovedLines)}).length>0});f(this,"getToolCallCheckpoint",async t=>{const n={type:S.getToolCallCheckpoint,data:{requestId:t}};return(await this._asyncMsgSender.send(n,3e4)).data.checkpointNumber});f(this,"setCurrentConversation",t=>{this._asyncMsgSender.sendToSidecar({type:U.setCurrentConversation,data:{conversationId:t}},5e3)});f(this,"migrateConversationId",async(t,n)=>{await this._asyncMsgSender.sendToSidecar({type:U.migrateConversationId,data:{oldConversationId:t,newConversationId:n}},3e4)});f(this,"showAgentReview",(t,n,r,a=!0,s)=>{this._asyncMsgSender.sendToSidecar({type:U.chatReviewAgentFile,data:{qualifiedPathName:t,fromTimestamp:n,toTimestamp:r,retainFocus:a,useNativeDiffIfAvailable:s}})});f(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:U.chatAgentEditAcceptAll}),!0));f(this,"revertToTimestamp",async(t,n)=>(await this._asyncMsgSender.sendToSidecar({type:U.revertToTimestamp,data:{timestamp:t,qualifiedPathNames:n}}),!0));f(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:S.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);f(this,"getAgentEditChangesByRequestId",async t=>{const n={type:U.getEditChangesByRequestIdRequest,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});f(this,"getAgentEditContentsByRequestId",async t=>{const n={type:U.getAgentEditContentsByRequestId,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});f(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:S.triggerInitialOrientation})});f(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:S.getWorkspaceInfoRequest},5e3)).data}catch(t){return console.error("Error getting workspace info:",t),{}}});f(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:S.toggleCollapseUnchangedRegions})});f(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:S.checkAgentAutoModeApproval},5e3)).data);f(this,"setAgentAutoModeApproved",async t=>{await this._asyncMsgSender.send({type:S.setAgentAutoModeApproved,data:t},5e3)});f(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:U.checkHasEverUsedAgent},5e3)).data);f(this,"setHasEverUsedAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:U.setHasEverUsedAgent,data:t},5e3)});f(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:U.checkHasEverUsedRemoteAgent},5e3)).data);f(this,"setHasEverUsedRemoteAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:U.setHasEverUsedRemoteAgent,data:t},5e3)});f(this,"getChatRequestIdeState",async()=>{const t={type:S.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(t,3e4)).data});f(this,"reportError",t=>{this._host.postMessage({type:S.reportError,data:t})});f(this,"sendMemoryCreated",async t=>{await this._asyncMsgSender.sendToSidecar(t,5e3)});f(this,"sendFlushPendingMemories",async(t=3e4)=>{try{const n={type:wi.flushPendingMemories,data:{}};await this._asyncMsgSender.sendToSidecar(n,t)}catch{return}});f(this,"sendGitMessage",async t=>await this._asyncMsgSender.sendToSidecar(t,3e4));this._host=t,this._asyncMsgSender=n,this._flags=r,this._taskClient=new uu(n)}async*generateCommitMessage(){const t={type:S.generateCommitMessage},n=this._asyncMsgSender.stream(t,3e4,6e4);yield*Qn(n,()=>{},this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async*sendInstructionMessage(t,n){const r={instruction:t.request_message??"",selectedCodeDetails:n,requestId:t.request_id},a={type:S.chatInstructionMessage,data:r},s=this._asyncMsgSender.stream(a,3e4,6e4);yield*async function*(o){let i;try{for await(const c of o)i=c.data.requestId,yield{request_id:i,response_text:c.data.text,seen_state:be.unseen,status:Z.sent};yield{request_id:i,seen_state:be.unseen,status:Z.success}}catch(c){console.error("Error in chat instruction model reply stream:",c),yield{request_id:i,seen_state:be.unseen,status:Z.failed}}}(s)}async openGuidelines(t){this._host.postMessage({type:S.openGuidelines,data:t})}async*getExistingChatStream(t,n,r){const a=r==null?void 0:r.flags.enablePreferenceCollection,s=a?1e9:6e4,o=a?1e9:3e5,i={type:S.chatGetStreamRequest,data:{requestId:t,lastChunkId:n}},c=this._asyncMsgSender.stream(i,s,o);yield*Qn(c,this.reportError,this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async*startChatStream(t,n){const r=n==null?void 0:n.flags.enablePreferenceCollection,a=r?1e9:1e5,s=r?1e9:3e5,o={type:S.chatUserMessage,data:t},i=this._asyncMsgSender.stream(o,a,s);yield*Qn(i,this.reportError,this._flags.retryChatStreamTimeouts,this.trackEventWithTypes)}async checkToolExists(t){return(await this._asyncMsgSender.send({type:S.checkToolExists,toolName:t},0)).exists}async saveImage(t,n){const r=fa(await xn(t)),a=n??`${await ma(await Ln(r))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:S.chatSaveImageRequest,data:{filename:a,data:r}},1e4)).data}async saveAttachment(t,n){const r=fa(await xn(t)),a=n??`${await ma(await Ln(r))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:S.chatSaveAttachmentRequest,data:{filename:a,data:r}},1e4)).data}async loadImage(t){const n=await this._asyncMsgSender.send({type:S.chatLoadImageRequest,data:t},1e4),r=n.data?await Ln(n.data):void 0;if(!r)return;let a="application/octet-stream";const s=t.split(".").at(-1);s==="png"?a="image/png":s!=="jpg"&&s!=="jpeg"||(a="image/jpeg");const o=new File([r],t,{type:a});return await xn(o)}async deleteImage(t){await this._asyncMsgSender.send({type:S.chatDeleteImageRequest,data:t},1e4)}async*startChatStreamWithRetry(t,n,r){const a=new lu(t,n,(s,o)=>this.startChatStream(s,o),(r==null?void 0:r.maxRetries)??5,4e3,r==null?void 0:r.flags);this._activeRetryStreams.set(t,a);try{yield*a.getStream()}finally{this._activeRetryStreams.delete(t)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:S.getSubscriptionInfo},5e3)}async loadExchanges(t,n){if(n.length===0)return[];const r={type:Jt.loadExchangesByUuidsRequest,data:{conversationId:t,uuids:n}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data.exchanges}async saveExchanges(t,n){if(n.length===0)return;const r={type:Jt.saveExchangesRequest,data:{conversationId:t,exchanges:n}};await this._asyncMsgSender.sendToSidecar(r,3e4)}async deleteConversationExchanges(t){const n={type:Jt.deleteConversationExchangesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationToolUseStates(t){const n={type:Qt.loadConversationToolUseStatesRequest,data:{conversationId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.toolUseStates}async saveToolUseStates(t,n){if(Object.keys(n).length===0)return;const r={type:Qt.saveToolUseStatesRequest,data:{conversationId:t,toolUseStates:n}};await this._asyncMsgSender.sendToSidecar(r,3e4)}async deleteConversationToolUseStates(t){const n={type:Qt.deleteConversationToolUseStatesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}}async function*Qn(e,t=()=>{},n,r){let a;try{for await(const s of e){if(a=s.data.requestId,s.data.error)return console.error("Error in chat model reply stream:",s.data.error.displayErrorMessage),r==null||r(ru.MESSAGE_SEND_ERROR_DISPLAYED,{errorMessagePreview:s.data.error.displayErrorMessage.substring(0,100),requestId:a}),yield{request_id:a,seen_state:be.unseen,status:Z.failed,display_error_message:s.data.error.displayErrorMessage,isRetriable:s.data.error.isRetriable,shouldBackoff:s.data.error.shouldBackoff};const o={request_id:a,response_text:s.data.text,workspace_file_chunks:s.data.workspaceFileChunks,structured_output_nodes:bd(s.data.nodes),seen_state:be.unseen,status:Z.sent,lastChunkId:s.data.chunkId};s.data.stop_reason!=null&&(o.stop_reason=s.data.stop_reason),yield o}yield{request_id:a,seen_state:be.unseen,status:Z.success}}catch(s){let o,i;if(t({originalRequestId:a||"",sanitizedMessage:s instanceof Error?s.message:String(s),stackTrace:s instanceof Error&&s.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),s instanceof Ci&&n)switch(s.name){case"MessageTimeout":o=!0,i=!1;break;case"StreamTimeout":case"InvalidResponse":o=!1}console.error("Unexpected error in chat model reply stream:",s),yield{request_id:a,seen_state:be.unseen,status:Z.failed,isRetriable:o,shouldBackoff:i}}}async function dt(e,t){try{return await e}catch(n){return console.warn(`Error while resolving promise: ${n}`),t}}function bd(e){if(!e)return e;let t=!1;return e.filter(n=>n.type!==Oi.TOOL_USE||!t&&(t=!0,!0))}const Yp=15,Kp=1e3,vd=25e4,Hp=2e4;class Wp{constructor(t){f(this,"_enableEditableHistory",!1);f(this,"_enablePreferenceCollection",!1);f(this,"_enableRetrievalDataCollection",!1);f(this,"_enableDebugFeatures",!1);f(this,"_enableConversationDebugUtils",!1);f(this,"_enableRichTextHistory",!1);f(this,"_enableAgentSwarmMode",!1);f(this,"_modelDisplayNameToId",{});f(this,"_fullFeatured",!0);f(this,"_enableExternalSourcesInChat",!1);f(this,"_smallSyncThreshold",15);f(this,"_bigSyncThreshold",1e3);f(this,"_enableSmartPaste",!1);f(this,"_enableDirectApply",!1);f(this,"_summaryTitles",!1);f(this,"_suggestedEditsAvailable",!1);f(this,"_enableShareService",!1);f(this,"_maxTrackableFileCount",vd);f(this,"_enableDesignSystemRichTextEditor",!1);f(this,"_enableSources",!1);f(this,"_enableChatMermaidDiagrams",!1);f(this,"_smartPastePrecomputeMode",Ai.visibleHover);f(this,"_useNewThreadsMenu",!1);f(this,"_enableChatMermaidDiagramsMinVersion",!1);f(this,"_enablePromptEnhancer",!1);f(this,"_idleNewSessionNotificationTimeoutMs");f(this,"_idleNewSessionMessageTimeoutMs");f(this,"_enableChatMultimodal",!1);f(this,"_enableAgentMode",!1);f(this,"_enableAgentAutoMode",!1);f(this,"_enableRichCheckpointInfo",!1);f(this,"_agentMemoriesFilePathName");f(this,"_conversationHistorySizeThresholdBytes",44040192);f(this,"_userTier","unknown");f(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});f(this,"_truncateChatHistory",!1);f(this,"_enableBackgroundAgents",!1);f(this,"_enableNewThreadsList",!1);f(this,"_customPersonalityPrompts",{});f(this,"_enablePersonalities",!1);f(this,"_enableRules",!1);f(this,"_memoryClassificationOnFirstToken",!1);f(this,"_enableGenerateCommitMessage",!1);f(this,"_modelRegistry",{});f(this,"_modelInfoRegistry",{});f(this,"_agentChatModel","");f(this,"_enableModelRegistry",!1);f(this,"_enableTaskList",!1);f(this,"_clientAnnouncement","");f(this,"_useHistorySummary",!1);f(this,"_historySummaryParams","");f(this,"_enableExchangeStorage",!1);f(this,"_enableToolUseStateStorage",!1);f(this,"_retryChatStreamTimeouts",!1);f(this,"_enableCommitIndexing",!1);f(this,"_enableMemoryRetrieval",!1);f(this,"_enableAgentTabs",!1);f(this,"_isVscodeVersionOutdated",!1);f(this,"_vscodeMinVersion","");f(this,"_enableGroupedTools",!1);f(this,"_remoteAgentsResumeHintAvailableTtlDays",0);f(this,"_enableParallelTools",!1);f(this,"_enableAgentGitTracker",!1);f(this,"_enableLucideIcons",!1);f(this,"_memoriesParams",{});f(this,"_subscribers",new Set);f(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));f(this,"update",t=>{if(this._enableEditableHistory=t.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=t.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=t.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=t.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=t.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=t.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=t.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...t.modelDisplayNameToId},this._fullFeatured=t.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=t.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=t.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=t.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=t.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=t.enableDirectApply??this._enableDirectApply,this._summaryTitles=t.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=t.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=t.enableShareService??this._enableShareService,this._maxTrackableFileCount=t.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=t.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=t.enableSources??this._enableSources,this._enableChatMermaidDiagrams=t.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=t.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=t.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=t.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=t.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=t.idleNewSessionMessageTimeoutMs??(t.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=t.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=t.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=t.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=t.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=t.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=t.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=t.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=t.userTier??this._userTier,this._eloModelConfiguration=t.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=t.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=t.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=t.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=t.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=t.enablePersonalities??this._enablePersonalities,this._enableRules=t.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=t.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._enableGenerateCommitMessage=t.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=t.modelRegistry??this._modelRegistry,t.modelInfoRegistry){const n={};for(const[r,a]of Object.entries(t.modelInfoRegistry))if(typeof a=="string")try{n[r]=JSON.parse(a)}catch(s){console.error(`Failed to parse modelInfoRegistry entry for key "${r}"`,s)}else typeof a=="object"&&a!==null&&(n[r]=a);this._modelInfoRegistry=n}this._enableModelRegistry=t.enableModelRegistry??this._enableModelRegistry,this._agentChatModel=t.agentChatModel??this._agentChatModel,this._enableTaskList=t.enableTaskList??this._enableTaskList,this._clientAnnouncement=t.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=t.useHistorySummary??this._useHistorySummary,this._historySummaryParams=t.historySummaryParams??this._historySummaryParams,this._enableExchangeStorage=t.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=t.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=t.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=t.enableMemoryRetrieval??this._enableMemoryRetrieval,this._enableAgentTabs=t.enableAgentTabs??this._enableAgentTabs,this._isVscodeVersionOutdated=t.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=t.vscodeMinVersion??this._vscodeMinVersion,this._enableGroupedTools=t.enableGroupedTools??this._enableGroupedTools,this._remoteAgentsResumeHintAvailableTtlDays=t.remoteAgentsResumeHintAvailableTtlDays??this._remoteAgentsResumeHintAvailableTtlDays,this._enableToolUseStateStorage=t.enableToolUseStateStorage??this._enableToolUseStateStorage,this._enableParallelTools=t.enableParallelTools??this._enableParallelTools,this._enableAgentGitTracker=t.enableAgentGitTracker??this._enableAgentGitTracker,this._enableLucideIcons=t.enableLucideIcons??this._enableLucideIcons,this._memoriesParams=t.memoriesParams??this._memoriesParams,this._subscribers.forEach(n=>n(this))});f(this,"isModelIdValid",t=>t!==void 0&&(Object.values(this._modelDisplayNameToId).includes(t)||Object.values(this._modelRegistry).includes(t??"")||Object.keys(this._modelInfoRegistry).includes(t??"")));f(this,"getModelDisplayName",t=>{if(t!==void 0)return Object.keys(this._modelDisplayNameToId).find(n=>this._modelDisplayNameToId[n]===t)});t&&this.update(t)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((t,n)=>{const r=t.toLowerCase(),a=n.toLowerCase();return r==="default"&&a!=="default"?-1:a==="default"&&r!=="default"?1:t.localeCompare(n)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get agentChatModel(){return this._agentChatModel}get modelRegistry(){return this._modelRegistry}get modelInfoRegistry(){return this._modelInfoRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary}get historySummaryParams(){return this._historySummaryParams}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get enableAgentTabs(){return this._enableAgentTabs}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}get enableGroupedTools(){return this._enableGroupedTools}get remoteAgentsResumeHintAvailableTtlDays(){return this._remoteAgentsResumeHintAvailableTtlDays}get enableParallelTools(){return this._enableParallelTools}get enableAgentGitTracker(){return this._enableAgentGitTracker}get enableLucideIcons(){return this._enableLucideIcons}get memoriesParams(){return this._memoriesParams}}var _d=Vi,Ed=/\s/,Sd=function(e){for(var t=e.length;t--&&Ed.test(e.charAt(t)););return t},Td=/^\s+/,wd=ji,Nd=Gi,Id=function(e){return e&&e.slice(0,Sd(e)+1).replace(Td,"")},za=jr,kd=function(e){return typeof e=="symbol"||Nd(e)&&wd(e)=="[object Symbol]"},Rd=/^[-+]0x[0-9a-f]+$/i,Cd=/^0b[01]+$/i,Ad=/^0o[0-7]+$/i,Od=parseInt,Md=jr,er=function(){return _d.Date.now()},Ja=function(e){if(typeof e=="number")return e;if(kd(e))return NaN;if(za(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=za(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Id(e);var n=Cd.test(e);return n||Ad.test(e)?Od(e.slice(2),n?2:8):Rd.test(e)?NaN:+e},Pd=Math.max,xd=Math.min,Ld=function(e,t,n){var r,a,s,o,i,c,l=0,p=!1,u=!1,d=!0;if(typeof e!="function")throw new TypeError("Expected a function");function h(E){var _=r,T=a;return r=a=void 0,l=E,o=e.apply(T,_)}function m(E){var _=E-c;return c===void 0||_>=t||_<0||u&&E-l>=s}function y(){var E=er();if(m(E))return g(E);i=setTimeout(y,function(_){var T=t-(_-c);return u?xd(T,s-(_-l)):T}(E))}function g(E){return i=void 0,d&&r?h(E):(r=a=void 0,o)}function v(){var E=er(),_=m(E);if(r=arguments,a=this,c=E,_){if(i===void 0)return function(T){return l=T,i=setTimeout(y,t),p?h(T):o}(c);if(u)return clearTimeout(i),i=setTimeout(y,t),h(c)}return i===void 0&&(i=setTimeout(y,t)),o}return t=Ja(t)||0,Md(n)&&(p=!!n.leading,s=(u="maxWait"in n)?Pd(Ja(n.maxWait)||0,t):s,d="trailing"in n?!!n.trailing:d),v.cancel=function(){i!==void 0&&clearTimeout(i),l=0,r=c=a=i=void 0},v.flush=function(){return i===void 0?o:g(er())},v},Dd=jr;const Fd=Vr(function(e,t,n){var r=!0,a=!0;if(typeof e!="function")throw new TypeError("Expected a function");return Dd(n)&&(r="leading"in n?!!n.leading:r,a="trailing"in n?!!n.trailing:a),Ld(e,t,{leading:r,maxWait:t,trailing:a})});class Ud{constructor(t){f(this,"SIDECAR_TIMEOUT_MS",5e3);f(this,"getRulesList",async(t=!0)=>{const n={type:ge.getRulesListRequest,data:{includeGuidelines:t}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.rules});f(this,"createRule",async t=>{const n={type:ge.createRule,data:{ruleName:t.trim()}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.createdRule||null});f(this,"getWorkspaceRoot",async()=>{const t={type:ge.getWorkspaceRoot};return(await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)).data.workspaceRoot||""});f(this,"updateRuleFile",async(t,n)=>{const r={type:ge.updateRuleFile,data:{path:t,content:n}};await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS)});f(this,"deleteRule",async(t,n=!0)=>{const r={type:ge.deleteRule,data:{path:t,confirmed:n}};await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS)});f(this,"processSelectedPaths",async(t,n=!0)=>{const r={type:ge.processSelectedPathsRequest,data:{selectedPaths:t,autoImport:n}},a=await this._asyncMsgSender.sendToSidecar(r,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:a.data.importedRulesCount,directoryOrFile:a.data.directoryOrFile,errors:a.data.errors}});f(this,"getAutoImportOptions",async()=>{const t={type:ge.autoImportRules};return await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)});f(this,"processAutoImportSelection",async t=>{const n={type:ge.autoImportRulesSelectionRequest,data:{selectedLabel:t}},r=await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:r.data.importedRulesCount,duplicatesCount:r.data.duplicatesCount,totalAttempted:r.data.totalAttempted,source:r.data.source}});this._asyncMsgSender=t}}class Xp{constructor(t,n=!0){f(this,"_rulesFiles",en([]));f(this,"_loading",en(!0));f(this,"_extensionClientRules");f(this,"_requestRulesThrottled",Fd(async()=>{this._loading.set(!0);try{const t=await this._extensionClientRules.getRulesList(this.includeGuidelines);this._rulesFiles.set(t)}catch(t){console.error("Failed to get rules list:",t)}finally{this._loading.set(!1)}},250,{leading:!0,trailing:!0}));this._msgBroker=t,this.includeGuidelines=n,this._extensionClientRules=new Ud(this._msgBroker),this.requestRules()}handleMessageFromExtension(t){return!(!t.data||t.data.type!==S.getRulesListResponse)&&(this._rulesFiles.set(t.data.data),this._loading.set(!1),!0)}async requestRules(){return this._requestRulesThrottled()}async createRule(t){try{const n=await this._extensionClientRules.createRule(t);return await this.requestRules(),n}catch(n){throw console.error("Failed to create rule:",n),n}}async getWorkspaceRoot(){try{return await this._extensionClientRules.getWorkspaceRoot()}catch(t){return console.error("Failed to get workspace root:",t),""}}async updateRuleContent(t){const n=cr.formatRuleFileForMarkdown(t);try{await this._extensionClientRules.updateRuleFile(t.path,n)}catch(r){console.error("Failed to update rule file:",r)}await this.requestRules()}async deleteRule(t){try{await this._extensionClientRules.deleteRule(t,!0),await this.requestRules()}catch(n){throw console.error("Failed to delete rule:",n),n}}async processSelectedPaths(t){try{const n=await this._extensionClientRules.processSelectedPaths(t,!0);return await this.requestRules(),n}catch(n){throw console.error("Failed to process selected paths:",n),n}}async getAutoImportOptions(){return await this._extensionClientRules.getAutoImportOptions()}async processAutoImportSelection(t){try{const n=await this._extensionClientRules.processAutoImportSelection(t.label);return await this.requestRules(),n}catch(n){throw console.error("Failed to process auto-import selection:",n),n}}getCachedRules(){return this._rulesFiles}getLoading(){return this._loading}}var $d=so('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor"></path></svg>');function Bd(e){var t=$d();A(e,t)}const Tt=class Tt{constructor(t=void 0){f(this,"_lastFocusAnchorElement");f(this,"_focusedIndexStore",en(void 0));f(this,"focusedIndex",this._focusedIndexStore);f(this,"_rootElement");f(this,"_triggerElement");f(this,"_getItems",()=>{var r;const t=(r=this._rootElement)==null?void 0:r.querySelectorAll(`.${Tt.ITEM_CLASS}`),n=t==null?void 0:t[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(t??[])});f(this,"_recomputeFocusAnchor",t=>{var s;const n=(s=this._parentContext)==null?void 0:s._getItems(),r=n==null?void 0:n.indexOf(t);if(r===void 0||n===void 0)return;const a=Math.max(r-1,0);this._lastFocusAnchorElement=n[a]});f(this,"registerRoot",t=>{this._rootElement=t,t.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},r=a=>{t.contains(a.relatedTarget)||this._focusedIndexStore.set(void 0)};return t.addEventListener("focusin",n),t.addEventListener("focusout",r),this._getItems(),{destroy:()=>{this._removeFromTrapStack(),this._rootElement=void 0,t.removeEventListener("keydown",this._onKeyDown),t.removeEventListener("focusin",n),t.removeEventListener("focusout",r),this._focusedIndexStore.set(void 0)}}});f(this,"registerTrigger",t=>(this._triggerElement=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??t,{destroy:()=>{this._triggerElement=void 0}}));f(this,"_onKeyDown",t=>{var n;switch(t.key){case"ArrowUp":t.preventDefault(),this.focusPrev();break;case"ArrowDown":t.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const r=this.getCurrentFocusedIdx();if(r===void 0||this.parentContext)break;(!t.shiftKey&&r===this._getItems().length-1||t.shiftKey&&r===0)&&(t.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});f(this,"_requestClose",()=>{var t;(t=this._rootElement)==null||t.dispatchEvent(new Hi)});f(this,"getCurrentFocusedIdx",()=>{const t=this._getItems().findIndex(r=>r===document.activeElement),n=t===-1?void 0:t;return this._focusedIndexStore.set(n),n});f(this,"setFocusedIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const r=jt(t,n.length);this._focusedIndexStore.set(r)});f(this,"focusIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const r=jt(t,n.length),a=n[r];a==null||a.focus(),this._focusedIndexStore.set(r)});f(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const t=this._lastFocusAnchorElement,n=t?this._parentContext._getItems().indexOf(t):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});f(this,"focusNext",()=>{const t=this._getItems();if(t.length===0)return;const n=jt(t.findIndex(r=>r===document.activeElement)+1,t.length);t[n].focus(),this._focusedIndexStore.set(n)});f(this,"focusPrev",()=>{var r;const t=this._getItems();if(t.length===0)return;const n=jt(t.findIndex(a=>a===document.activeElement)-1,t.length);(r=t[n])==null||r.focus(),this._focusedIndexStore.set(n)});f(this,"clickFocusedItem",async()=>{const t=document.activeElement;t&&(t.click(),await tr())});f(this,"_addToTrapStack",()=>{this._rootElement&&fs.add(this._rootElement)});f(this,"_removeFromTrapStack",()=>{this._rootElement&&fs.remove(this._rootElement)});f(this,"handleOpenChange",t=>{t?this._addToTrapStack():this._removeFromTrapStack()});this._parentContext=t}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};f(Tt,"CONTEXT_KEY","augment-dropdown-menu-focus"),f(Tt,"ITEM_CLASS","js-dropdown-menu__focusable-item");let te=Tt;function jt(e,t){return(e%t+t)%t}const et="augment-dropdown-menu-content";var Vd=H("<div><!></div>"),Gd=H('<div class="l-dropdown-menu-augment__container svelte-o54ind"><!></div>');function Za(e,t){se(t,!1);const[n,r]=Ae(),a=()=>ye(h,"$sizeState",n),s=_e();let o=R(t,"size",8,2),i=R(t,"onEscapeKeyDown",8,()=>{}),c=R(t,"onClickOutside",8,()=>{}),l=R(t,"onRequestClose",8,()=>{}),p=R(t,"side",8,"top"),u=R(t,"align",8,"center");const d={size:en(o())},h=d.size;Gr(et,d);const m=Y(te.CONTEXT_KEY),y=Y(qt.CONTEXT_KEY);ve(()=>xe(o()),()=>{h.set(o())}),ve(()=>{},()=>{Pi(Ee(s,y.state),"$openState",n)}),tt(),ue(),fe("keydown",xi,function(g){if(ye(x(s),"$openState",n).open&&g.key==="Tab"&&!g.shiftKey){if(m.getCurrentFocusedIdx()!==void 0)return;g.preventDefault(),m==null||m.focusIdx(0)}}),Wi(e,{onEscapeKeyDown:i(),onClickOutside:function(g){var v;return y.externalControlSetOpen(!1),(v=c())==null?void 0:v(g)},onRequestClose:l(),get side(){return p()},get align(){return u()},$$events:{keydown(g){we.call(this,t,g)}},children:(g,v)=>{var E=Gd(),_=Q(E);Xi(_,{get size(){return a()},insetContent:!0,includeBackground:!1,children:(T,w)=>{var I=Vd(),N=Q(I);K(N,t,"default",{},null),nr(I,F=>{var M;return(M=m.registerRoot)==null?void 0:M.call(m,F)}),nt(()=>rt(I,1,`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${a()}`,"svelte-o54ind")),A(T,I)},$$slots:{default:!0}}),A(g,E)},$$slots:{default:!0}}),ae(),r()}var jd=H('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),qd=H('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),Yd=H("<!> <!> <!>",1);function Br(e,t){const n=to(t),r=B(t,["children","$$slots","$$events","$$legacy"]),a=B(r,["highlight","disabled","color","onSelect"]);se(t,!1);const[s,o]=Ae(),i=()=>ye(v,"$sizeState",s),c=_e(),l=_e(),p=_e();let u=R(t,"highlight",24,()=>{}),d=R(t,"disabled",24,()=>{}),h=R(t,"color",24,()=>{}),m=R(t,"onSelect",8,()=>{});const y=Y(et),g=Y(te.CONTEXT_KEY),v=y.size;function E(N){var k;if(d())return;const F=(k=g.rootElement)==null?void 0:k.querySelectorAll(`.${te.ITEM_CLASS}`);if(!F)return;const M=Array.from(F).findIndex(ne=>ne===N);M!==-1&&g.setFocusedIdx(M)}ve(()=>(x(c),x(l),xe(a)),()=>{Ee(c,a.class),Ee(l,ao(a,["class"]))}),ve(()=>(xe(d()),xe(u()),x(c)),()=>{Ee(p,[d()?"":te.ITEM_CLASS,"c-dropdown-menu-augment__item",u()?"c-dropdown-menu-augment__item--highlighted":"",x(c)].join(" "))}),tt(),ue();const _=tn(()=>h()??"neutral"),T=tn(()=>!h());var w=ys(()=>gs("dropdown-menu-item","highlighted",u())),I=ys(()=>gs("dropdown-menu-item","disabled",d()));qi(e,Ge({get class(){return x(p)},get size(){return i()},variant:"ghost",get color(){return x(_)},get highContrast(){return x(T)},alignment:"left",get disabled(){return d()}},()=>x(w),()=>x(I),()=>x(l),{$$events:{click:N=>{N.currentTarget instanceof HTMLElement&&E(N.currentTarget),m()(N)},mouseover:N=>{N.currentTarget instanceof HTMLElement&&E(N.currentTarget)},mousedown:N=>{N.preventDefault(),N.stopPropagation()}},children:(N,F)=>{var M=Yd(),k=de(M),ne=z=>{var He=jd(),We=Q(He);K(We,t,"iconLeft",{},null),A(z,He)};ft(k,z=>{je(()=>n.iconLeft)&&z(ne)});var Mt=gt(k,2);no(Mt,{get size(){return i()},children:(z,He)=>{var We=ke(),Ii=de(We);K(Ii,t,"default",{},null),A(z,We)},$$slots:{default:!0}});var C=gt(Mt,2),Ue=z=>{var He=qd(),We=Q(He);K(We,t,"iconRight",{},null),A(z,He)};ft(C,z=>{je(()=>n.iconRight)&&z(Ue)}),A(N,M)},$$slots:{default:!0}})),ae(),o()}var Kd=so("<svg><!></svg>");function Hd(e,t){const n=B(t,["children","$$slots","$$events","$$legacy"]);var r=Kd();oo(r,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 320 512",...n}));var a=Q(r);Yi(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M305 239c9.4 9.4 9.4 24.6 0 33.9L113 465c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l175-175L79 81c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0z"/>',!0),A(e,r)}function Qa(e,t){const n=B(t,["children","$$slots","$$events","$$legacy"]),r=B(n,[]);Br(e,Ge({class:"c-dropdown-menu-augment__breadcrumb-chevron"},()=>r,{children:(a,s)=>{var o=ke(),i=de(o);K(i,t,"default",{},null),A(a,o)},$$slots:{default:!0,iconRight:(a,s)=>{Hd(a,{slot:"iconRight"})}}}))}var Wd=H("<div><!></div>");const Ni=Symbol("command-scope-node");function Xd(){return Y(Ni)}function zp(e){const t=[];let n=e;for(;n;)t.push(n.value),n=n.parent??void 0;return t.reverse()}function zd(e,t){se(t,!0);const n=Xd()??null,r=function(o,i){const c={value:o,parent:i,children:new Set};return i&&i.children.add(c),c}(t.scope,n);(function(o){Gr(Ni,o)})(r),Li(()=>{r.value=t.scope}),Di(()=>{(function(o){o.parent&&o.parent.children.delete(o),o.parent=null})(r)});var a=ke(),s=de(a);Ki(s,()=>t.children??Fi),A(e,a),ae()}function eo(e,t){const n=B(t,["children","$$slots","$$events","$$legacy"]),r=B(n,["requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex","defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn"]);se(t,!1);let a=R(t,"defaultOpen",24,()=>{}),s=R(t,"open",24,()=>{}),o=R(t,"onOpenChange",24,()=>{}),i=R(t,"delayDurationMs",24,()=>{}),c=R(t,"nested",24,()=>{}),l=R(t,"onHoverStart",8,()=>{}),p=R(t,"onHoverEnd",8,()=>{}),u=R(t,"triggerOn",24,()=>[Yt.Click]),d=_e();const h=()=>{var w;return(w=x(d))==null?void 0:w.requestOpen()},m=()=>{var w;return(w=x(d))==null?void 0:w.requestClose()},y=w=>_.focusIdx(w),g=w=>_.setFocusedIdx(w),v=()=>_.getCurrentFocusedIdx(),E=Y(te.CONTEXT_KEY),_=new te(E);Gr(te.CONTEXT_KEY,_);const T=_.focusedIndex;return ue(),ro(zi(e,Ge({get defaultOpen(){return a()},get open(){return s()},onOpenChange:function(w){var I;_.handleOpenChange(w),(I=o())==null||I(w)},get delayDurationMs(){return i()},onHoverStart:l(),onHoverEnd:p(),get triggerOn(){return u()},get nested(){return c()}},()=>r,{children:(w,I)=>{zd(w,{scope:"dropdown-menu",children:(N,F)=>{var M=ke(),k=de(M);K(k,t,"default",{},null),A(N,M)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0})),w=>Ee(d,w),()=>x(d)),Xe(t,"requestOpen",h),Xe(t,"requestClose",m),Xe(t,"focusIdx",y),Xe(t,"setFocusedIdx",g),Xe(t,"getCurrentFocusedIdx",v),Xe(t,"focusedIndex",T),ae({requestOpen:h,requestClose:m,focusIdx:y,setFocusedIdx:g,getCurrentFocusedIdx:v,focusedIndex:T})}var Jd=H("<div></div>");function Zd(e,t){let n=R(t,"size",8,1),r=R(t,"orientation",8,"horizontal"),a=R(t,"useCurrentColor",8,!1),s=R(t,"class",8,"");var o=Jd();let i;nt(c=>i=rt(o,1,`c-separator c-separator--size-${n()===.5?"0_5":n()} c-separator--orientation-${r()} ${s()}`,"svelte-o0csoy",i,c),[()=>({"c-separator--current-color":a()})],tn),A(e,o)}var Qd=H("<div><!></div>"),ep=H('<label class="c-text-field-label svelte-vuqlvc"><!></label>'),tp=H('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),np=H('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),rp=H("<!> <input/> <!>",1),sp=H("<div><!> <!></div>");function ap(e,t){const n=to(t),r=B(t,["children","$$slots","$$events","$$legacy"]),a=B(r,["variant","size","color","textInput","value","id"]);se(t,!1);const s=_e(),o=_e(),i=_e(),c=$i();let l=R(t,"variant",8,"surface"),p=R(t,"size",8,2),u=R(t,"color",24,()=>{}),d=R(t,"textInput",28,()=>{}),h=R(t,"value",12,""),m=R(t,"id",24,()=>{});const y=`text-field-${Math.random().toString(36).substring(2,11)}`;function g(w){c("change",w)}ve(()=>xe(m()),()=>{Ee(s,m()||y)}),ve(()=>(x(o),x(i),xe(a)),()=>{Ee(o,a.class),Ee(i,ao(a,["class"]))}),tt(),ue();var v=sp();rt(v,1,"c-text-field svelte-vuqlvc",null,{},{"c-text-field--has-left-icon":n.iconLeft!==void 0,"c-text-field--has-right-icon":n.iconRight!==void 0});var E=Q(v),_=w=>{var I=ep(),N=Q(I);K(N,t,"label",{},null),nt(()=>Bi(I,"for",x(s))),A(w,I)};ft(E,w=>{je(()=>n.label)&&w(_)});var T=gt(E,2);Zi(T,{get variant(){return l()},get size(){return p()},get color(){return u()},children:(w,I)=>{var N=rp(),F=de(N),M=C=>{var Ue=tp(),z=Q(Ue);K(z,t,"iconLeft",{},null),A(C,Ue)};ft(F,C=>{je(()=>n.iconLeft)&&C(M)});var k=gt(F,2);oo(k,()=>({spellCheck:"false",class:`c-text-field__input c-base-text-input__input ${x(o)}`,id:x(s),...x(i)}),void 0,"svelte-vuqlvc"),ro(k,C=>d(C),()=>d());var ne=gt(k,2),Mt=C=>{var Ue=np(),z=Q(Ue);K(z,t,"iconRight",{},null),A(C,Ue)};ft(ne,C=>{je(()=>n.iconRight)&&C(Mt)}),Ji(k,h),fe("change",k,g),fe("click",k,function(C){we.call(this,t,C)}),fe("keydown",k,function(C){we.call(this,t,C)}),fe("input",k,function(C){we.call(this,t,C)}),fe("blur",k,function(C){we.call(this,t,C)}),fe("dblclick",k,function(C){we.call(this,t,C)}),fe("focus",k,function(C){we.call(this,t,C)}),fe("mouseup",k,function(C){we.call(this,t,C)}),fe("selectionchange",k,function(C){we.call(this,t,C)}),A(w,N)},$$slots:{default:!0}}),A(e,v),ae()}var op=H("<div><!></div>"),ip=H("<div><!></div>");const Jp={BreadcrumbBackItem:function(e,t){const n=B(t,["children","$$slots","$$events","$$legacy"]),r=B(n,[]);Br(e,Ge({class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},()=>r,{children:(a,s)=>{var o=ke(),i=de(o);K(i,t,"default",{},null),A(a,o)},$$slots:{default:!0,iconLeft:(a,s)=>{Bd(a)}}}))},BreadcrumbItem:Qa,Content:Za,Item:Br,Label:function(e,t){se(t,!1);const[n,r]=Ae(),a=()=>ye(o,"$sizeState",n),s=_e(),o=Y(et).size;ve(()=>a(),()=>{Ee(s,["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${a()}`].join(" "))}),tt(),ue();var i=Wd(),c=Q(i);no(c,{get size(){return a()},weight:"regular",children:(l,p)=>{var u=ke(),d=de(u);K(d,t,"default",{},null),A(l,u)},$$slots:{default:!0}}),nt(()=>rt(i,1,bs(x(s)),"svelte-gehsvg")),A(e,i),ae(),r()},Root:eo,Separator:function(e,t){se(t,!1);const[n,r]=Ae(),a=Y(et).size;ue();var s=Qd();Zd(Q(s),{size:4,orientation:"horizontal"}),nt(()=>rt(s,1,`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${ye(a,"$sizeState",n)}`,"svelte-24h9u")),A(e,s),ae(),r()},Sub:function(e,t){const n=B(t,["children","$$slots","$$events","$$legacy"]),r=B(n,[]);se(t,!1),ue();const a=tn(()=>(xe(Yt),je(()=>[Yt.Click,Yt.Hover])));eo(e,Ge({nested:!0,get triggerOn(){return x(a)}},()=>r,{children:(s,o)=>{var i=ke(),c=de(i);K(c,t,"default",{},null),A(s,i)},$$slots:{default:!0}})),ae()},SubContent:function(e,t){const n=B(t,["children","$$slots","$$events","$$legacy"]),r=B(n,[]);se(t,!1);const[a,s]=Ae(),o=()=>ye(p,"$didOpen",a),i=Y(et).size,c=Y(te.CONTEXT_KEY),l=Y(qt.CONTEXT_KEY),p=Ui(l.state,u=>u.open);ve(()=>(o(),tr),()=>{o()&&tr().then(()=>c==null?void 0:c.focusIdx(0))}),ve(()=>o(),()=>{!o()&&(c==null||c.popNestedFocus())}),tt(),ue(),Za(e,Ge(()=>r,{side:"right",align:"start",get size(){return ye(i,"$sizeState",a)},children:(u,d)=>{var h=ke(),m=de(h);K(m,t,"default",{},null),A(u,h)},$$slots:{default:!0}})),ae(),s()},SubTrigger:function(e,t){se(t,!1);const[n,r]=Ae(),a=Y(qt.CONTEXT_KEY).state;ue(),vs(e,{children:(s,o)=>{Qa(s,{get highlight(){return ye(a,"$stateStore",n).open},children:(i,c)=>{var l=ke(),p=de(l);K(p,t,"default",{},null),A(i,l)},$$slots:{default:!0}})},$$slots:{default:!0}}),ae(),r()},TextFieldItem:function(e,t){const n=B(t,["children","$$slots","$$events","$$legacy"]),r=B(n,["value"]);se(t,!1);const[a,s]=Ae(),o=()=>ye(l,"$sizeState",a),i=_e();let c=R(t,"value",12,"");const l=Y(et).size;ve(()=>o(),()=>{Ee(i,["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${o()}`].join(" "))}),tt(),ue();var p=op();ap(Q(p),Ge({get class(){return xe(te),je(()=>te.ITEM_CLASS)},get size(){return o()}},()=>r,{get value(){return c()},set value(u){c(u)},$$legacy:!0})),nt(()=>rt(p,1,bs(x(i)),"svelte-1xu00bc")),A(e,p),ae(),s()},Trigger:function(e,t){se(t,!1);const[n,r]=Ae(),a=()=>ye(c,"$openState",n);let s=R(t,"referenceClientRect",24,()=>{});const o=Y(te.CONTEXT_KEY),i=Y(qt.CONTEXT_KEY),c=i.state;ue(),vs(e,{get referenceClientRect(){return s()},$$events:{keydown:async l=>{switch(l.key){case"ArrowUp":l.preventDefault(),l.stopPropagation(),a().open||await o.clickFocusedItem(),o==null||o.focusIdx(-1);break;case"ArrowDown":l.preventDefault(),l.stopPropagation(),a().open||await o.clickFocusedItem(),o==null||o.focusIdx(0);break;case"Enter":l.preventDefault(),l.stopPropagation(),o==null||o.clickFocusedItem()}}},children:(l,p)=>{var u=ip(),d=Q(u);K(d,t,"default",{},null),nr(u,h=>{var m;return(m=o.registerTrigger)==null?void 0:m.call(o,h)}),nr(u,h=>{var m;return(m=i.registerTrigger)==null?void 0:m.call(i,h)}),A(l,u)},$$slots:{default:!0}}),ae(),r()}};export{Jo as $,Ep as A,Fu as B,Wp as C,Jp as D,qp as E,yd as F,Uu as G,Ip as H,Mp as I,Bu as J,Vu as K,Br as L,cu as M,xp as N,Pp as O,wp as P,Ap as Q,Xp as R,vu as S,ap as T,Cp as U,Op as V,Np as W,Hd as X,du as Y,Su as Z,Fd as _,cr as a,mu as a0,hu as a1,Hp as a2,_u as a3,Eu as a4,Sp as a5,kp as a6,Zd as a7,zd as a8,ku as a9,Iu as aa,ee as ab,Bd as ac,Cu as ad,Ru as ae,Nu as af,wu as ag,Xd as ah,zp as ai,Tu as aj,wi as ak,Zo as al,yu as am,jp as an,Tc as ao,li as ap,Fp as aq,Dp as ar,ds as as,wc as at,$p as au,Up as av,bu as b,fu as c,Mu as d,Pu as e,U as f,Vp as g,gu as h,nu as i,Gp as j,ru as k,su as l,$u as m,Lp as n,Rp as o,Bp as p,_p as q,vd as r,Kp as s,Yp as t,Au as u,Ou as v,xu as w,Lu as x,Tp as y,Du as z};
