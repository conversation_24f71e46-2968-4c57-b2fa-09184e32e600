import{aY as I,aZ as k,a_ as ce,a$ as j,b0 as X,b1 as ve,b2 as He,b3 as q,b4 as Be,b5 as lt,b6 as yt,b7 as xs,b8 as F,b9 as te,ba as ks,bb as Tt,bc as H,bd as En,be as dt,bf as Ps,bg as ye,bh as cn,bi as un,bj as $s,bk as ln,bl as Me,bm as ke,bn as Rs,bo as Or,bp as Cs,bq as Os,br as fe,bs as Ee,bt as As,bu as Ht,bv as Ds,bw as pt,bx as wn,by as dn,bz as Is,bA as Sn,bB as ze,bC as Ls,bD as Ns,bE as qs,bF as Ms,bG as Fs,bH as Ar,bI as Dr,bJ as Ir,bK as js,bL as Tn,bM as Bt,bN as _t,bO as xn,bP as Us,bQ as kn,bR as Pn,bS as Hs,bT as Bs,bU as zs,bV as Ws,bW as Gs,bX as Ks,bY as pn,bZ as he,b_ as ge,b$ as ft,c0 as Xs,c1 as K,c2 as $n,c3 as Rn,c4 as Lr,c5 as Nr,c6 as Js,c7 as bt,c8 as W,c9 as qr,ca as We,cb as Xe,cc as Ys,cd as Vs,ce as zt,cf as Mr,cg as Cn,ch as Qs,ci as fn,cj as Zs,ck as eo,cl as to,cm as Y,cn as Fr,co as jr,cp as Pe,cq as no,cr as Ur,cs as Le,ct as ro,cu as Te,cv as On,cw as so,cx as oo,cy as ao,cz as io,cA as An,cB as co,cC as De,cD as uo,a0 as lo,v as po,u as Hr,q as fo,cE as mn,aS as mo,cF as ho,aa as Wt,m as Gt,cG as go,z as mt,ar as vo,C as oe,aR as Kt,cH as yo,cI as Br,cJ as _o,cK as bo,Q as zr,cL as Eo,cM as wo,cN as So,cO as To,cP as xo,s as ko,cQ as Dn,ag as Ie,cR as Wr,x as Po,y as $o,D as Ro,F as Co,G as Gr,a as Oo,b as Xt,L as Ao,o as Do,H as Io,A as Lo,B as No,M as qo,I as Mo,J as Fo,O as jo,Y as Uo}from"./legacy-YP6Kq8lu.js";const at={},In={};function ue(t,e){at[t]=at[t]||[],at[t].push(e)}function le(t,e){if(!In[t]){In[t]=!0;try{e()}catch(n){I&&k.error(`Error while instrumenting ${t}`,n)}}}function V(t,e){const n=t&&at[t];if(n)for(const r of n)try{r(e)}catch(s){I&&k.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${ce(r)}
Error:`,s)}}let xt=null;function Kr(t){const e="error";ue(e,t),le(e,Ho)}function Ho(){xt=j.onerror,j.onerror=function(t,e,n,r,s){return V("error",{column:r,error:s,line:n,msg:t,url:e}),!!xt&&xt.apply(this,arguments)},j.onerror.__SENTRY_INSTRUMENTED__=!0}let kt=null;function Xr(t){const e="unhandledrejection";ue(e,t),le(e,Bo)}function Bo(){kt=j.onunhandledrejection,j.onunhandledrejection=function(t){return V("unhandledrejection",t),!kt||kt.apply(this,arguments)},j.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let Ln=!1;function Jt(){const t=X(),e=t&&ve(t);if(e){const n="internal_error";I&&k.log(`[Tracing] Root span: ${n} -> Global error occurred`),e.setStatus({code:He,message:n})}}Jt.tag="sentry_tracingErrorCallback";const it={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},zo="heartbeatFailed",Wo="idleTimeout",Go="finalTimeout",Ko="externalFinish";function Nn(t,e={}){const n=new Map;let r,s=!1,o=Ko,a=!e.disableAutoFinish;const i=[],{idleTimeout:c=it.idleTimeout,finalTimeout:u=it.finalTimeout,childSpanTimeout:d=it.childSpanTimeout,beforeSpanEnd:f}=e,l=q();if(!l||!Be()){const v=new lt,y={sample_rate:"0",sampled:"false",...yt(v)};return xs(v,y),v}const p=F(),m=X(),h=function(v){const y=ye(v);return En(F(),y),I&&k.log("[Tracing] Started span is an idle span"),y}(t);function b(){r&&(clearTimeout(r),r=void 0)}function E(v){b(),r=setTimeout(()=>{!s&&n.size===0&&a&&(o=Wo,h.end(v))},c)}function g(v){r=setTimeout(()=>{!s&&a&&(o=zo,h.end(v))},d)}function x(v){s=!0,n.clear(),i.forEach(_=>_()),En(p,m);const y=H(h),{start_timestamp:T}=y;if(!T)return;y.data[dt]||h.setAttribute(dt,o),k.log(`[Tracing] Idle span "${y.op}" finished`);const w=Tt(h).filter(_=>_!==h);let N=0;w.forEach(_=>{_.isRecording()&&(_.setStatus({code:He,message:"cancelled"}),_.end(v),I&&k.log("[Tracing] Cancelling span since span ended early",JSON.stringify(_,void 0,2)));const O=H(_),{timestamp:D=0,start_timestamp:S=0}=O,P=S<=v,R=D-S<=(u+c)/1e3;if(I){const $=JSON.stringify(_,void 0,2);P?R||k.log("[Tracing] Discarding span since it finished after idle span final timeout",$):k.log("[Tracing] Discarding span since it happened after idle span was finished",$)}R&&P||(Ps(h,_),N++)}),N>0&&h.setAttribute("sentry.idle_span_discarded_spans",N)}return h.end=new Proxy(h.end,{apply(v,y,T){if(f&&f(h),y instanceof lt)return;const[w,...N]=T,_=w||te(),O=ks(_),D=Tt(h).filter(A=>A!==h);if(!D.length)return x(O),Reflect.apply(v,y,[O,...N]);const S=D.map(A=>H(A).timestamp).filter(A=>!!A),P=S.length?Math.max(...S):void 0,R=H(h).start_timestamp,$=Math.min(R?R+u/1e3:1/0,Math.max(R||-1/0,Math.min(O,P||1/0)));return x($),Reflect.apply(v,y,[$,...N])}}),i.push(l.on("spanStart",v=>{if(!(s||v===h||H(v).timestamp)){var y;Tt(h).includes(v)&&(y=v.spanContext().spanId,b(),n.set(y,!0),g(te()+d/1e3))}})),i.push(l.on("spanEnd",v=>{var y;s||(y=v.spanContext().spanId,n.has(y)&&n.delete(y),n.size===0&&E(te()+c/1e3))})),i.push(l.on("idleSpanEnableAutoFinish",v=>{v===h&&(a=!0,E(),n.size&&g())})),e.disableAutoFinish||E(),setTimeout(()=>{s||(h.setStatus({code:He,message:"deadline_exceeded"}),o=Go,h.end())},u),h}var ne;function _e(t){return new de(e=>{e(t)})}function ht(t){return new de((e,n)=>{n(t)})}(function(t){t[t.PENDING=0]="PENDING",t[t.RESOLVED=1]="RESOLVED",t[t.REJECTED=2]="REJECTED"})(ne||(ne={}));class de{constructor(e){this._state=ne.PENDING,this._handlers=[],this._runExecutor(e)}then(e,n){return new de((r,s)=>{this._handlers.push([!1,o=>{if(e)try{r(e(o))}catch(a){s(a)}else r(o)},o=>{if(n)try{r(n(o))}catch(a){s(a)}else s(o)}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new de((n,r)=>{let s,o;return this.then(a=>{o=!1,s=a,e&&e()},a=>{o=!0,s=a,e&&e()}).then(()=>{o?r(s):n(s)})})}_executeHandlers(){if(this._state===ne.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===ne.RESOLVED&&n[1](this._value),this._state===ne.REJECTED&&n[2](this._value),n[0]=!0)})}_runExecutor(e){const n=(o,a)=>{this._state===ne.PENDING&&(cn(a)?a.then(r,s):(this._state=o,this._value=a,this._executeHandlers()))},r=o=>{n(ne.RESOLVED,o)},s=o=>{n(ne.REJECTED,o)};try{e(r,s)}catch(o){s(o)}}}function Yt(t,e,n,r=0){return new de((s,o)=>{const a=t[r];if(e===null||typeof a!="function")s(e);else{const i=a({...e},n);I&&a.id&&i===null&&k.log(`Event processor "${a.id}" dropped event`),cn(i)?i.then(c=>Yt(t,c,n,r+1).then(s)).then(null,o):Yt(t,i,n,r+1).then(s).then(null,o)}})}let Ve,qn,Pt;function Xo(t,e){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:o}=e;(function(a,i){const{extra:c,tags:u,user:d,contexts:f,level:l,transactionName:p}=i;Object.keys(c).length&&(a.extra={...c,...a.extra}),Object.keys(u).length&&(a.tags={...u,...a.tags}),Object.keys(d).length&&(a.user={...d,...a.user}),Object.keys(f).length&&(a.contexts={...f,...a.contexts}),l&&(a.level=l),p&&a.type!=="transaction"&&(a.transaction=p)})(t,e),r&&function(a,i){a.contexts={trace:$s(i),...a.contexts},a.sdkProcessingMetadata={dynamicSamplingContext:yt(i),...a.sdkProcessingMetadata};const c=ve(i),u=H(c).description;u&&!a.transaction&&a.type==="transaction"&&(a.transaction=u)}(t,r),function(a,i){a.fingerprint=a.fingerprint?Array.isArray(a.fingerprint)?a.fingerprint:[a.fingerprint]:[],i&&(a.fingerprint=a.fingerprint.concat(i)),a.fingerprint.length||delete a.fingerprint}(t,n),function(a,i){const c=[...a.breadcrumbs||[],...i];a.breadcrumbs=c.length?c:void 0}(t,s),function(a,i){a.sdkProcessingMetadata={...a.sdkProcessingMetadata,...i}}(t,o)}function Mn(t,e){const{extra:n,tags:r,user:s,contexts:o,level:a,sdkProcessingMetadata:i,breadcrumbs:c,fingerprint:u,eventProcessors:d,attachments:f,propagationContext:l,transactionName:p,span:m}=e;Qe(t,"extra",n),Qe(t,"tags",r),Qe(t,"user",s),Qe(t,"contexts",o),t.sdkProcessingMetadata=un(t.sdkProcessingMetadata,i,2),a&&(t.level=a),p&&(t.transactionName=p),m&&(t.span=m),c.length&&(t.breadcrumbs=[...t.breadcrumbs,...c]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),d.length&&(t.eventProcessors=[...t.eventProcessors,...d]),f.length&&(t.attachments=[...t.attachments,...f]),t.propagationContext={...t.propagationContext,...l}}function Qe(t,e,n){t[e]=un(t[e],n,1)}function Jo(t,e,n,r,s,o){const{normalizeDepth:a=3,normalizeMaxBreadth:i=1e3}=t,c={...e,event_id:e.event_id||n.event_id||Me(),timestamp:e.timestamp||ln()},u=n.integrations||t.integrations.map(m=>m.name);(function(m,h){const{environment:b,release:E,dist:g,maxValueLength:x=250}=h;m.environment=m.environment||b||Or,!m.release&&E&&(m.release=E),!m.dist&&g&&(m.dist=g);const v=m.request;v!=null&&v.url&&(v.url=Cs(v.url,x))})(c,t),function(m,h){h.length>0&&(m.sdk=m.sdk||{},m.sdk.integrations=[...m.sdk.integrations||[],...h])}(c,u),s&&s.emit("applyFrameMetadata",e),e.type===void 0&&function(m,h){var E,g;const b=function(x){const v=j._sentryDebugIds;if(!v)return{};const y=Object.keys(v);return Pt&&y.length===qn||(qn=y.length,Pt=y.reduce((T,w)=>{Ve||(Ve={});const N=Ve[w];if(N)T[N[0]]=N[1];else{const _=x(w);for(let O=_.length-1;O>=0;O--){const D=_[O],S=D==null?void 0:D.filename,P=v[w];if(S&&P){T[S]=P,Ve[w]=[S,P];break}}}return T},{})),Pt}(h);(g=(E=m.exception)==null?void 0:E.values)==null||g.forEach(x=>{var v,y;(y=(v=x.stacktrace)==null?void 0:v.frames)==null||y.forEach(T=>{T.filename&&(T.debug_id=b[T.filename])})})}(c,t.stackParser);const d=function(m,h){if(!h)return m;const b=m?m.clone():new Os;return b.update(h),b}(r,n.captureContext);n.mechanism&&ke(c,n.mechanism);const f=s?s.getEventProcessors():[],l=Rs().getScopeData();o&&Mn(l,o.getScopeData()),d&&Mn(l,d.getScopeData());const p=[...n.attachments||[],...l.attachments];return p.length&&(n.attachments=p),Xo(c,l),Yt([...f,...l.eventProcessors],c,n).then(m=>(m&&function(h){var g,x;const b={};if((x=(g=h.exception)==null?void 0:g.values)==null||x.forEach(v=>{var y,T;(T=(y=v.stacktrace)==null?void 0:y.frames)==null||T.forEach(w=>{w.debug_id&&(w.abs_path?b[w.abs_path]=w.debug_id:w.filename&&(b[w.filename]=w.debug_id),delete w.debug_id)})}),Object.keys(b).length===0)return;h.debug_meta=h.debug_meta||{},h.debug_meta.images=h.debug_meta.images||[];const E=h.debug_meta.images;Object.entries(b).forEach(([v,y])=>{E.push({type:"sourcemap",code_file:v,debug_id:y})})}(m),typeof a=="number"&&a>0?function(h,b,E){var x,v;if(!h)return null;const g={...h,...h.breadcrumbs&&{breadcrumbs:h.breadcrumbs.map(y=>({...y,...y.data&&{data:fe(y.data,b,E)}}))},...h.user&&{user:fe(h.user,b,E)},...h.contexts&&{contexts:fe(h.contexts,b,E)},...h.extra&&{extra:fe(h.extra,b,E)}};return(x=h.contexts)!=null&&x.trace&&g.contexts&&(g.contexts.trace=h.contexts.trace,h.contexts.trace.data&&(g.contexts.trace.data=fe(h.contexts.trace.data,b,E))),h.spans&&(g.spans=h.spans.map(y=>({...y,...y.data&&{data:fe(y.data,b,E)}}))),(v=h.contexts)!=null&&v.flags&&g.contexts&&(g.contexts.flags=fe(h.contexts.flags,3,E)),g}(m,a,i):m))}function Fn(t,e){return F().captureEvent(t,e)}function jn(t){const e=Ee(),n=F(),{userAgent:r}=j.navigator||{},s=As({user:n.getUser()||e.getUser(),...r&&{userAgent:r},...t}),o=e.getSession();return(o==null?void 0:o.status)==="ok"&&Ht(o,{status:"exited"}),Jr(),e.setSession(s),s}function Jr(){const t=Ee(),e=F().getSession()||t.getSession();e&&Ds(e),Yr(),t.setSession()}function Yr(){const t=Ee(),e=q(),n=t.getSession();n&&e&&e.captureSession(n)}function Un(t=!1){t?Jr():Yr()}const Yo="7";function Vo(t,e,n){return e||`${function(r){return`${function(s){const o=s.protocol?`${s.protocol}:`:"",a=s.port?`:${s.port}`:"";return`${o}//${s.host}${a}${s.path?`/${s.path}`:""}/api/`}(r)}${r.projectId}/envelope/`}(t)}?${function(r,s){const o={sentry_version:Yo};return r.publicKey&&(o.sentry_key=r.publicKey),s&&(o.sentry_client=`${s.name}/${s.version}`),new URLSearchParams(o).toString()}(t,n)}`}const Hn=[];function Qo(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;if(e.forEach(s=>{s.isDefaultInstance=!0}),Array.isArray(n))r=[...e,...n];else if(typeof n=="function"){const s=n(e);r=Array.isArray(s)?s:[s]}else r=e;return function(s){const o={};return s.forEach(a=>{const{name:i}=a,c=o[i];c&&!c.isDefaultInstance&&a.isDefaultInstance||(o[i]=a)}),Object.values(o)}(r)}function Bn(t,e){for(const n of e)n!=null&&n.afterAllSetup&&n.afterAllSetup(t)}function zn(t,e,n){if(n[e.name])I&&k.log(`Integration skipped because it was already installed: ${e.name}`);else{if(n[e.name]=e,Hn.indexOf(e.name)===-1&&typeof e.setupOnce=="function"&&(e.setupOnce(),Hn.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(t),typeof e.preprocessEvent=="function"){const r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(s,o)=>r(s,o,t))}if(typeof e.processEvent=="function"){const r=e.processEvent.bind(e),s=Object.assign((o,a)=>r(o,a,t),{id:e.name});t.addEventProcessor(s)}I&&k.log(`Integration installed: ${e.name}`)}}function Vr(t){const e=[];t.message&&e.push(t.message);try{const n=t.exception.values[t.exception.values.length-1];n!=null&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`))}catch{}return e}const Wn="Not capturing exception because it's already been captured.",Gn="Discarded session because of missing or non-string release",Qr=Symbol.for("SentryInternalError"),Zr=Symbol.for("SentryDoNotSendEventError");function Ze(t){return{message:t,[Qr]:!0}}function $t(t){return{message:t,[Zr]:!0}}function Kn(t){return!!t&&typeof t=="object"&&Qr in t}function Xn(t){return!!t&&typeof t=="object"&&Zr in t}class Zo{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=Is(e.dsn):I&&k.warn("No DSN provided, client will not send events."),this._dsn){const n=Vo(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){const s=Me();if(Sn(e))return I&&k.log(Wn),s;const o={event_id:s,...n};return this._process(this.eventFromException(e,o).then(a=>this._captureEvent(a,o,r))),o.event_id}captureMessage(e,n,r,s){const o={event_id:Me(),...r},a=Ir(e)?e:String(e),i=ze(e)?this.eventFromMessage(a,n,o):this.eventFromException(e,o);return this._process(i.then(c=>this._captureEvent(c,o,s))),o.event_id}captureEvent(e,n,r){const s=Me();if(n!=null&&n.originalException&&Sn(n.originalException))return I&&k.log(Wn),s;const o={event_id:s,...n},a=e.sdkProcessingMetadata||{},i=a.capturedSpanScope,c=a.capturedSpanIsolationScope;return this._process(this._captureEvent(e,o,i||r,c)),o.event_id}captureSession(e){this.sendSession(e),Ht(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>n.flush(e).then(s=>r&&s))):_e(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const n=this._integrations[e.name];zn(this,e,this._integrations),n||Bn(this,[e])}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=Ls(e,this._dsn,this._options._metadata,this._options.tunnel);for(const o of n.attachments||[])r=Ns(r,qs(o));const s=this.sendEnvelope(r);s&&s.then(o=>this.emit("afterSendEvent",e,o),null)}sendSession(e){const{release:n,environment:r=Or}=this._options;if("aggregates"in e){const o=e.attrs||{};if(!o.release&&!n)return void(I&&k.warn(Gn));o.release=o.release||n,o.environment=o.environment||r,e.attrs=o}else{if(!e.release&&!n)return void(I&&k.warn(Gn));e.release=e.release||n,e.environment=e.environment||r}this.emit("beforeSendSession",e);const s=Ms(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(s)}recordDroppedEvent(e,n,r=1){if(this._options.sendClientReports){const s=`${e}:${n}`;I&&k.log(`Recording outcome: "${s}"${r>1?` (${r} times)`:""}`),this._outcomes[s]=(this._outcomes[s]||0)+r}}on(e,n){const r=this._hooks[e]=this._hooks[e]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(e,...n){const r=this._hooks[e];r&&r.forEach(s=>s(...n))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,n=>(I&&k.error("Error while sending envelope:",n),n)):(I&&k.error("Transport disabled"),_e({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=function(n,r){const s={};return r.forEach(o=>{o&&zn(n,o,s)}),s}(this,e),Bn(this,e)}_updateSessionFromEvent(e,n){var i;let r=n.level==="fatal",s=!1;const o=(i=n.exception)==null?void 0:i.values;if(o){s=!0;for(const c of o){const u=c.mechanism;if((u==null?void 0:u.handled)===!1){r=!0;break}}}const a=e.status==="ok";(a&&e.errors===0||a&&r)&&(Ht(e,{...r&&{status:"crashed"},errors:e.errors||Number(s||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new de(n=>{let r=0;const s=setInterval(()=>{this._numProcessing==0?(clearInterval(s),n(!0)):(r+=1,e&&r>=e&&(clearInterval(s),n(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r,s){const o=this.getOptions(),a=Object.keys(this._integrations);return!n.integrations&&(a!=null&&a.length)&&(n.integrations=a),this.emit("preprocessEvent",e,n),e.type||s.setLastEventId(e.event_id||n.event_id),Jo(o,e,n,r,this,s).then(i=>{if(i===null)return i;this.emit("postprocessEvent",i,n),i.contexts={trace:Fs(r),...i.contexts};const c=Ar(this,r);return i.sdkProcessingMetadata={dynamicSamplingContext:c,...i.sdkProcessingMetadata},i})}_captureEvent(e,n={},r=F(),s=Ee()){return I&&Rt(e)&&k.log(`Captured error event \`${Vr(e)[0]||"<unknown>"}\``),this._processEvent(e,n,r,s).then(o=>o.event_id,o=>{I&&(Xn(o)?k.log(o.message):Kn(o)?k.warn(o.message):k.warn(o))})}_processEvent(e,n,r,s){const o=this.getOptions(),{sampleRate:a}=o,i=Jn(e),c=Rt(e),u=e.type||"error",d=`before send for type \`${u}\``,f=a===void 0?void 0:js(a);if(c&&typeof f=="number"&&Math.random()>f)return this.recordDroppedEvent("sample_rate","error"),ht($t(`Discarding event because it's not included in the random sample (sampling rate = ${a})`));const l=u==="replay_event"?"replay":u;return this._prepareEvent(e,n,r,s).then(p=>{if(p===null)throw this.recordDroppedEvent("event_processor",l),$t("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return p;const m=function(h,b,E,g){const{beforeSend:x,beforeSendTransaction:v,beforeSendSpan:y}=b;let T=E;if(Rt(T)&&x)return x(T,g);if(Jn(T)){if(y){const N=y(function(_){var U;const{trace_id:O,parent_span_id:D,span_id:S,status:P,origin:R,data:$,op:A}=((U=_.contexts)==null?void 0:U.trace)??{};return{data:$??{},description:_.transaction,op:A,parent_span_id:D,span_id:S??"",start_timestamp:_.start_timestamp??0,status:P,timestamp:_.timestamp,trace_id:O??"",origin:R,profile_id:$==null?void 0:$[wn],exclusive_time:$==null?void 0:$[pt],measurements:_.measurements,is_segment:!0}}(T));if(N?T=un(E,{type:"transaction",timestamp:(w=N).timestamp,start_timestamp:w.start_timestamp,transaction:w.description,contexts:{trace:{trace_id:w.trace_id,span_id:w.span_id,parent_span_id:w.parent_span_id,op:w.op,status:w.status,origin:w.origin,data:{...w.data,...w.profile_id&&{[wn]:w.profile_id},...w.exclusive_time&&{[pt]:w.exclusive_time}}}},measurements:w.measurements}):Tn(),T.spans){const _=[];for(const O of T.spans){const D=y(O);D?_.push(D):(Tn(),_.push(O))}T.spans=_}}if(v){if(T.spans){const N=T.spans.length;T.sdkProcessingMetadata={...E.sdkProcessingMetadata,spanCountBeforeProcessing:N}}return v(T,g)}}var w;return T}(0,o,p,n);return function(h,b){const E=`${b} must return \`null\` or a valid event.`;if(cn(h))return h.then(g=>{if(!Bt(g)&&g!==null)throw Ze(E);return g},g=>{throw Ze(`${b} rejected with ${g}`)});if(!Bt(h)&&h!==null)throw Ze(E);return h}(m,d)}).then(p=>{var b;if(p===null){if(this.recordDroppedEvent("before_send",l),i){const E=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",E)}throw $t(`${d} returned \`null\`, will not send event.`)}const m=r.getSession()||s.getSession();if(c&&m&&this._updateSessionFromEvent(m,p),i){const E=(((b=p.sdkProcessingMetadata)==null?void 0:b.spanCountBeforeProcessing)||0)-(p.spans?p.spans.length:0);E>0&&this.recordDroppedEvent("before_send","span",E)}const h=p.transaction_info;if(i&&h&&p.transaction!==e.transaction){const E="custom";p.transaction_info={...h,source:E}}return this.sendEvent(p,n),p}).then(null,p=>{throw Xn(p)||Kn(p)?p:(this.captureException(p,{data:{__sentry__:!0},originalException:p}),Ze(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${p}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([n,r])=>{const[s,o]=n.split(":");return{reason:s,category:o,quantity:r}})}_flushOutcomes(){I&&k.log("Flushing outcomes...");const e=this._clearOutcomes();if(e.length===0)return void(I&&k.log("No outcomes to send"));if(!this._dsn)return void(I&&k.log("No dsn provided, will not send outcomes"));I&&k.log("Sending outcomes:",e);const n=function(r,s,o){const a=[{type:"client_report"},{timestamp:ln(),discarded_events:r}];return dn(s?{dsn:s}:{},[a])}(e,this._options.tunnel&&Dr(this._dsn));this.sendEnvelope(n)}}function Rt(t){return t.type===void 0}function Jn(t){return t.type==="transaction"}function Ct(t,e){var o;const n=function(a){var i;return(i=j._sentryClientToLogBufferMap)==null?void 0:i.get(a)}(t)??[];if(n.length===0)return;const r=t.getOptions(),s=function(a,i,c,u){const d={};return i!=null&&i.sdk&&(d.sdk={name:i.sdk.name,version:i.sdk.version}),c&&u&&(d.dsn=Dr(u)),dn(d,[(f=a,[{type:"log",item_count:f.length,content_type:"application/vnd.sentry.items.log+json"},{items:f}])]);var f}(n,r._metadata,r.tunnel,t.getDsn());(o=j._sentryClientToLogBufferMap)==null||o.set(t,[]),t.emit("flushLogs"),t.sendEnvelope(s)}function ea(t,e){e.debug===!0&&(I?k.enable():_t(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),F().update(e.initialScope);const n=new t(e);return function(r){F().setClient(r)}(n),n.init(),n}j._sentryClientToLogBufferMap=new WeakMap;const es=Symbol.for("SentryBufferFullError");function ta(t){const e=[];function n(r){return e.splice(e.indexOf(r),1)[0]||Promise.resolve(void 0)}return{$:e,add:function(r){if(!(t===void 0||e.length<t))return ht(es);const s=r();return e.indexOf(s)===-1&&e.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s},drain:function(r){return new de((s,o)=>{let a=e.length;if(!a)return s(!0);const i=setTimeout(()=>{r&&r>0&&s(!1)},r);e.forEach(c=>{_e(c).then(()=>{--a||(clearTimeout(i),s(!0))},o)})})}}}const na=6e4;function ra(t,{statusCode:e,headers:n},r=Date.now()){const s={...t},o=n==null?void 0:n["x-sentry-rate-limits"],a=n==null?void 0:n["retry-after"];if(o)for(const i of o.trim().split(",")){const[c,u,,,d]=i.split(":",5),f=parseInt(c,10),l=1e3*(isNaN(f)?60:f);if(u)for(const p of u.split(";"))p==="metric_bucket"&&d&&!d.split(";").includes("custom")||(s[p]=r+l);else s.all=r+l}else a?s.all=r+function(i,c=Date.now()){const u=parseInt(`${i}`,10);if(!isNaN(u))return 1e3*u;const d=Date.parse(`${i}`);return isNaN(d)?na:d-c}(a,r):e===429&&(s.all=r+6e4);return s}const sa=64;function oa(t,e,n=ta(t.bufferSize||sa)){let r={};return{send:function(s){const o=[];if(xn(s,(c,u)=>{const d=kn(u);(function(f,l,p=Date.now()){return function(m,h){return m[h]||m.all||0}(f,l)>p})(r,d)?t.recordDroppedEvent("ratelimit_backoff",d):o.push(c)}),o.length===0)return _e({});const a=dn(s[0],o),i=c=>{xn(a,(u,d)=>{t.recordDroppedEvent(c,kn(d))})};return n.add(()=>e({body:Us(a)}).then(c=>(c.statusCode!==void 0&&(c.statusCode<200||c.statusCode>=300)&&I&&k.warn(`Sentry responded with status code ${c.statusCode} to sent event.`),r=ra(r,c),c),c=>{throw i("network_error"),I&&k.error("Encountered error running transport request:",c),c})).then(c=>c,c=>{if(c===es)return I&&k.error("Skipped sending event because buffer is full."),i("queue_overflow"),_e({});throw c})},flush:s=>n.drain(s)}}function aa(t){var e;((e=t.user)==null?void 0:e.ip_address)===void 0&&(t.user={...t.user,ip_address:"{{auto}}"})}function ia(t){var e;"aggregates"in t?((e=t.attrs)==null?void 0:e.ip_address)===void 0&&(t.attrs={...t.attrs,ip_address:"{{auto}}"}):t.ipAddress===void 0&&(t.ipAddress="{{auto}}")}function ts(t,e,n=[e],r="npm"){const s=t._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${e}`,packages:n.map(o=>({name:`${r}:@sentry/${o}`,version:Pn})),version:Pn}),t._metadata=s}function ns(t={}){const e=q();if(!function(){const u=q();return(u==null?void 0:u.getOptions().enabled)!==!1&&!!(u!=null&&u.getTransport())}()||!e)return{};const n=Hs(),r=Bs(n);if(r.getTraceData)return r.getTraceData(t);const s=F(),o=t.span||X(),a=o?zs(o):function(u){const{traceId:d,sampled:f,propagationSpanId:l}=u.getPropagationContext();return Ks(d,l,f)}(s),i=o?yt(o):Ar(e,s),c=Ws(i);return Gs.test(a)?{"sentry-trace":a,baggage:c}:(k.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}const ca=100;function me(t,e){const n=q(),r=Ee();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:o=ca}=n.getOptions();if(o<=0)return;const a={timestamp:ln(),...t},i=s?_t(()=>s(a,e)):a;i!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",i,e),r.addBreadcrumb(i,o))}let Yn;const Vn=new WeakMap,ua=()=>({name:"FunctionToString",setupOnce(){Yn=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=pn(this),n=Vn.has(q())&&e!==void 0?e:this;return Yn.apply(n,t)}}catch{}},setup(t){Vn.set(t,!0)}}),la=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],da=(t={})=>{let e;return{name:"EventFilters",setup(n){const r=n.getOptions();e=Qn(t,r)},processEvent(n,r,s){if(!e){const o=s.getOptions();e=Qn(t,o)}return function(o,a){if(o.type){if(o.type==="transaction"&&function(i,c){if(!(c!=null&&c.length))return!1;const u=i.transaction;return!!u&&ge(u,c)}(o,a.ignoreTransactions))return I&&k.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${he(o)}`),!0}else{if(function(i,c){return c!=null&&c.length?Vr(i).some(u=>ge(u,c)):!1}(o,a.ignoreErrors))return I&&k.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${he(o)}`),!0;if(function(i){var c,u;return(u=(c=i.exception)==null?void 0:c.values)!=null&&u.length?!i.message&&!i.exception.values.some(d=>d.stacktrace||d.type&&d.type!=="Error"||d.value):!1}(o))return I&&k.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${he(o)}`),!0;if(function(i,c){if(!(c!=null&&c.length))return!1;const u=et(i);return!!u&&ge(u,c)}(o,a.denyUrls))return I&&k.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${he(o)}.
Url: ${et(o)}`),!0;if(!function(i,c){if(!(c!=null&&c.length))return!0;const u=et(i);return!u||ge(u,c)}(o,a.allowUrls))return I&&k.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${he(o)}.
Url: ${et(o)}`),!0}return!1}(n,e)?null:n}}},pa=(t={})=>({...da(t),name:"InboundFilters"});function Qn(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:la],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]]}}function et(t){var e,n;try{const r=[...((e=t.exception)==null?void 0:e.values)??[]].reverse().find(o=>{var a,i,c;return((a=o.mechanism)==null?void 0:a.parent_id)===void 0&&((c=(i=o.stacktrace)==null?void 0:i.frames)==null?void 0:c.length)}),s=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return s?function(o=[]){for(let a=o.length-1;a>=0;a--){const i=o[a];if(i&&i.filename!=="<anonymous>"&&i.filename!=="[native code]")return i.filename||null}return null}(s):null}catch{return I&&k.error(`Cannot extract url for event ${he(t)}`),null}}function fa(t,e,n,r,s,o){var i;if(!((i=s.exception)!=null&&i.values)||!o||!ft(o.originalException,Error))return;const a=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;a&&(s.exception.values=Vt(t,e,r,o.originalException,n,s.exception.values,a,0))}function Vt(t,e,n,r,s,o,a,i){if(o.length>=n+1)return o;let c=[...o];if(ft(r[s],Error)){Zn(a,i);const u=t(e,r[s]),d=c.length;er(u,s,d,i),c=Vt(t,e,n,r[s],s,[u,...c],u,d)}return Array.isArray(r.errors)&&r.errors.forEach((u,d)=>{if(ft(u,Error)){Zn(a,i);const f=t(e,u),l=c.length;er(f,`errors[${d}]`,l,i),c=Vt(t,e,n,u,s,[f,...c],f,l)}}),c}function Zn(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,...t.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function er(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function ma(){"console"in j&&Xs.forEach(function(t){t in j.console&&K(j.console,t,function(e){return $n[t]=e,function(...n){V("console",{args:n,level:t});const r=$n[t];r==null||r.apply(j.console,n)}})})}function ha(t){return t==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log"}const ga=()=>{let t;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{if(function(n,r){return r?!!(function(s,o){const a=s.message,i=o.message;return!(!a&&!i||a&&!i||!a&&i||a!==i||!nr(s,o)||!tr(s,o))}(n,r)||function(s,o){const a=rr(o),i=rr(s);return!(!a||!i||a.type!==i.type||a.value!==i.value||!nr(s,o)||!tr(s,o))}(n,r)):!1}(e,t))return I&&k.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return t=e}}};function tr(t,e){let n=Rn(t),r=Rn(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const o=r[s],a=n[s];if(o.filename!==a.filename||o.lineno!==a.lineno||o.colno!==a.colno||o.function!==a.function)return!1}return!0}function nr(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function rr(t){var e;return((e=t.exception)==null?void 0:e.values)&&t.exception.values[0]}const va="thismessage:/";function rs(t){return"isRelative"in t}function ss(t,e){const n=t.indexOf("://")<=0&&t.indexOf("//")!==0,r=n?va:void 0;try{if("canParse"in URL&&!URL.canParse(t,r))return;const s=new URL(t,r);return n?{isRelative:n,pathname:s.pathname,search:s.search,hash:s.hash}:s}catch{}}function ya(t){if(rs(t))return t.pathname;const e=new URL(t);return e.search="",e.hash="",["80","443"].includes(e.port)&&(e.port=""),e.password&&(e.password="%filtered%"),e.username&&(e.username="%filtered%"),e.toString()}function xe(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}function _a(t,e,n,r,s="auto.http.browser"){if(!t.fetchData)return;const{method:o,url:a}=t.fetchData,i=Be()&&e(a);if(t.endTimestamp&&i){const f=t.fetchData.__span;if(!f)return;const l=r[f];return void(l&&(function(p,m){var h;if(m.response){Lr(p,m.response.status);const b=((h=m.response)==null?void 0:h.headers)&&m.response.headers.get("content-length");if(b){const E=parseInt(b);E>0&&p.setAttribute("http.response_content_length",E)}}else m.error&&p.setStatus({code:He,message:"internal_error"});p.end()}(l,t),delete r[f]))}const c=!!X(),u=i&&c?ye(function(f,l,p){const m=ss(f);return{name:m?`${l} ${ya(m)}`:l,attributes:ba(f,m,l,p)}}(a,o,s)):new lt;if(t.fetchData.__span=u.spanContext().spanId,r[u.spanContext().spanId]=u,n(t.fetchData.url)){const f=t.args[0],l=t.args[1]||{},p=function(m,h,b){const E=ns({span:b}),g=E["sentry-trace"],x=E.baggage;if(!g)return;const v=h.headers||(Nr(m)?m.headers:void 0);if(v){if(function(y){return typeof Headers<"u"&&ft(y,Headers)}(v)){const y=new Headers(v);if(y.get("sentry-trace")||y.set("sentry-trace",g),x){const T=y.get("baggage");T?tt(T)||y.set("baggage",`${T},${x}`):y.set("baggage",x)}return y}if(Array.isArray(v)){const y=[...v];v.find(w=>w[0]==="sentry-trace")||y.push(["sentry-trace",g]);const T=v.find(w=>w[0]==="baggage"&&tt(w[1]));return x&&!T&&y.push(["baggage",x]),y}{const y="sentry-trace"in v?v["sentry-trace"]:void 0,T="baggage"in v?v.baggage:void 0,w=T?Array.isArray(T)?[...T]:[T]:[],N=T&&(Array.isArray(T)?T.find(_=>tt(_)):tt(T));return x&&!N&&w.push(x),{...v,"sentry-trace":y??g,baggage:w.length>0?w.join(","):void 0}}}return{...E}}(f,l,Be()&&c?u:void 0);p&&(t.args[1]=l,l.headers=p)}const d=q();if(d){const f={input:t.args,response:t.response,startTimestamp:t.startTimestamp,endTimestamp:t.endTimestamp};d.emit("beforeOutgoingRequestSpan",u,f)}return u}function tt(t){return t.split(",").some(e=>e.trim().startsWith(Js))}function ba(t,e,n,r){const s={url:t,type:"fetch","http.method":n,[W]:r,[bt]:"http.client"};return e&&(rs(e)||(s["http.url"]=e.href,s["server.address"]=e.host),e.search&&(s["http.query"]=e.search),e.hash&&(s["http.fragment"]=e.hash)),s}function sr(t){return t===void 0?void 0:t>=400&&t<500?"warning":t>=500?"error":void 0}const Ge=j;function os(){if(!("fetch"in Ge))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Qt(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function as(t,e){const n="fetch";ue(n,t),le(n,()=>is(void 0,e))}function is(t,e=!1){e&&!function(){var s;if(typeof EdgeRuntime=="string")return!0;if(!os())return!1;if(Qt(Ge.fetch))return!0;let n=!1;const r=Ge.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o),(s=o.contentWindow)!=null&&s.fetch&&(n=Qt(o.contentWindow.fetch)),r.head.removeChild(o)}catch(o){I&&k.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",o)}return n}()||K(j,"fetch",function(n){return function(...r){const s=new Error,{method:o,url:a}=function(c){if(c.length===0)return{method:"GET",url:""};if(c.length===2){const[d,f]=c;return{url:or(d),method:Zt(f,"method")?String(f.method).toUpperCase():"GET"}}const u=c[0];return{url:or(u),method:Zt(u,"method")?String(u.method).toUpperCase():"GET"}}(r),i={args:r,fetchData:{method:o,url:a},startTimestamp:1e3*te(),virtualError:s,headers:wa(r)};return t||V("fetch",{...i}),n.apply(j,r).then(async c=>(t?t(c):V("fetch",{...i,endTimestamp:1e3*te(),response:c}),c),c=>{if(V("fetch",{...i,endTimestamp:1e3*te(),error:c}),qr(c)&&c.stack===void 0&&(c.stack=s.stack,We(c,"framesToPop",1)),c instanceof TypeError&&(c.message==="Failed to fetch"||c.message==="Load failed"||c.message==="NetworkError when attempting to fetch resource."))try{const u=new URL(i.fetchData.url);c.message=`${c.message} (${u.host})`}catch{}throw c})}})}function Ea(t){let e;try{e=t.clone()}catch{return}(async function(n,r){if(n!=null&&n.body){const s=n.body,o=s.getReader(),a=setTimeout(()=>{s.cancel().then(null,()=>{})},9e4);let i=!0;for(;i;){let c;try{c=setTimeout(()=>{s.cancel().then(null,()=>{})},5e3);const{done:u}=await o.read();clearTimeout(c),u&&(r(),i=!1)}catch{i=!1}finally{clearTimeout(c)}}clearTimeout(a),o.releaseLock(),s.cancel().then(null,()=>{})}})(e,()=>{V("fetch-body-resolved",{endTimestamp:1e3*te(),response:t})})}function Zt(t,e){return!!t&&typeof t=="object"&&!!t[e]}function or(t){return typeof t=="string"?t:t?Zt(t,"url")?t.url:t.toString?t.toString():"":""}function wa(t){const[e,n]=t;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(Nr(e))return new Headers(e.headers)}catch{}}const L=j;let en=0;function ar(){return en>0}function $e(t,e={}){if(!function(r){return typeof r=="function"}(t))return t;try{const r=t.__sentry_wrapped__;if(r)return typeof r=="function"?r:t;if(pn(t))return t}catch{return t}const n=function(...r){try{const s=r.map(o=>$e(o,e));return t.apply(this,s)}catch(s){throw en++,setTimeout(()=>{en--}),Vs(o=>{var a;o.addEventProcessor(i=>(e.mechanism&&(zt(i,void 0),ke(i,e.mechanism)),i.extra={...i.extra,arguments:r},i)),a=s,F().captureException(a,void 0)}),s}};try{for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}catch{}Ys(n,t),We(t,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>t.name})}catch{}return n}function tn(){const t=Xe(),{referrer:e}=L.document||{},{userAgent:n}=L.navigator||{};return{url:t,headers:{...e&&{Referer:e},...n&&{"User-Agent":n}}}}function hn(t,e){const n=gn(t,e),r={type:xa(e),value:ka(e)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Sa(t,e,n,r){const s=q(),o=s==null?void 0:s.getOptions().normalizeDepth,a=function(u){for(const d in u)if(Object.prototype.hasOwnProperty.call(u,d)){const f=u[d];if(f instanceof Error)return f}}(e),i={__serialized__:Zs(e,o)};if(a)return{exception:{values:[hn(t,a)]},extra:i};const c={exception:{values:[{type:fn(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:Pa(e,{isUnhandledRejection:r})}]},extra:i};if(n){const u=gn(t,n);u.length&&(c.exception.values[0].stacktrace={frames:u})}return c}function Ot(t,e){return{exception:{values:[hn(t,e)]}}}function gn(t,e){const n=e.stacktrace||e.stack||"",r=function(o){return o&&Ta.test(o.message)?1:0}(e),s=function(o){return typeof o.framesToPop=="number"?o.framesToPop:0}(e);try{return t(n,r,s)}catch{}return[]}const Ta=/Minified React error #\d+;/i;function cs(t){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&t instanceof WebAssembly.Exception}function xa(t){const e=t==null?void 0:t.name;return!e&&cs(t)?t.message&&Array.isArray(t.message)&&t.message.length==2?t.message[0]:"WebAssembly.Exception":e}function ka(t){const e=t==null?void 0:t.message;return cs(t)?Array.isArray(t.message)&&t.message.length==2?t.message[1]:"wasm exception":e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function nn(t,e,n,r,s){let o;if(Mr(e)&&e.error)return Ot(t,e.error);if(Cn(e)||Qs(e)){const a=e;if("stack"in e)o=Ot(t,e);else{const i=a.name||(Cn(a)?"DOMError":"DOMException"),c=a.message?`${i}: ${a.message}`:i;o=rn(t,c,n,r),zt(o,c)}return"code"in a&&(o.tags={...o.tags,"DOMException.code":`${a.code}`}),o}return qr(e)?Ot(t,e):Bt(e)||fn(e)?(o=Sa(t,e,n,s),ke(o,{synthetic:!0}),o):(o=rn(t,e,n,r),zt(o,`${e}`),ke(o,{synthetic:!0}),o)}function rn(t,e,n,r){const s={};if(r&&n){const o=gn(t,n);o.length&&(s.exception={values:[{value:e,stacktrace:{frames:o}}]}),ke(s,{synthetic:!0})}if(Ir(e)){const{__sentry_template_string__:o,__sentry_template_values__:a}=e;return s.logentry={message:o,params:a},s}return s.message=e,s}function Pa(t,{isUnhandledRejection:e}){const n=eo(t),r=e?"promise rejection":"exception";return Mr(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:fn(t)?`Event \`${function(s){try{const o=Object.getPrototypeOf(s);return o?o.constructor.name:void 0}catch{}}(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}class $a extends Zo{constructor(e){const n={parentSpanIsAlwaysRootSpan:!0,...e};ts(n,"browser",["browser"],L.SENTRY_SDK_SOURCE||"npm"),super(n);const r=this,{sendDefaultPii:s,_experiments:o}=r._options,a=o==null?void 0:o.enableLogs;n.sendClientReports&&L.document&&L.document.addEventListener("visibilitychange",()=>{L.document.visibilityState==="hidden"&&(this._flushOutcomes(),a&&Ct(r))}),a&&(r.on("flush",()=>{Ct(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{Ct(r)},5e3)})),s&&(r.on("postprocessEvent",aa),r.on("beforeSendSession",ia))}eventFromException(e,n){return function(r,s,o,a){const i=nn(r,s,(o==null?void 0:o.syntheticException)||void 0,a);return ke(i),i.level="error",o!=null&&o.event_id&&(i.event_id=o.event_id),_e(i)}(this._options.stackParser,e,n,this._options.attachStacktrace)}eventFromMessage(e,n="info",r){return function(s,o,a="info",i,c){const u=rn(s,o,(i==null?void 0:i.syntheticException)||void 0,c);return u.level=a,i!=null&&i.event_id&&(u.event_id=i.event_id),_e(u)}(this._options.stackParser,e,n,r,this._options.attachStacktrace)}_prepareEvent(e,n,r,s){return e.platform=e.platform||"javascript",super._prepareEvent(e,n,r,s)}}const vn=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Re=(t,e,n,r)=>{let s,o;return a=>{e.value>=0&&(a||r)&&(o=e.value-(s||0),(o||s===void 0)&&(s=e.value,e.delta=o,e.rating=((i,c)=>i>c[1]?"poor":i>c[0]?"needs-improvement":"good")(e.value,n),t(e)))}},C=j,Ke=(t=!0)=>{var n,r;const e=(r=(n=C.performance)==null?void 0:n.getEntriesByType)==null?void 0:r.call(n,"navigation")[0];if(!t||e&&e.responseStart>0&&e.responseStart<performance.now())return e},Je=()=>{const t=Ke();return(t==null?void 0:t.activationStart)||0},Ce=(t,e)=>{var s,o;const n=Ke();let r="navigate";return n&&((s=C.document)!=null&&s.prerendering||Je()>0?r="prerender":(o=C.document)!=null&&o.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:t,value:e===void 0?-1:e,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},be=(t,e,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const r=new PerformanceObserver(s=>{Promise.resolve().then(()=>{e(s.getEntries())})});return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch{}},Oe=t=>{const e=n=>{var r;n.type!=="pagehide"&&((r=C.document)==null?void 0:r.visibilityState)!=="hidden"||t(n)};C.document&&(addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0))},Et=t=>{let e=!1;return()=>{e||(t(),e=!0)}};let Fe=-1;const gt=t=>{C.document.visibilityState==="hidden"&&Fe>-1&&(Fe=t.type==="visibilitychange"?t.timeStamp:0,Ra())},Ra=()=>{removeEventListener("visibilitychange",gt,!0),removeEventListener("prerenderingchange",gt,!0)},wt=()=>(C.document&&Fe<0&&(Fe=C.document.visibilityState!=="hidden"||C.document.prerendering?1/0:0,addEventListener("visibilitychange",gt,!0),addEventListener("prerenderingchange",gt,!0)),{get firstHiddenTime(){return Fe}}),Ye=t=>{var e;(e=C.document)!=null&&e.prerendering?addEventListener("prerenderingchange",()=>t(),!0):t()},Ca=[1800,3e3],Oa=[.1,.25],Aa=(t,e={})=>{((n,r={})=>{Ye(()=>{const s=wt(),o=Ce("FCP");let a;const i=be("paint",c=>{c.forEach(u=>{u.name==="first-contentful-paint"&&(i.disconnect(),u.startTime<s.firstHiddenTime&&(o.value=Math.max(u.startTime-Je(),0),o.entries.push(u),a(!0)))})});i&&(a=Re(n,o,Ca,r.reportAllChanges))})})(Et(()=>{const n=Ce("CLS",0);let r,s=0,o=[];const a=c=>{c.forEach(u=>{if(!u.hadRecentInput){const d=o[0],f=o[o.length-1];s&&d&&f&&u.startTime-f.startTime<1e3&&u.startTime-d.startTime<5e3?(s+=u.value,o.push(u)):(s=u.value,o=[u])}}),s>n.value&&(n.value=s,n.entries=o,r())},i=be("layout-shift",a);i&&(r=Re(t,n,Oa,e.reportAllChanges),Oe(()=>{a(i.takeRecords()),r(!0)}),setTimeout(r,0))}))},Da=[100,300],Ia=(t,e={})=>{Ye(()=>{const n=wt(),r=Ce("FID");let s;const o=c=>{c.startTime<n.firstHiddenTime&&(r.value=c.processingStart-c.startTime,r.entries.push(c),s(!0))},a=c=>{c.forEach(o)},i=be("first-input",a);s=Re(t,r,Da,e.reportAllChanges),i&&Oe(Et(()=>{a(i.takeRecords()),i.disconnect()}))})};let us=0,At=1/0,nt=0;const La=t=>{t.forEach(e=>{e.interactionId&&(At=Math.min(At,e.interactionId),nt=Math.max(nt,e.interactionId),us=nt?(nt-At)/7+1:0)})};let sn;const Na=()=>{"interactionCount"in performance||sn||(sn=be("event",La,{type:"event",buffered:!0,durationThreshold:0}))},re=[],Dt=new Map,qa=()=>(sn?us:performance.interactionCount||0)-0,Ma=[],Fa=t=>{var r;if(Ma.forEach(s=>s(t)),!t.interactionId&&t.entryType!=="first-input")return;const e=re[re.length-1],n=Dt.get(t.interactionId);if(n||re.length<10||e&&t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===((r=n.entries[0])==null?void 0:r.startTime)&&n.entries.push(t);else{const s={id:t.interactionId,latency:t.duration,entries:[t]};Dt.set(s.id,s),re.push(s)}re.sort((s,o)=>o.latency-s.latency),re.length>10&&re.splice(10).forEach(s=>Dt.delete(s.id))}},ls=t=>{var r;const e=C.requestIdleCallback||C.setTimeout;let n=-1;return t=Et(t),((r=C.document)==null?void 0:r.visibilityState)==="hidden"?t():(n=e(t),Oe(t)),n},ja=[200,500],Ua=(t,e={})=>{"PerformanceEventTiming"in C&&"interactionId"in PerformanceEventTiming.prototype&&Ye(()=>{Na();const n=Ce("INP");let r;const s=a=>{ls(()=>{a.forEach(Fa);const i=(()=>{const c=Math.min(re.length-1,Math.floor(qa()/50));return re[c]})();i&&i.latency!==n.value&&(n.value=i.latency,n.entries=i.entries,r())})},o=be("event",s,{durationThreshold:e.durationThreshold!=null?e.durationThreshold:40});r=Re(t,n,ja,e.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),Oe(()=>{s(o.takeRecords()),r(!0)}))})},Ha=[2500,4e3],ir={},Ba=(t,e={})=>{Ye(()=>{const n=wt(),r=Ce("LCP");let s;const o=i=>{e.reportAllChanges||(i=i.slice(-1)),i.forEach(c=>{c.startTime<n.firstHiddenTime&&(r.value=Math.max(c.startTime-Je(),0),r.entries=[c],s())})},a=be("largest-contentful-paint",o);if(a){s=Re(t,r,Ha,e.reportAllChanges);const i=Et(()=>{ir[r.id]||(o(a.takeRecords()),a.disconnect(),ir[r.id]=!0,s(!0))});["keydown","click"].forEach(c=>{C.document&&addEventListener(c,()=>ls(i),{once:!0,capture:!0})}),Oe(i)}})},za=[800,1800],on=t=>{var e,n;(e=C.document)!=null&&e.prerendering?Ye(()=>on(t)):((n=C.document)==null?void 0:n.readyState)!=="complete"?addEventListener("load",()=>on(t),!0):setTimeout(t,0)},Wa=(t,e={})=>{const n=Ce("TTFB"),r=Re(t,n,za,e.reportAllChanges);on(()=>{const s=Ke();s&&(n.value=Math.max(s.responseStart-Je(),0),n.entries=[s],r(!0))})},je={},vt={};let ds,ps,fs,ms,hs;function gs(t,e=!1){return Ue("cls",t,Ga,ds,e)}function Ne(t,e){return vs(t,e),vt[t]||(function(n){const r={};n==="event"&&(r.durationThreshold=0),be(n,s=>{Ae(n,{entries:s})},r)}(t),vt[t]=!0),ys(t,e)}function Ae(t,e){const n=je[t];if(n!=null&&n.length)for(const r of n)try{r(e)}catch(s){vn&&k.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${ce(r)}
Error:`,s)}}function Ga(){return Aa(t=>{Ae("cls",{metric:t}),ds=t},{reportAllChanges:!0})}function Ka(){return Ia(t=>{Ae("fid",{metric:t}),ps=t})}function Xa(){return Ba(t=>{Ae("lcp",{metric:t}),fs=t},{reportAllChanges:!0})}function Ja(){return Wa(t=>{Ae("ttfb",{metric:t}),ms=t})}function Ya(){return Ua(t=>{Ae("inp",{metric:t}),hs=t})}function Ue(t,e,n,r,s=!1){let o;return vs(t,e),vt[t]||(o=n(),vt[t]=!0),r&&e({metric:r}),ys(t,e,s?o:void 0)}function vs(t,e){je[t]=je[t]||[],je[t].push(e)}function ys(t,e,n){return()=>{n&&n();const r=je[t];if(!r)return;const s=r.indexOf(e);s!==-1&&r.splice(s,1)}}function It(t){return typeof t=="number"&&isFinite(t)}function se(t,e,n,{...r}){const s=H(t).start_timestamp;return s&&s>e&&typeof t.updateStartTime=="function"&&t.updateStartTime(e),to(t,()=>{const o=ye({startTime:e,...r});return o&&o.end(n),o})}function _s(t){var b;const e=q();if(!e)return;const{name:n,transaction:r,attributes:s,startTime:o}=t,{release:a,environment:i,sendDefaultPii:c}=e.getOptions(),u=e.getIntegrationByName("Replay"),d=u==null?void 0:u.getReplayId(),f=F(),l=f.getUser(),p=l!==void 0?l.email||l.id||l.ip_address:void 0;let m;try{m=f.getScopeData().contexts.profile.profile_id}catch{}const h={release:a,environment:i,user:p||void 0,profile_id:m||void 0,replay_id:d||void 0,transaction:r,"user_agent.original":(b=C.navigator)==null?void 0:b.userAgent,"client.address":c?"{{auto}}":void 0,...s};return ye({name:n,attributes:h,startTime:o,experimental:{standalone:!0}})}function yn(){return C.addEventListener&&C.performance}function M(t){return t/1e3}function bs(t){let e="unknown",n="unknown",r="";for(const s of t){if(s==="/"){[e,n]=t.split("/");break}if(!isNaN(Number(s))){e=r==="h"?"http":r,n=t.split(r)[1];break}r+=s}return r===t&&(e=r),{name:e,version:n}}function Va(){let t,e,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let r=!1;function s(){r||(r=!0,e&&function(a,i,c){var m;vn&&k.log(`Sending CLS span (${a})`);const u=M((Y()||0)+((i==null?void 0:i.startTime)||0)),d=F().getScopeData().transactionName,f=i?Pe((m=i.sources[0])==null?void 0:m.node):"Layout shift",l={[W]:"auto.http.browser.cls",[bt]:"ui.webvital.cls",[pt]:(i==null?void 0:i.duration)||0,"sentry.pageload.span_id":c},p=_s({name:f,transaction:d,attributes:l,startTime:u});p&&(p.addEvent("cls",{[jr]:"",[Fr]:a}),p.end(u))}(n,t,e),o())}const o=gs(({metric:a})=>{const i=a.entries[a.entries.length-1];i&&(n=a.value,t=i)},!0);Oe(()=>{s()}),setTimeout(()=>{const a=q();if(!a)return;const i=a.on("startNavigationSpan",()=>{s(),i==null||i()}),c=X();if(c){const u=ve(c);H(u).op==="pageload"&&(e=u.spanContext().spanId)}},0)}const Qa=2147483647;let G,we,cr=0,B={};function Za({recordClsStandaloneSpans:t}){const e=yn();if(e&&Y()){e.mark&&C.performance.mark("sentry-tracing-init");const n=Ue("fid",({metric:a})=>{const i=a.entries[a.entries.length-1];if(!i)return;const c=M(Y()),u=M(i.startTime);B.fid={value:a.value,unit:"millisecond"},B["mark.fid"]={value:c+u,unit:"second"}},Ka,ps),r=function(a,i=!1){return Ue("lcp",a,Xa,fs,i)}(({metric:a})=>{const i=a.entries[a.entries.length-1];i&&(B.lcp={value:a.value,unit:"millisecond"},G=i)},!0),s=function(a){return Ue("ttfb",a,Ja,ms)}(({metric:a})=>{a.entries[a.entries.length-1]&&(B.ttfb={value:a.value,unit:"millisecond"})}),o=t?Va():gs(({metric:a})=>{const i=a.entries[a.entries.length-1];i&&(B.cls={value:a.value,unit:""},we=i)},!0);return()=>{n(),r(),s(),o==null||o()}}return()=>{}}function ei(t,e){const n=yn(),r=Y();if(!(n!=null&&n.getEntries)||!r)return;const s=M(r),o=n.getEntries(),{op:a,start_timestamp:i}=H(t);if(o.slice(cr).forEach(c=>{const u=M(c.startTime),d=M(Math.max(0,c.duration));if(!(a==="navigation"&&i&&s+u<i))switch(c.entryType){case"navigation":(function(f,l,p){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(m=>{rt(f,l,m,p)}),rt(f,l,"secureConnection",p,"TLS/SSL"),rt(f,l,"fetch",p,"cache"),rt(f,l,"domainLookup",p,"DNS"),function(m,h,b){const E=b+M(h.requestStart),g=b+M(h.responseEnd),x=b+M(h.responseStart);h.responseEnd&&(se(m,E,g,{op:"browser.request",name:h.name,attributes:{[W]:"auto.ui.browser.metrics"}}),se(m,x,g,{op:"browser.response",name:h.name,attributes:{[W]:"auto.ui.browser.metrics"}}))}(f,l,p)})(t,c,s);break;case"mark":case"paint":case"measure":{(function(p,m,h,b,E){const g=Ke(!1),x=M(g?g.requestStart:0),v=E+Math.max(h,x),y=E+h,T=y+b,w={[W]:"auto.resource.browser.metrics"};if(v!==y&&(w["sentry.browser.measure_happened_before_request"]=!0,w["sentry.browser.measure_start_time"]=v),m.detail)if(typeof m.detail=="object")for(const[N,_]of Object.entries(m.detail))if(_&&ze(_))w[`sentry.browser.measure.detail.${N}`]=_;else try{w[`sentry.browser.measure.detail.${N}`]=JSON.stringify(_)}catch{}else if(ze(m.detail))w["sentry.browser.measure.detail"]=m.detail;else try{w["sentry.browser.measure.detail"]=JSON.stringify(m.detail)}catch{}v<=T&&se(p,v,T,{name:m.name,op:m.entryType,attributes:w})})(t,c,u,d,s);const f=wt(),l=c.startTime<f.firstHiddenTime;c.name==="first-paint"&&l&&(B.fp={value:c.startTime,unit:"millisecond"}),c.name==="first-contentful-paint"&&l&&(B.fcp={value:c.startTime,unit:"millisecond"});break}case"resource":(function(f,l,p,m,h,b){if(l.initiatorType==="xmlhttprequest"||l.initiatorType==="fetch")return;const E=xe(p),g={[W]:"auto.resource.browser.metrics"};Lt(g,l,"transferSize","http.response_transfer_size"),Lt(g,l,"encodedBodySize","http.response_content_length"),Lt(g,l,"decodedBodySize","http.decoded_response_content_length");const x=l.deliveryType;x!=null&&(g["http.response_delivery_type"]=x);const v=l.renderBlockingStatus;v&&(g["resource.render_blocking_status"]=v),E.protocol&&(g["url.scheme"]=E.protocol.split(":").pop()),E.host&&(g["server.address"]=E.host),g["url.same_origin"]=p.includes(C.location.origin);const{name:y,version:T}=bs(l.nextHopProtocol);g["network.protocol.name"]=y,g["network.protocol.version"]=T;const w=b+m,N=w+h;se(f,w,N,{name:p.replace(C.location.origin,""),op:l.initiatorType?`resource.${l.initiatorType}`:"resource.other",attributes:g})})(t,c,c.name,u,d,s)}}),cr=Math.max(o.length-1,0),function(c){const u=C.navigator;if(!u)return;const d=u.connection;d&&(d.effectiveType&&c.setAttribute("effectiveConnectionType",d.effectiveType),d.type&&c.setAttribute("connectionType",d.type),It(d.rtt)&&(B["connection.rtt"]={value:d.rtt,unit:"millisecond"})),It(u.deviceMemory)&&c.setAttribute("deviceMemory",`${u.deviceMemory} GB`),It(u.hardwareConcurrency)&&c.setAttribute("hardwareConcurrency",String(u.hardwareConcurrency))}(t),a==="pageload"){(function(u){const d=Ke(!1);if(!d)return;const{responseStart:f,requestStart:l}=d;l<=f&&(u["ttfb.requestTime"]={value:f-l,unit:"millisecond"})})(B);const c=B["mark.fid"];c&&B.fid&&(se(t,c.value,c.value+M(B.fid.value),{name:"first input delay",op:"ui.action",attributes:{[W]:"auto.ui.browser.metrics"}}),delete B["mark.fid"]),"fcp"in B&&e.recordClsOnPageloadSpan||delete B.cls,Object.entries(B).forEach(([u,d])=>{no(u,d.value,d.unit)}),t.setAttribute("performance.timeOrigin",s),t.setAttribute("performance.activationStart",Je()),function(u){G&&(G.element&&u.setAttribute("lcp.element",Pe(G.element)),G.id&&u.setAttribute("lcp.id",G.id),G.url&&u.setAttribute("lcp.url",G.url.trim().slice(0,200)),G.loadTime!=null&&u.setAttribute("lcp.loadTime",G.loadTime),G.renderTime!=null&&u.setAttribute("lcp.renderTime",G.renderTime),u.setAttribute("lcp.size",G.size)),we!=null&&we.sources&&we.sources.forEach((d,f)=>u.setAttribute(`cls.source.${f+1}`,Pe(d.node)))}(t)}G=void 0,we=void 0,B={}}function rt(t,e,n,r,s=n){const o=function(c){return c==="secureConnection"?"connectEnd":c==="fetch"?"domainLookupStart":`${c}End`}(n),a=e[o],i=e[`${n}Start`];i&&a&&se(t,r+M(i),r+M(a),{op:`browser.${s}`,name:e.name,attributes:{[W]:"auto.ui.browser.metrics",...n==="redirect"&&e.redirectCount!=null?{"http.redirect_count":e.redirectCount}:{}}})}function Lt(t,e,n,r){const s=e[n];s!=null&&s<Qa&&(t[r]=s)}const ti=1e3;let ur,Nt,qt,st;function ni(){if(!C.document)return;const t=V.bind(null,"dom"),e=lr(t,!0);C.document.addEventListener("click",e,!1),C.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{var o,a;const r=C,s=(o=r[n])==null?void 0:o.prototype;(a=s==null?void 0:s.hasOwnProperty)!=null&&a.call(s,"addEventListener")&&(K(s,"addEventListener",function(i){return function(c,u,d){if(c==="click"||c=="keypress")try{const f=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},l=f[c]=f[c]||{refCount:0};if(!l.handler){const p=lr(t);l.handler=p,i.call(this,c,p,d)}l.refCount++}catch{}return i.call(this,c,u,d)}}),K(s,"removeEventListener",function(i){return function(c,u,d){if(c==="click"||c=="keypress")try{const f=this.__sentry_instrumentation_handlers__||{},l=f[c];l&&(l.refCount--,l.refCount<=0&&(i.call(this,c,l.handler,d),l.handler=void 0,delete f[c]),Object.keys(f).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return i.call(this,c,u,d)}}))})}function lr(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(o){try{return o.target}catch{return null}}(n);if(function(o,a){return o==="keypress"&&(!(a!=null&&a.tagName)||a.tagName!=="INPUT"&&a.tagName!=="TEXTAREA"&&!a.isContentEditable)}(n.type,r))return;We(n,"_sentryCaptured",!0),r&&!r._sentryId&&We(r,"_sentryId",Me());const s=n.type==="keypress"?"input":n.type;(function(o){if(o.type!==Nt)return!1;try{if(!o.target||o.target._sentryId!==qt)return!1}catch{}return!0})(n)||(t({event:n,name:s,global:e}),Nt=n.type,qt=r?r._sentryId:void 0),clearTimeout(ur),ur=C.setTimeout(()=>{qt=void 0,Nt=void 0},ti)}}function _n(t){const e="history";ue(e,t),le(e,ri)}function ri(){function t(e){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const s=st,o=function(a){try{return new URL(a,C.location.origin).toString()}catch{return a}}(String(r));if(st=o,s===o)return e.apply(this,n);V("history",{from:s,to:o})}return e.apply(this,n)}}C.addEventListener("popstate",()=>{const e=C.location.href,n=st;st=e,n!==e&&V("history",{from:n,to:e})}),"history"in Ge&&Ge.history&&(K(C.history,"pushState",t),K(C.history,"replaceState",t))}const ct={};function dr(t){ct[t]=void 0}const Se="__sentry_xhr_v3__";function Es(t){ue("xhr",t),le("xhr",si)}function si(){if(!C.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;t.open=new Proxy(t.open,{apply(e,n,r){const s=new Error,o=1e3*te(),a=Le(r[0])?r[0].toUpperCase():void 0,i=function(u){if(Le(u))return u;try{return u.toString()}catch{}}(r[1]);if(!a||!i)return e.apply(n,r);n[Se]={method:a,url:i,request_headers:{}},a==="POST"&&i.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const c=()=>{const u=n[Se];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}V("xhr",{endTimestamp:1e3*te(),startTimestamp:o,xhr:n,virtualError:s})}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply:(u,d,f)=>(c(),u.apply(d,f))}):n.addEventListener("readystatechange",c),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,d,f){const[l,p]=f,m=d[Se];return m&&Le(l)&&Le(p)&&(m.request_headers[l.toLowerCase()]=p),u.apply(d,f)}}),e.apply(n,r)}}),t.send=new Proxy(t.send,{apply(e,n,r){const s=n[Se];return s?(r[0]!==void 0&&(s.body=r[0]),V("xhr",{startTimestamp:1e3*te(),xhr:n}),e.apply(n,r)):e.apply(n,r)}})}const Mt=[],ut=new Map;function oi(){if(yn()&&Y()){const t=Ue("inp",({metric:e})=>{if(e.value==null)return;const n=e.entries.find(l=>l.duration===e.value&&pr[l.name]);if(!n)return;const{interactionId:r}=n,s=pr[n.name],o=M(Y()+n.startTime),a=M(e.value),i=X(),c=i?ve(i):void 0,u=(r!=null?ut.get(r):void 0)||c,d=u?H(u).description:F().getScopeData().transactionName,f=_s({name:Pe(n.target),transaction:d,attributes:{[W]:"auto.http.browser.inp",[bt]:`ui.interaction.${s}`,[pt]:n.duration},startTime:o});f&&(f.addEvent("inp",{[jr]:"millisecond",[Fr]:e.value}),f.end(o+a))},Ya,hs);return()=>{t()}}return()=>{}}const pr={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function ai(t,e=function(n){const r=ct[n];if(r)return r;let s=C[n];if(Qt(s))return ct[n]=s.bind(C);const o=C.document;if(o&&typeof o.createElement=="function")try{const a=o.createElement("iframe");a.hidden=!0,o.head.appendChild(a);const i=a.contentWindow;i!=null&&i[n]&&(s=i[n]),o.head.removeChild(a)}catch(a){vn&&k.warn(`Could not create sandbox iframe for ${n} check, bailing to window.${n}: `,a)}return s&&(ct[n]=s.bind(C))}("fetch")){let n=0,r=0;return oa(t,function(s){const o=s.body.length;n+=o,r++;const a={body:s.body,method:"POST",referrerPolicy:"strict-origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};if(!e)return dr("fetch"),ht("No fetch implementation available");try{return e(t.url,a).then(i=>(n-=o,r--,{statusCode:i.status,headers:{"x-sentry-rate-limits":i.headers.get("X-Sentry-Rate-Limits"),"retry-after":i.headers.get("Retry-After")}}))}catch(i){return dr("fetch"),n-=o,r--,ht(i)}})}function Ft(t,e,n,r){const s={filename:t,function:e==="<anonymous>"?Te:e,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const ii=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,ci=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,ui=/\((\S*)(?::(\d+))(?::(\d+))\)/,li=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,di=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,pi=ro([30,t=>{const e=ii.exec(t);if(e){const[,r,s,o]=e;return Ft(r,Te,+s,+o)}const n=ci.exec(t);if(n){if(n[2]&&n[2].indexOf("eval")===0){const o=ui.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}const[r,s]=fr(n[1]||Te,n[2]);return Ft(s,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,t=>{const e=li.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const s=di.exec(e[3]);s&&(e[1]=e[1]||"eval",e[3]=s[1],e[4]=s[2],e[5]="")}let n=e[3],r=e[1]||Te;return[r,n]=fr(r,n),Ft(n,r,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]),fr=(t,e)=>{const n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:Te,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},J=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,mr=1024,fi=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:"Breadcrumbs",setup(n){var r;e.console&&function(s){const o="console";ue(o,s),le(o,ma)}(function(s){return function(o){if(q()!==s)return;const a={category:"console",data:{arguments:o.args,logger:"console"},level:ha(o.level),message:On(o.args," ")};if(o.level==="assert"){if(o.args[0]!==!1)return;a.message=`Assertion failed: ${On(o.args.slice(1)," ")||"console.assert"}`,a.data.arguments=o.args.slice(1)}me(a,{input:o.args,level:o.level})}}(n)),e.dom&&(r=function(s,o){return function(a){if(q()!==s)return;let i,c,u=typeof o=="object"?o.serializeAttribute:void 0,d=typeof o=="object"&&typeof o.maxStringLength=="number"?o.maxStringLength:void 0;d&&d>mr&&(J&&k.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${d} was configured. Sentry will use 1024 instead.`),d=mr),typeof u=="string"&&(u=[u]);try{const l=a.event,p=function(m){return!!m&&!!m.target}(l)?l.target:l;i=Pe(p,{keyAttrs:u,maxStringLength:d}),c=Ur(p)}catch{i="<unknown>"}if(i.length===0)return;const f={category:`ui.${a.name}`,message:i};c&&(f.data={"ui.component_name":c}),me(f,{event:a.event,name:a.name,global:a.global})}}(n,e.dom),ue("dom",r),le("dom",ni)),e.xhr&&Es(function(s){return function(o){if(q()!==s)return;const{startTimestamp:a,endTimestamp:i}=o,c=o.xhr[Se];if(!a||!i||!c)return;const{method:u,url:d,status_code:f,body:l}=c,p={method:u,url:d,status_code:f},m={xhr:o.xhr,input:l,startTimestamp:a,endTimestamp:i},h={category:"xhr",data:p,type:"http",level:sr(f)};s.emit("beforeOutgoingRequestBreadcrumb",h,m),me(h,m)}}(n)),e.fetch&&as(function(s){return function(o){if(q()!==s)return;const{startTimestamp:a,endTimestamp:i}=o;if(i&&(!o.fetchData.url.match(/sentry_key/)||o.fetchData.method!=="POST"))if(o.fetchData.method,o.fetchData.url,o.error){const c=o.fetchData,u={data:o.error,input:o.args,startTimestamp:a,endTimestamp:i},d={category:"fetch",data:c,level:"error",type:"http"};s.emit("beforeOutgoingRequestBreadcrumb",d,u),me(d,u)}else{const c=o.response,u={...o.fetchData,status_code:c==null?void 0:c.status};o.fetchData.request_body_size,o.fetchData.response_body_size;const d={input:o.args,response:c,startTimestamp:a,endTimestamp:i},f={category:"fetch",data:u,type:"http",level:sr(u.status_code)};s.emit("beforeOutgoingRequestBreadcrumb",f,d),me(f,d)}}}(n)),e.history&&_n(function(s){return function(o){if(q()!==s)return;let a=o.from,i=o.to;const c=xe(L.location.href);let u=a?xe(a):void 0;const d=xe(i);u!=null&&u.path||(u=c),c.protocol===d.protocol&&c.host===d.host&&(i=d.relative),c.protocol===u.protocol&&c.host===u.host&&(a=u.relative),me({category:"navigation",data:{from:a,to:i}})}}(n)),e.sentry&&n.on("beforeSendEvent",function(s){return function(o){q()===s&&me({category:"sentry."+(o.type==="transaction"?"transaction":"event"),event_id:o.event_id,level:o.level,message:he(o)},{event:o})}}(n))}}},mi=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],hi=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:"BrowserApiErrors",setupOnce(){e.setTimeout&&K(L,"setTimeout",hr),e.setInterval&&K(L,"setInterval",hr),e.requestAnimationFrame&&K(L,"requestAnimationFrame",gi),e.XMLHttpRequest&&"XMLHttpRequest"in L&&K(XMLHttpRequest.prototype,"send",vi);const n=e.eventTarget;n&&(Array.isArray(n)?n:mi).forEach(yi)}}};function hr(t){return function(...e){const n=e[0];return e[0]=$e(n,{mechanism:{data:{function:ce(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function gi(t){return function(e){return t.apply(this,[$e(e,{mechanism:{data:{function:"requestAnimationFrame",handler:ce(t)},handled:!1,type:"instrument"}})])}}function vi(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&K(n,r,function(s){const o={mechanism:{data:{function:r,handler:ce(s)},handled:!1,type:"instrument"}},a=pn(s);return a&&(o.mechanism.data.handler=ce(a)),$e(s,o)})}),t.apply(this,e)}}function yi(t){var r,s;const e=L,n=(r=e[t])==null?void 0:r.prototype;(s=n==null?void 0:n.hasOwnProperty)!=null&&s.call(n,"addEventListener")&&(K(n,"addEventListener",function(o){return function(a,i,c){try{typeof i.handleEvent=="function"&&(i.handleEvent=$e(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:ce(i),target:t},handled:!1,type:"instrument"}}))}catch{}return o.apply(this,[a,$e(i,{mechanism:{data:{function:"addEventListener",handler:ce(i),target:t},handled:!1,type:"instrument"}}),c])}}),K(n,"removeEventListener",function(o){return function(a,i,c){try{const u=i.__sentry_wrapped__;u&&o.call(this,a,u,c)}catch{}return o.call(this,a,i,c)}}))}const _i=()=>({name:"BrowserSession",setupOnce(){L.document!==void 0?(jn({ignoreDuration:!0}),Un(),_n(({from:t,to:e})=>{t!==void 0&&t!==e&&(jn({ignoreDuration:!0}),Un())})):J&&k.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),bi=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(n){e.onerror&&(function(r){Kr(s=>{const{stackParser:o,attachStacktrace:a}=vr();if(q()!==r||ar())return;const{msg:i,url:c,line:u,column:d,error:f}=s,l=function(p,m,h,b){const E=p.exception=p.exception||{},g=E.values=E.values||[],x=g[0]=g[0]||{},v=x.stacktrace=x.stacktrace||{},y=v.frames=v.frames||[],T=b,w=h,N=Le(m)&&m.length>0?m:Xe();return y.length===0&&y.push({colno:T,filename:N,function:Te,in_app:!0,lineno:w}),p}(nn(o,f||i,void 0,a,!1),c,u,d);l.level="error",Fn(l,{originalException:f,mechanism:{handled:!1,type:"onerror"}})})}(n),gr("onerror")),e.onunhandledrejection&&(function(r){Xr(s=>{const{stackParser:o,attachStacktrace:a}=vr();if(q()!==r||ar())return;const i=function(u){if(ze(u))return u;try{if("reason"in u)return u.reason;if("detail"in u&&"reason"in u.detail)return u.detail.reason}catch{}return u}(s),c=ze(i)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(i)}`}]}}:nn(o,i,void 0,a,!0);c.level="error",Fn(c,{originalException:i,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(n),gr("onunhandledrejection"))}}};function gr(t){J&&k.log(`Global Handler attached: ${t}`)}function vr(){const t=q();return(t==null?void 0:t.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}const Ei=()=>({name:"HttpContext",preprocessEvent(t){var r;if(!L.navigator&&!L.location&&!L.document)return;const e=tn(),n={...e.headers,...(r=t.request)==null?void 0:r.headers};t.request={...e,...t.request,headers:n}}}),wi=(t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(r,s,o){fa(hn,o.getOptions().stackParser,n,e,r,s)}}};function Si(t){const e={};for(const n of Object.getOwnPropertyNames(t)){const r=n;t[r]!==void 0&&(e[r]=t[r])}return e}function Ti(t={}){const e=function(r={}){var s;return{defaultIntegrations:[pa(),ua(),hi(),fi(),bi(),wi(),ga(),Ei(),_i()],release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(s=L.SENTRY_RELEASE)==null?void 0:s.id,sendClientReports:!0,...Si(r)}}(t);if(!e.skipBrowserExtensionCheck&&function(){var u;const r=L.window!==void 0&&L;if(!r)return!1;const s=r[r.chrome?"chrome":"browser"],o=(u=s==null?void 0:s.runtime)==null?void 0:u.id,a=Xe()||"",i=!!o&&L===L.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(d=>a.startsWith(`${d}//`)),c=r.nw!==void 0;return!!o&&!i&&!c}())return void(J&&_t(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));J&&!os()&&k.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill.");const n={...e,stackParser:so(e.stackParser||pi),integrations:Qo(e),transport:e.transport||ai};return ea($a,n)}const yr=new WeakMap,jt=new Map,ws={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function xi(t,e){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:o,enableHTTPTimings:a,tracePropagationTargets:i,onRequestSpanStart:c}={...ws,...e},u=typeof o=="function"?o:l=>!0,d=l=>function(p,m){const h=Xe();if(h){let b,E;try{b=new URL(p,h),E=new URL(h).origin}catch{return!1}const g=b.origin===E;return m?ge(b.toString(),m)||g&&ge(b.pathname,m):g}{const b=!!p.match(/^\/(?!\/)/);return m?ge(p,m):b}}(l,i),f={};n&&(t.addEventProcessor(l=>(l.type==="transaction"&&l.spans&&l.spans.forEach(p=>{if(p.op==="http.client"){const m=jt.get(p.span_id);m&&(p.timestamp=m/1e3,jt.delete(p.span_id))}}),l)),s&&function(l){const p="fetch-body-resolved";ue(p,l),le(p,()=>is(Ea))}(l=>{if(l.response){const p=yr.get(l.response);p&&l.endTimestamp&&jt.set(p,l.endTimestamp)}}),as(l=>{const p=_a(l,u,d,f);if(l.response&&l.fetchData.__span&&yr.set(l.response,l.fetchData.__span),p){const m=br(l.fetchData.url),h=m?xe(m).host:void 0;p.setAttributes({"http.url":m,"server.address":h}),a&&_r(p),c==null||c(p,{headers:l.headers})}})),r&&Es(l=>{var m;const p=function(h,b,E,g){const x=h.xhr,v=x==null?void 0:x[Se];if(!x||x.__sentry_own_request__||!v)return;const{url:y,method:T}=v,w=Be()&&b(y);if(h.endTimestamp&&w){const $=x.__sentry_xhr_span_id__;if(!$)return;const A=g[$];return void(A&&v.status_code!==void 0&&(Lr(A,v.status_code),A.end(),delete g[$]))}const N=br(y),_=xe(N||y),O=(P=y,P.split(/[?#]/,1)[0]),D=!!X(),S=w&&D?ye({name:`${T} ${O}`,attributes:{url:y,type:"xhr","http.method":T,"http.url":N,"server.address":_==null?void 0:_.host,[W]:"auto.http.browser",[bt]:"http.client",...(_==null?void 0:_.search)&&{"http.query":_==null?void 0:_.search},...(_==null?void 0:_.hash)&&{"http.fragment":_==null?void 0:_.hash}}}):new lt;var P;x.__sentry_xhr_span_id__=S.spanContext().spanId,g[x.__sentry_xhr_span_id__]=S,E(y)&&function($,A){const{"sentry-trace":U,baggage:z}=ns({span:A});U&&function(pe,ae,ie){var bn;const Q=(bn=pe.__sentry_xhr_v3__)==null?void 0:bn.request_headers;if(!(Q!=null&&Q["sentry-trace"]))try{if(pe.setRequestHeader("sentry-trace",ae),ie){const St=Q==null?void 0:Q.baggage;St&&St.split(",").some(Ts=>Ts.trim().startsWith("sentry-"))||pe.setRequestHeader("baggage",ie)}}catch{}}($,U,z)}(x,Be()&&D?S:void 0);const R=q();return R&&R.emit("beforeOutgoingRequestSpan",S,h),S}(l,u,d,f);if(p){let h;a&&_r(p);try{h=new Headers((m=l.xhr.__sentry_xhr_v3__)==null?void 0:m.request_headers)}catch{}c==null||c(p,{headers:h})}})}function _r(t){const{url:e}=H(t).data;if(!e||typeof e!="string")return;const n=Ne("resource",({entries:r})=>{r.forEach(s=>{(function(o){return o.entryType==="resource"&&"initiatorType"in o&&typeof o.nextHopProtocol=="string"&&(o.initiatorType==="fetch"||o.initiatorType==="xmlhttprequest")})(s)&&s.name.endsWith(e)&&(function(o){const{name:a,version:i}=bs(o.nextHopProtocol),c=[];return c.push(["network.protocol.version",i],["network.protocol.name",a]),Y()?[...c,["http.request.redirect_start",Z(o.redirectStart)],["http.request.fetch_start",Z(o.fetchStart)],["http.request.domain_lookup_start",Z(o.domainLookupStart)],["http.request.domain_lookup_end",Z(o.domainLookupEnd)],["http.request.connect_start",Z(o.connectStart)],["http.request.secure_connection_start",Z(o.secureConnectionStart)],["http.request.connection_end",Z(o.connectEnd)],["http.request.request_start",Z(o.requestStart)],["http.request.response_start",Z(o.responseStart)],["http.request.response_end",Z(o.responseEnd)]]:c}(s).forEach(o=>t.setAttribute(...o)),setTimeout(n))})})}function Z(t=0){return((Y()||performance.timeOrigin)+t)/1e3}function br(t){try{return new URL(t,L.location.origin).href}catch{return}}const ki=3600,Er="sentry_previous_trace",Pi="sentry.previous_trace";function $i(t,{linkPreviousTrace:e,consistentTraceSampling:n}){const r=e==="session-storage";let s=r?function(){var a;try{const i=(a=L.sessionStorage)==null?void 0:a.getItem(Er);return JSON.parse(i)}catch{return}}():void 0;t.on("spanStart",a=>{if(ve(a)!==a)return;const i=F().getPropagationContext();s=function(c,u,d){const f=H(u);function l(){var h,b;try{return Number((h=d.dsc)==null?void 0:h.sample_rate)??Number((b=f.data)==null?void 0:b[io])}catch{return 0}}const p={spanContext:u.spanContext(),startTimestamp:f.start_timestamp,sampleRate:l(),sampleRand:d.sampleRand};if(!c)return p;const m=c.spanContext;return m.traceId===f.trace_id?c:(Date.now()/1e3-c.startTimestamp<=ki&&(J&&k.info(`Adding previous_trace ${m} link to span ${{op:f.op,...u.spanContext()}}`),u.addLink({context:m,attributes:{[ao]:"previous_trace"}}),u.setAttribute(Pi,`${m.traceId}-${m.spanId}-${Ut(m)?1:0}`)),p)}(s,a,i),r&&function(c){try{L.sessionStorage.setItem(Er,JSON.stringify(c))}catch(u){J&&k.warn("Could not store previous trace in sessionStorage",u)}}(s)});let o=!0;n&&t.on("beforeSampling",a=>{if(!s)return;const i=F(),c=i.getPropagationContext();o&&c.parentSpanId?o=!1:(i.setPropagationContext({...c,dsc:{...c.dsc,sample_rate:String(s.sampleRate),sampled:String(Ut(s.spanContext))},sampleRand:s.sampleRand}),a.parentSampled=Ut(s.spanContext),a.parentSampleRate=s.sampleRate,a.spanAttributes={...a.spanAttributes,[oo]:s.sampleRate})})}function Ut(t){return t.traceFlags===1}const Ri={...it,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...ws};let wr=!1;const Ci=(t={})=>{wr&&_t(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),wr=!0;const e=L.document;Ln||(Ln=!0,Kr(Jt),Xr(Jt));const{enableInp:n,enableLongTask:r,enableLongAnimationFrame:s,_experiments:{enableInteractions:o,enableStandaloneClsSpans:a},beforeStartSpan:i,idleTimeout:c,finalTimeout:u,childSpanTimeout:d,markBackgroundSpan:f,traceFetch:l,traceXHR:p,trackFetchStreamPerformance:m,shouldCreateSpanForRequest:h,enableHTTPTimings:b,instrumentPageLoad:E,instrumentNavigation:g,linkPreviousTrace:x,consistentTraceSampling:v,onRequestSpanStart:y}={...Ri,...t},T=Za({recordClsStandaloneSpans:a||!1});n&&oi(),s&&j.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(_=>{const O=X();if(O)for(const D of _.getEntries()){if(!D.scripts[0])continue;const S=M(Y()+D.startTime),{start_timestamp:P,op:R}=H(O);if(R==="navigation"&&P&&S<P)continue;const $=M(D.duration),A={[W]:"auto.ui.browser.metrics"},U=D.scripts[0],{invoker:z,invokerType:pe,sourceURL:ae,sourceFunctionName:ie,sourceCharPosition:Q}=U;A["browser.script.invoker"]=z,A["browser.script.invoker_type"]=pe,ae&&(A["code.filepath"]=ae),ie&&(A["code.function"]=ie),Q!==-1&&(A["browser.script.source_char_position"]=Q),se(O,S,S+$,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:A})}}).observe({type:"long-animation-frame",buffered:!0}):r&&Ne("longtask",({entries:_})=>{const O=X();if(!O)return;const{op:D,start_timestamp:S}=H(O);for(const P of _){const R=M(Y()+P.startTime),$=M(P.duration);D==="navigation"&&S&&R<S||se(O,R,R+$,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[W]:"auto.ui.browser.metrics"}})}}),o&&Ne("event",({entries:_})=>{const O=X();if(O){for(const D of _)if(D.name==="click"){const S=M(Y()+D.startTime),P=M(D.duration),R={name:Pe(D.target),op:`ui.interaction.${D.name}`,startTime:S,attributes:{[W]:"auto.ui.browser.metrics"}},$=Ur(D.target);$&&(R.attributes["ui.component_name"]=$),se(O,S,S+P,R)}}});const w={name:void 0,source:void 0};function N(_,O){const D=O.op==="pageload",S=i?i(O):O,P=S.attributes||{};O.name!==S.name&&(P[De]="custom",S.attributes=P),w.name=S.name,w.source=P[De];const R=Nn(S,{idleTimeout:c,finalTimeout:u,childSpanTimeout:d,disableAutoFinish:D,beforeSpanEnd:A=>{T(),ei(A,{recordClsOnPageloadSpan:!a}),Tr(_,void 0);const U=F(),z=U.getPropagationContext();U.setPropagationContext({...z,traceId:R.spanContext().traceId,sampled:uo(R),dsc:yt(A)})}});function $(){e&&["interactive","complete"].includes(e.readyState)&&_.emit("idleSpanEnableAutoFinish",R)}Tr(_,R),D&&e&&(e.addEventListener("readystatechange",()=>{$()}),$())}return{name:"BrowserTracing",afterAllSetup(_){let O=Xe();function D(){const S=ot(_);S&&!H(S).timestamp&&(J&&k.log(`[Tracing] Finishing current active span with op: ${H(S).op}`),S.setAttribute(dt,"cancelled"),S.end())}if(_.on("startNavigationSpan",S=>{if(q()!==_)return;D(),Ee().setPropagationContext({traceId:An(),sampleRand:Math.random()});const P=F();P.setPropagationContext({traceId:An(),sampleRand:Math.random()}),P.setSDKProcessingMetadata({normalizedRequest:void 0}),N(_,{op:"navigation",...S})}),_.on("startPageLoadSpan",(S,P={})=>{if(q()!==_)return;D();const R=P.sentryTrace||Sr("sentry-trace"),$=P.baggage||Sr("baggage"),A=co(R,$),U=F();U.setPropagationContext(A),U.setSDKProcessingMetadata({normalizedRequest:tn()}),N(_,{op:"pageload",...S})}),x!=="off"&&$i(_,{linkPreviousTrace:x,consistentTraceSampling:v}),L.location){if(E){const S=Y();(function(P,R,$){P.emit("startPageLoadSpan",R,$),F().setTransactionName(R.name),ot(P)})(_,{name:L.location.pathname,startTime:S?S/1e3:void 0,attributes:{[De]:"url",[W]:"auto.pageload.browser"}})}g&&_n(({to:S,from:P})=>{if(P===void 0&&(O==null?void 0:O.indexOf(S))!==-1)return void(O=void 0);O=void 0;const R=ss(S);(function($,A){$.emit("startNavigationSpan",A),F().setTransactionName(A.name),ot($)})(_,{name:(R==null?void 0:R.pathname)||L.location.pathname,attributes:{[De]:"url",[W]:"auto.navigation.browser"}}),F().setSDKProcessingMetadata({normalizedRequest:{...tn(),url:S}})})}f&&(L.document?L.document.addEventListener("visibilitychange",()=>{const S=X();if(!S)return;const P=ve(S);if(L.document.hidden&&P){const R="cancelled",{op:$,status:A}=H(P);J&&k.log(`[Tracing] Transaction: ${R} -> since tab moved to the background, op: ${$}`),A||P.setStatus({code:He,message:R}),P.setAttribute("sentry.cancellation_reason","document.hidden"),P.end()}}):J&&k.warn("[Tracing] Could not set up background tab detection due to lack of global document")),o&&function(S,P,R,$,A){const U=L.document;let z;const pe=()=>{const ae="ui.action.click",ie=ot(S);if(ie){const Q=H(ie).op;if(["navigation","pageload"].includes(Q))return void(J&&k.warn(`[Tracing] Did not create ${ae} span because a pageload or navigation span is in progress.`))}z&&(z.setAttribute(dt,"interactionInterrupted"),z.end(),z=void 0),A.name?z=Nn({name:A.name,op:ae,attributes:{[De]:A.source||"url"}},{idleTimeout:P,finalTimeout:R,childSpanTimeout:$}):J&&k.warn(`[Tracing] Did not create ${ae} transaction because _latestRouteName is missing.`)};U&&addEventListener("click",pe,{once:!1,capture:!0})}(_,c,u,d,w),n&&function(){const S=({entries:P})=>{const R=X(),$=R&&ve(R);P.forEach(A=>{if(!function(z){return"duration"in z}(A)||!$)return;const U=A.interactionId;if(U!=null&&!ut.has(U)){if(Mt.length>10){const z=Mt.shift();ut.delete(z)}Mt.push(U),ut.set(U,$)}})};Ne("event",S),Ne("first-input",S)}(),xi(_,{traceFetch:l,traceXHR:p,trackFetchStreamPerformance:m,tracePropagationTargets:_.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:h,enableHTTPTimings:b,onRequestSpanStart:y})}}};function Sr(t){const e=L.document,n=e==null?void 0:e.querySelector(`meta[name=${t}]`);return(n==null?void 0:n.getAttribute("content"))||void 0}const Ss="_sentry_idleSpan";function ot(t){return t[Ss]}function Tr(t,e){We(t,Ss,e)}function Oi(t,e,n,r,s){var i;var o=(i=e.$$slots)==null?void 0:i[n],a=!1;o===!0&&(o=e[n==="default"?"children":n],a=!0),o===void 0?s!==null&&s(t):o(t,a?()=>r:r)}function ji(t){const e={};t.children&&(e.default=!0);for(const n in t.$$slots)e[n]=!0;return e}function xr(t,e){return t===e||(t==null?void 0:t[mn])===e}function Ui(t={},e,n,r){return lo(()=>{var s,o;return po(()=>{s=o,o=(r==null?void 0:r())||[],Hr(()=>{t!==n(...o)&&(e(t,...o),s&&xr(n(...s),t)&&e(null,...s))})}),()=>{fo(()=>{o&&xr(n(...o),t)&&e(null,...o)})}}),t}let qe=!1,an=Symbol();function Hi(t,e,n){const r=n[e]??(n[e]={store:null,source:Gt(void 0),unsubscribe:Wt});if(r.store!==t&&!(an in n))if(r.unsubscribe(),r.store=t??null,t==null)r.source.v=void 0,r.unsubscribe=Wt;else{var s=!0;r.unsubscribe=go(t,o=>{s?r.source.v=o:mt(r.source,o)}),s=!1}return t&&an in n?vo(t):oe(r.source)}function Bi(t,e,n){let r=n[e];return r&&r.store!==t&&(r.unsubscribe(),r.unsubscribe=Wt),t}function zi(t,e){return t.set(e),e}function Wi(){const t={};return[t,function(){mo(()=>{for(var e in t)t[e].unsubscribe();ho(t,an,{enumerable:!1,value:!0})})}]}function Gi(t,e,n){return t.set(n),e}function Ki(){qe=!0}const Ai={get(t,e){if(!t.exclude.includes(e))return t.props[e]},set:(t,e)=>!1,getOwnPropertyDescriptor(t,e){if(!t.exclude.includes(e))return e in t.props?{enumerable:!0,configurable:!0,value:t.props[e]}:void 0},has:(t,e)=>!t.exclude.includes(e)&&e in t.props,ownKeys:t=>Reflect.ownKeys(t.props).filter(e=>!t.exclude.includes(e))};function Xi(t,e,n){return new Proxy({props:t,exclude:e},Ai)}const Di={get(t,e){if(!t.exclude.includes(e))return oe(t.version),e in t.special?t.special[e]():t.props[e]},set:(t,e,n)=>(e in t.special||(t.special[e]=ee({get[e](){return t.props[e]}},e,Br)),t.special[e](n),Dn(t.version),!0),getOwnPropertyDescriptor(t,e){if(!t.exclude.includes(e))return e in t.props?{enumerable:!0,configurable:!0,value:t.props[e]}:void 0},deleteProperty:(t,e)=>(t.exclude.includes(e)||(t.exclude.push(e),Dn(t.version)),!0),has:(t,e)=>!t.exclude.includes(e)&&e in t.props,ownKeys:t=>Reflect.ownKeys(t.props).filter(e=>!t.exclude.includes(e))};function kr(t,e){return new Proxy({props:t,exclude:e,special:{},version:ko(0)},Di)}const Ii={get(t,e){let n=t.props.length;for(;n--;){let r=t.props[n];if(Ie(r)&&(r=r()),typeof r=="object"&&r!==null&&e in r)return r[e]}},set(t,e,n){let r=t.props.length;for(;r--;){let s=t.props[r];Ie(s)&&(s=s());const o=Kt(s,e);if(o&&o.set)return o.set(n),!0}return!1},getOwnPropertyDescriptor(t,e){let n=t.props.length;for(;n--;){let r=t.props[n];if(Ie(r)&&(r=r()),typeof r=="object"&&r!==null&&e in r){const s=Kt(r,e);return s&&!s.configurable&&(s.configurable=!0),s}}},has(t,e){if(e===mn||e===Wr)return!1;for(let n of t.props)if(Ie(n)&&(n=n()),n!=null&&e in n)return!0;return!1},ownKeys(t){const e=[];for(let n of t.props)if(Ie(n)&&(n=n()),n){for(const r in n)e.includes(r)||e.push(r);for(const r of Object.getOwnPropertySymbols(n))e.includes(r)||e.push(r)}return e}};function Ji(...t){return new Proxy({props:t},Ii)}function ee(t,e,n,r){var E;var s,o,a=!wo||!!(n&So),i=!!(n&Eo),c=!!(n&xo),u=r,d=!0,f=()=>(d&&(d=!1,u=c?Hr(r):r),u);if(i){var l=mn in t||Wr in t;s=((E=Kt(t,e))==null?void 0:E.set)??(l&&e in t?g=>t[e]=g:void 0)}var p,m=!1;if(i?[o,m]=function(g){var x=qe;try{return qe=!1,[g(),qe]}finally{qe=x}}(()=>t[e]):o=t[e],o===void 0&&r!==void 0&&(o=f(),s&&(a&&yo(),s(o))),p=a?()=>{var g=t[e];return g===void 0?f():(d=!0,g)}:()=>{var g=t[e];return g!==void 0&&(u=void 0),g===void 0?u:g},a&&!(n&Br))return p;if(s){var h=t.$$legacy;return function(g,x){return arguments.length>0?(a&&x&&!h&&!m||s(x?p():g),g):p()}}var b=(n&To?bo:zr)(p);return i&&oe(b),function(g,x){var y;if(arguments.length>0){const T=x?oe(b):a&&i?_o(g):g;return mt(b,T),u!==void 0&&(u=T),g}return v=b,(y=v.ctx)!=null&&y.d?b.v:oe(b);var v}}var Yi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Vi(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function Li(t){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let e=0,n=performance.now(),r=60;const s=[];let o=0;const a=t.lowFramerateThreshold,i=t.slowInpThreshold;if(requestAnimationFrame(function c(u){const d=u-n;if(e++,d>1e3){r=1e3*e/d,e=0,n=u,s.push(r),s.length>10&&s.shift();const f=s.reduce((l,p)=>l+p,0)/s.length;if(r<a){console.error(`[Augment Performance] Slow framerate detected: ${r.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${f.toFixed(1)} fps`);const l=ye({name:"slow_framerate",op:"performance.monitoring",attributes:{"performance.fps":r,"performance.avg_fps":f,"performance.threshold":a,"performance.is_critical":r<15,"webview.url":window.location.href}});l.setStatus({code:2,message:`Slow framerate: ${r.toFixed(1)} fps`}),l.end()}}requestAnimationFrame(c)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(c=>{(u=>{const d=u.getEntries().filter(p=>"interactionId"in p&&"duration"in p&&p.startTime>0&&p.duration<1e6);if(d.length===0)return;d.sort((p,m)=>m.duration-p.duration);const f=Math.floor(.98*d.length),l=d[Math.min(f,d.length-1)].duration;if(l>i){console.error(`[Augment Performance] Slow INP detected: ${l.toFixed(1)} ms`);let p=null;const m=d[0];m&&"target"in m&&(p=m.target,console.error("[Augment Performance] Slow interaction target:",p,m));const h=ye({name:"slow_inp",op:"performance.monitoring",attributes:{"performance.inp":l,"performance.threshold":i,"performance.target":p?String(p):void 0,"webview.url":window.location.href}});h.setStatus({code:2,message:`Slow INP: ${l.toFixed(1)} ms`}),h.end(),l>o&&(o=l)}})(c)}).observe({entryTypes:["event","first-input"],buffered:!0})}catch(c){console.error("[Augment Performance] Error setting up INP monitoring:",c)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>r,window.augmentPerformance.getWorstINP=()=>o}const Pr=16,$r=200;function Rr(){var t;return((t=window.augmentFlags)==null?void 0:t.enablePerformanceMonitoring)??!1}let Cr=!1;function Ni(t){return t?{"data-ds-color":t}:{}}function Qi(t){return{"data-ds-radius":t}}function Zi(t,e,n){return n?{[`data-ds-${t}-${e}`]:!0,[`data-${e}`]:!0}:{}}(function(){var n,r;const t=!!((r=(n=window.augmentFlags)==null?void 0:n.sentry)!=null&&r.enabled);var e;(e={enabled:Rr(),lowFramerateThreshold:Pr,slowInpThreshold:$r}).enabled&&Li({lowFramerateThreshold:e.lowFramerateThreshold||Pr,slowInpThreshold:e.slowInpThreshold||$r}),Rr()&&!t&&console.warn("[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.")})(),function(){var e,n;if(!((n=(e=window.augmentFlags)==null?void 0:e.sentry)!=null&&n.enabled))return;const t=window.augmentFlags.sentry;if(t)if(Cr)console.warn("Sentry is already initialized, duplicate initialization attempt");else try{(function(r){const s={...r};ts(s,"svelte"),Ti(s)})({dsn:t.dsn,release:t.release,environment:t.environment,tracesSampleRate:t.tracesSampleRate||0,replaysSessionSampleRate:t.replaysSessionSampleRate||0,replaysOnErrorSampleRate:t.replaysOnErrorSampleRate||0,sampleRate:t.errorSampleRate||0,sendDefaultPii:t.sendDefaultPii!==void 0&&t.sendDefaultPii,integrations:(()=>{const r=[];return t.tracesSampleRate&&t.tracesSampleRate>0&&r.push(Ci()),r})(),beforeSend:r=>{var s;return(s=t.release)!=null&&s.endsWith("@999.999.999")?null:r}}),t.tags&&Object.entries(t.tags).forEach(([r,s])=>{(function(o,a){Ee().setTag(o,a)})(r,String(s))}),Cr=!0}catch(r){console.error("Failed to initialize Sentry:",r)}else console.warn("Sentry configuration not found in window.augmentDeps")}();var qi=Gr("<span><!></span>");function ec(t,e){const n=kr(e,["children","$$slots","$$events","$$legacy"]),r=kr(n,["size","weight","type","color","truncate"]);Po(e,!1);const s=Gt(),o=Gt();let a=ee(e,"size",8,3),i=ee(e,"weight",8,"regular"),c=ee(e,"type",8,"default"),u=ee(e,"color",24,()=>{}),d=ee(e,"truncate",8,!1);$o(()=>(oe(s),oe(o),No(r)),()=>{mt(s,r.class),mt(o,Lo(r,["class"]))}),Ro(),Co();var f=qi();Oo(f,(l,p)=>({...l,class:`c-text c-text--size-${a()??""} c-text--weight-${i()??""} c-text--type-${c()??""} c-text--color-${u()??""} ${oe(s)??""}`,...oe(o),[Io]:p}),[()=>u()?Ni(u()):{},()=>({"c-text--has-color":u()!==void 0,"c-text--truncate":d()})],"svelte-zmgqjq"),Oi(Do(f),e,"default",{},null),Xt(t,f),Ao()}var Mi=Gr('<div data-testid="spinner-augment"><div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div> <div class="c-spinner__leaf svelte-abmqgo"></div></div>');function tc(t,e){let n=ee(e,"size",8,2),r=ee(e,"loading",8,!0),s=ee(e,"useCurrentColor",8,!1),o=ee(e,"class",8,"");var a=qo(),i=Mo(a),c=u=>{var d=Mi();let f;jo(l=>f=Uo(d,1,`c-spinner c-spinner--size-${n()??""} ${o()??""}`,"svelte-abmqgo",f,l),[()=>({"c-spinner--current-color":s()})],zr),Xt(u,d)};Fo(i,u=>{r()&&u(c)}),Xt(t,a)}export{tc as S,ec as T,Oi as a,Wi as b,Yi as c,Ni as d,Hi as e,Bi as f,Vi as g,Ji as h,Ui as i,Qi as j,zi as k,kr as l,Gi as m,Zi as n,Ki as o,ee as p,Xi as r,ji as s};
