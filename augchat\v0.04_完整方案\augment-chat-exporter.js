#!/usr/bin/env node

/**
 * Augment聊天记录完整导出工具
 * 自动发现所有工作区，提取聊天记录，生成Markdown文件
 * 支持增量更新和智能文件命名
 *
 * 使用方法:
 *   node augment-chat-exporter.js                    # 默认启用去重
 *   node augment-chat-exporter.js --no-dedup        # 关闭去重
 *   node augment-chat-exporter.js --verbose-dedup   # 启用去重并显示详细分析
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 解析命令行参数
const args = process.argv.slice(2);
const NO_DEDUP = args.includes('--no-dedup');
const VERBOSE_DEDUP = args.includes('--verbose-dedup');

console.log(`🔧 去重设置: ${NO_DEDUP ? '关闭' : '开启'}${VERBOSE_DEDUP ? ' (详细模式)' : ''}`);

// 生成带时间戳的输出目录名
function generateOutputDir() {
    const now = new Date();
    const timestamp = now.toISOString()
        .replace(/:/g, '-')
        .replace(/\./g, '-')
        .split('T')[0] + '_' +
        now.toTimeString().split(' ')[0].replace(/:/g, '-');
    return path.join(__dirname, `conversations_export_${timestamp}`);
}

// 配置
const CONFIG = {
    outputDir: generateOutputDir(),
    tempDir: path.join(__dirname, 'temp_export'),
    maxTitleLength: 80, // 🔧 优化：增加标题长度，更好地描述对话内容
    maxMessageLength: 15000,
    includeMetadata: true,
    searchPaths: [
        path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User', 'workspaceStorage'),
        path.join(os.homedir(), '.vscode', 'User', 'workspaceStorage'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Code', 'User', 'workspaceStorage')
    ]
};

// Level库导入
let Level;
try {
    const levelModule = require('level');
    Level = levelModule.Level || levelModule.default || levelModule;
} catch (error) {
    console.error('❌ 无法加载level库，请运行: npm install level');
    process.exit(1);
}

// 工具函数
const utils = {
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    },

    formatDate(date = new Date()) {
        return date.toISOString().replace(/[:.]/g, '-').split('.')[0];
    },

    sanitizeFilename(filename) {
        return filename
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, '_')
            .replace(/_{2,}/g, '_')
            .substring(0, 100);
    },

    extractTitle(conversation) {
        const messages = conversation.messages || [];

        // 🔧 修复：先按时间排序消息，确保获取真正的第一条消息
        const sortedMessages = [...messages].sort((a, b) => {
            const getTimestamp = (message) => {
                if (message.timestamp) {
                    try {
                        const timestamp = new Date(message.timestamp).getTime();
                        if (!isNaN(timestamp)) {
                            return timestamp;
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                }

                // 备选方案：使用exchangeId
                if (message.exchangeId) {
                    const uuidMatch = message.exchangeId.match(/^([0-9a-f]{8})-/i);
                    if (uuidMatch) {
                        return parseInt(uuidMatch[1], 16);
                    }
                }

                return 0;
            };

            return getTimestamp(a) - getTimestamp(b);
        });

        // 查找第一个有意义的用户消息（按时间顺序）
        for (const message of sortedMessages) {
            if (message.request_message && message.request_message.trim()) {
                let title = message.request_message.trim();
                title = title.replace(/[\r\n]+/g, ' ').substring(0, CONFIG.maxTitleLength);
                if (title.length >= CONFIG.maxTitleLength - 3) title += '...';
                return title;
            }
        }

        // 如果没有用户消息，查找AI回复（按时间顺序）
        for (const message of sortedMessages) {
            if (message.response_text && message.response_text.trim()) {
                let title = message.response_text.trim();
                title = title.replace(/[\r\n]+/g, ' ').substring(0, CONFIG.maxTitleLength);
                if (title.length >= CONFIG.maxTitleLength - 3) title += '...';
                return title;
            }
        }

        return '未命名对话';
    },

    // 🆕 新增：提取第一条和最后一条消息的时间戳
    extractMessageTimeRange(conversation) {
        const messages = conversation.messages || [];

        // 按时间排序消息
        const sortedMessages = [...messages].sort((a, b) => {
            const getTimestamp = (message) => {
                if (message.timestamp) {
                    try {
                        const timestamp = new Date(message.timestamp).getTime();
                        if (!isNaN(timestamp)) {
                            return timestamp;
                        }
                    } catch (e) {
                        // 忽略错误
                    }
                }

                // 备选方案：使用exchangeId
                if (message.exchangeId) {
                    const uuidMatch = message.exchangeId.match(/^([0-9a-f]{8})-/i);
                    if (uuidMatch) {
                        return parseInt(uuidMatch[1], 16);
                    }
                }

                return 0;
            };

            return getTimestamp(a) - getTimestamp(b);
        });

        if (sortedMessages.length === 0) {
            return 'unknown';
        }

        // 格式化时间的辅助函数
        const formatTime = (timestamp) => {
            try {
                const date = new Date(timestamp);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const seconds = String(date.getSeconds()).padStart(2, '0');
                return `${month}${day}_${hours}${minutes}${seconds}`;
            } catch (e) {
                return null;
            }
        };

        // 查找第一个有意义的用户消息的时间戳
        let firstTime = null;
        for (const message of sortedMessages) {
            if (message.request_message && message.request_message.trim() && message.timestamp) {
                firstTime = formatTime(message.timestamp);
                if (firstTime) break;
            }
        }

        // 如果没有找到用户消息的时间戳，使用第一条消息的时间戳
        if (!firstTime && sortedMessages[0].timestamp) {
            firstTime = formatTime(sortedMessages[0].timestamp);
        }

        // 查找最后一条消息的时间戳
        let lastTime = null;
        for (let i = sortedMessages.length - 1; i >= 0; i--) {
            const message = sortedMessages[i];
            if (message.timestamp) {
                lastTime = formatTime(message.timestamp);
                if (lastTime) break;
            }
        }

        // 如果只有一条消息或者时间相同，只返回一个时间
        if (!firstTime || !lastTime || firstTime === lastTime) {
            return firstTime || lastTime || 'unknown';
        }

        // 返回时间范围：第一条_最后一条
        return `${firstTime}_${lastTime}`;
    },

    // 🔧 保持向后兼容性的函数
    extractFirstMessageTime(conversation) {
        const timeRange = this.extractMessageTimeRange(conversation);
        // 如果是时间范围，只返回第一个时间
        return timeRange.split('_')[0] || 'unknown';
    },

    getWorkspaceName(workspacePath) {
        const match = workspacePath.match(/workspaceStorage[\\\/]([^\\\/]+)[\\\/]/);
        return match ? match[1].substring(0, 12) : 'unknown';
    },

    copyDatabase(sourcePath, targetPath) {
        try {
            if (fs.existsSync(sourcePath)) {
                const files = fs.readdirSync(sourcePath);
                utils.ensureDir(targetPath);
                
                for (const file of files) {
                    if (file !== 'LOCK') {
                        const sourceFile = path.join(sourcePath, file);
                        const targetFile = path.join(targetPath, file);
                        fs.copyFileSync(sourceFile, targetFile);
                    }
                }
                return true;
            }
        } catch (error) {
            // 忽略复制错误，继续处理
        }
        return false;
    },

    getFileHash(filePath) {
        try {
            const stats = fs.statSync(filePath);
            return `${stats.size}_${stats.mtime.getTime()}`;
        } catch {
            return null;
        }
    }
};

// 自动发现所有工作区
function discoverWorkspaces() {
    console.log('🔍 自动发现VSCode工作区...');
    const workspaces = [];
    
    for (const searchPath of CONFIG.searchPaths) {
        if (fs.existsSync(searchPath)) {
            console.log(`📂 搜索路径: ${searchPath}`);
            
            try {
                const workspaceDirs = fs.readdirSync(searchPath);
                
                for (const workspaceDir of workspaceDirs) {
                    const augmentPath = path.join(searchPath, workspaceDir, 'Augment.vscode-augment', 'augment-kv-store');
                    
                    if (fs.existsSync(augmentPath)) {
                        workspaces.push({
                            id: workspaceDir,
                            path: augmentPath,
                            name: utils.getWorkspaceName(augmentPath)
                        });
                    }
                }
            } catch (error) {
                console.warn(`⚠️  搜索路径 ${searchPath} 访问失败: ${error.message}`);
            }
        }
    }
    
    console.log(`✅ 发现 ${workspaces.length} 个工作区`);
    return workspaces;
}

// 使用字符串提取方法
function extractWithStrings(dbPath, workspaceId) {
    try {
        const files = fs.readdirSync(dbPath);
        const conversations = [];
        
        for (const file of files) {
            if (file.endsWith('.log') || file.endsWith('.ldb')) {
                const filePath = path.join(dbPath, file);
                
                try {
                    const content = fs.readFileSync(filePath, 'binary');
                    const strings = [];
                    let currentString = '';
                    
                    for (let i = 0; i < content.length; i++) {
                        const char = content.charCodeAt(i);
                        
                        if (char >= 32 && char <= 126) {
                            currentString += String.fromCharCode(char);
                        } else {
                            if (currentString.length >= 4) {
                                strings.push(currentString);
                            }
                            currentString = '';
                        }
                    }
                    
                    if (currentString.length >= 4) {
                        strings.push(currentString);
                    }
                    
                    // 查找JSON数据
                    for (let i = 0; i < strings.length; i++) {
                        const str = strings[i];
                        
                        if (str.includes('conversationId') || 
                            str.includes('request_message') || 
                            str.includes('response_text')) {
                            
                            try {
                                const jsonMatch = str.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g);
                                if (jsonMatch) {
                                    for (const jsonStr of jsonMatch) {
                                        try {
                                            const parsed = JSON.parse(jsonStr);
                                            conversations.push({
                                                workspaceId: workspaceId,
                                                source: file,
                                                data: parsed,
                                                method: 'strings'
                                            });
                                        } catch {
                                            // JSON解析失败，继续
                                        }
                                    }
                                }
                            } catch (parseError) {
                                // 解析错误，跳过
                            }
                        }
                    }
                } catch (error) {
                    // 文件读取错误，跳过
                }
            }
        }
        
        return conversations;
    } catch (error) {
        return [];
    }
}

// 使用LevelDB方法
async function extractWithLevelDB(dbPath, workspaceId) {
    try {
        const tempDbPath = path.join(CONFIG.tempDir, `${workspaceId}_db`);
        if (!utils.copyDatabase(dbPath, tempDbPath)) {
            return [];
        }

        const conversations = [];
        
        try {
            const db = new Level(tempDbPath, { 
                createIfMissing: false,
                errorIfExists: false,
                valueEncoding: 'json'
            });
            
            const iterator = db.iterator();
            
            for await (const [key, value] of iterator) {
                try {
                    const keyStr = key.toString();
                    
                    if (keyStr.includes('exchange:') || keyStr.includes('metadata:')) {
                        conversations.push({
                            workspaceId: workspaceId,
                            key: keyStr,
                            value: value,
                            method: 'leveldb'
                        });
                    }
                } catch (parseError) {
                    // 跳过解析错误的记录
                }
            }
            
            await db.close();
        } catch (dbError) {
            // 数据库操作失败
        }
        
        // 清理临时文件
        try {
            fs.rmSync(tempDbPath, { recursive: true, force: true });
        } catch (cleanupError) {
            // 清理失败，忽略
        }
        
        return conversations;
    } catch (error) {
        return [];
    }
}

// 解析对话数据
function parseConversations(allData) {
    const conversations = new Map();

    // 用于去重的消息集合（基于内容哈希）
    const messageHashes = new Map(); // conversationId -> Set of message hashes
    const duplicateInfo = new Map(); // 记录重复消息的详细信息

    // 生成消息内容哈希
    const getMessageHash = (message) => {
        const request = (message.request_message || '').trim();
        const response = (message.response_text || '').trim();

        // 使用更精确的哈希策略
        if (NO_DEDUP) {
            return null; // 不去重时返回null
        }

        // 策略1: 如果内容很短，使用完整内容
        if (request.length + response.length < 200) {
            return request + '|||' + response;
        }

        // 策略2: 对于长内容，使用更智能的哈希
        const requestHash = request.length > 100 ?
            request.substring(0, 50) + '...' + request.substring(request.length - 50) :
            request;
        const responseHash = response.length > 100 ?
            response.substring(0, 50) + '...' + response.substring(response.length - 50) :
            response;

        return requestHash + '|||' + responseHash;
    };

    // 处理strings方法的结果
    allData.filter(item => item.method === 'strings').forEach(item => {
        try {
            const data = item.data;
            if (data && data.conversationId) {
                const convId = data.conversationId;

                if (!conversations.has(convId)) {
                    conversations.set(convId, {
                        conversationId: convId,
                        workspaceId: item.workspaceId,
                        messages: [],
                        metadata: {
                            source: 'strings',
                            firstSeen: data.timestamp,
                            lastSeen: data.timestamp
                        }
                    });
                    messageHashes.set(convId, new Set());
                }

                // 检查消息是否重复（如果启用去重）
                let shouldAddMessage = true;

                if (!NO_DEDUP) {
                    const messageHash = getMessageHash({
                        request_message: data.request_message || '',
                        response_text: data.response_text || ''
                    });

                    if (messageHash && messageHashes.get(convId).has(messageHash)) {
                        shouldAddMessage = false;
                        // 记录重复信息用于分析
                        if (!duplicateInfo.has(convId)) {
                            duplicateInfo.set(convId, []);
                        }
                        duplicateInfo.get(convId).push({
                            timestamp: data.timestamp,
                            source: item.source,
                            hash: messageHash.substring(0, 50) + '...'
                        });
                    } else if (messageHash) {
                        messageHashes.get(convId).add(messageHash);
                    }
                }

                if (shouldAddMessage) {
                    const conv = conversations.get(convId);
                    conv.messages.push({
                        timestamp: data.timestamp,
                        request_message: data.request_message || '',
                        response_text: data.response_text || '',
                        uuid: data.uuid,
                        source: item.source
                    });

                    if (data.timestamp > conv.metadata.lastSeen) {
                        conv.metadata.lastSeen = data.timestamp;
                    }
                }
            }
        } catch (error) {
            // 解析错误，跳过
        }
    });
    
    // 处理leveldb方法的结果
    allData.filter(item => item.method === 'leveldb').forEach(item => {
        try {
            if (item.key && item.key.startsWith('exchange:')) {
                const keyParts = item.key.split(':');
                if (keyParts.length >= 3) {
                    const convId = keyParts[1];

                    if (!conversations.has(convId)) {
                        conversations.set(convId, {
                            conversationId: convId,
                            workspaceId: item.workspaceId,
                            messages: [],
                            metadata: {
                                source: 'leveldb',
                                firstSeen: new Date().toISOString(),
                                lastSeen: new Date().toISOString()
                            }
                        });
                        messageHashes.set(convId, new Set());
                    }

                    const messageData = item.value || {};

                    // 检查消息是否重复（如果启用去重）
                    let shouldAddMessage = true;

                    if (!NO_DEDUP) {
                        const messageHash = getMessageHash({
                            request_message: messageData.request_message || '',
                            response_text: messageData.response_text || ''
                        });

                        if (messageHash && messageHashes.get(convId).has(messageHash)) {
                            shouldAddMessage = false;
                            // 记录重复信息用于分析
                            if (!duplicateInfo.has(convId)) {
                                duplicateInfo.set(convId, []);
                            }
                            duplicateInfo.get(convId).push({
                                timestamp: messageData.timestamp,
                                source: 'leveldb',
                                exchangeId: keyParts[2],
                                hash: messageHash.substring(0, 50) + '...'
                            });
                        } else if (messageHash) {
                            messageHashes.get(convId).add(messageHash);
                        }
                    }

                    if (shouldAddMessage) {
                        const conv = conversations.get(convId);
                        conv.messages.push({
                            exchangeId: keyParts[2],
                            timestamp: messageData.timestamp || null, // 🔧 修复：添加timestamp
                            request_message: messageData.request_message || '',
                            response_text: messageData.response_text || '',
                            request_nodes: messageData.request_nodes || [],
                            source: 'leveldb'
                        });
                    }
                }
            }
        } catch (error) {
            // 解析错误，跳过
        }
    });
    
    // 输出统计信息
    if (NO_DEDUP) {
        console.log('📊 消息统计（未去重）:');
        let totalMessages = 0;
        for (const [convId, conversation] of conversations) {
            totalMessages += conversation.messages.length;
            console.log(`   - ${convId.substring(0, 8)}: ${conversation.messages.length} 条消息`);
        }
        console.log(`   - 总计: ${totalMessages} 条消息`);
    } else {
        console.log('📊 消息去重统计:');
        let totalOriginalMessages = 0;
        let totalUniqueMessages = 0;

        for (const [convId, conversation] of conversations) {
            const originalCount = allData.filter(item =>
                (item.data && item.data.conversationId === convId) ||
                (item.key && item.key.includes(convId))
            ).length;

            totalOriginalMessages += originalCount;
            totalUniqueMessages += conversation.messages.length;

            const duplicates = originalCount - conversation.messages.length;
            if (duplicates > 0) {
                console.log(`   - ${convId.substring(0, 8)}: ${originalCount} → ${conversation.messages.length} (去重 ${duplicates} 条)`);
            }
        }

        console.log(`   - 总计: ${totalOriginalMessages} → ${totalUniqueMessages} (去重 ${totalOriginalMessages - totalUniqueMessages} 条)`);

        // 显示详细的去重分析
        if (VERBOSE_DEDUP && duplicateInfo.size > 0) {
            console.log('\n🔍 详细去重分析:');
            for (const [convId, duplicates] of duplicateInfo) {
                if (duplicates.length > 0) {
                    console.log(`\n   📋 对话 ${convId.substring(0, 8)}:`);
                    console.log(`      - 发现 ${duplicates.length} 条重复消息`);

                    // 按来源分组统计
                    const sourceStats = {};
                    duplicates.forEach(dup => {
                        sourceStats[dup.source] = (sourceStats[dup.source] || 0) + 1;
                    });

                    for (const [source, count] of Object.entries(sourceStats)) {
                        console.log(`      - ${source}: ${count} 条重复`);
                    }

                    // 显示前3个重复消息的哈希
                    console.log(`      - 重复内容示例:`);
                    duplicates.slice(0, 3).forEach((dup, index) => {
                        console.log(`        ${index + 1}. ${dup.hash}`);
                    });
                }
            }
        }
    }

    return conversations;
}

// 生成Markdown内容
function generateMarkdown(conversation, workspaceName) {
    const title = utils.extractTitle(conversation);

    let markdown = '';

    // 文件头部标识
    markdown += `<!-- 对话文件: ${conversation.conversationId} -->\n`;
    markdown += `<!-- 生成时间: ${new Date().toISOString()} -->\n`;
    markdown += `<!-- 工作区: ${conversation.workspaceId} -->\n\n`;

    // 标题
    markdown += `# ${title}\n\n`;

    // 重要提示
    markdown += `> 📋 **对话文件说明**  \n`;
    markdown += `> 这是一个独立的对话文件，包含完整的对话记录。  \n`;
    markdown += `> 对话ID: \`${conversation.conversationId}\`  \n`;
    markdown += `> 如果文件中出现其他对话ID，那是因为对话内容中引用了其他对话。\n\n`;

    // 元数据
    if (CONFIG.includeMetadata) {
        markdown += `## 📋 对话信息\n\n`;
        markdown += `- **对话ID**: \`${conversation.conversationId}\`\n`;
        markdown += `- **工作区**: ${workspaceName} (\`${conversation.workspaceId}\`)\n`;
        markdown += `- **消息数量**: ${conversation.messages.length}\n`;
        markdown += `- **数据源**: ${conversation.metadata.source}\n`;
        markdown += `- **开始时间**: ${new Date(conversation.metadata.firstSeen).toLocaleString('zh-CN')}\n`;
        markdown += `- **最后更新**: ${new Date(conversation.metadata.lastSeen).toLocaleString('zh-CN')}\n\n`;
        markdown += `---\n\n`;
    }

    // 对话内容
    markdown += `## 💬 对话内容\n\n`;

    // 按时间排序消息 - 使用与Augment插件相同的排序逻辑
    const sortedMessages = conversation.messages.sort((a, b) => {
        // 🎯 关键发现：Augment插件使用timestamp排序，而不是exchangeId！
        // 通过分析真实数据库发现，100%的消息都有timestamp，插件按时间戳排序

        // 优先使用timestamp（这是Augment插件的实际排序方法）
        const getTimestamp = (message) => {
            if (message.timestamp) {
                try {
                    const timestamp = new Date(message.timestamp).getTime();
                    if (!isNaN(timestamp)) {
                        return timestamp;
                    }
                } catch (e) {
                    console.warn(`⚠️  无效的时间戳: ${message.timestamp}`);
                }
            }

            // 备选方案：如果没有timestamp，使用exchangeId
            if (message.exchangeId) {
                // 对于UUID格式的exchangeId，使用第一部分的十六进制值
                const uuidMatch = message.exchangeId.match(/^([0-9a-f]{8})-/i);
                if (uuidMatch) {
                    return parseInt(uuidMatch[1], 16);
                }

                // 对于temp-fe格式
                const tempMatch = message.exchangeId.match(/^temp-fe-([0-9a-f]{8})-/i);
                if (tempMatch) {
                    return parseInt(tempMatch[1], 16);
                }

                // 对于纯数字格式
                const numericMatch = message.exchangeId.match(/^(\d+)$/);
                if (numericMatch) {
                    return parseInt(numericMatch[1], 10);
                }

                // 其他情况使用字符串哈希
                let hash = 0;
                for (let i = 0; i < message.exchangeId.length; i++) {
                    const char = message.exchangeId.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                return Math.abs(hash);
            }

            // 最后的备选方案：返回0
            return 0;
        };

        const timeA = getTimestamp(a);
        const timeB = getTimestamp(b);

        // 按时间戳排序（早的在前）
        return timeA - timeB;
    });

    // 添加排序验证和调试信息
    console.log(`📊 [${conversation.conversationId.substring(0, 8)}] 消息排序完成: ${sortedMessages.length} 条消息`);

    // 验证排序结果
    let sortingIssues = 0;
    for (let i = 1; i < sortedMessages.length; i++) {
        const prevTime = sortedMessages[i-1].timestamp || sortedMessages[i-1].exchangeId || '0';
        const currTime = sortedMessages[i].timestamp || sortedMessages[i].exchangeId || '0';

        // 简单的顺序检查
        if (prevTime > currTime && prevTime !== '0' && currTime !== '0') {
            sortingIssues++;
        }
    }

    if (sortingIssues > 0) {
        console.log(`⚠️  [${conversation.conversationId.substring(0, 8)}] 发现 ${sortingIssues} 个可能的排序问题`);
    }

    sortedMessages.forEach((message, index) => {
        markdown += `### 消息 ${index + 1}\n\n`;

        // 时间戳
        if (message.timestamp) {
            markdown += `**时间**: ${new Date(message.timestamp).toLocaleString('zh-CN')}\n\n`;
        }

        // 用户消息
        if (message.request_message && message.request_message.trim()) {
            markdown += `**👤 用户**:\n\n`;
            const truncatedRequest = message.request_message.length > CONFIG.maxMessageLength
                ? message.request_message.substring(0, CONFIG.maxMessageLength) + '\n\n...[内容过长，已截断]...'
                : message.request_message;
            markdown += `${truncatedRequest}\n\n`;
        }

        // AI回复
        if (message.response_text && message.response_text.trim()) {
            markdown += `**🤖 助手**:\n\n`;
            const truncatedResponse = message.response_text.length > CONFIG.maxMessageLength
                ? message.response_text.substring(0, CONFIG.maxMessageLength) + '\n\n...[内容过长，已截断]...'
                : message.response_text;
            markdown += `${truncatedResponse}\n\n`;
        }

        // 工具使用信息
        if (message.request_nodes && message.request_nodes.length > 0) {
            markdown += `**🔧 工具使用**:\n\n`;
            message.request_nodes.forEach((node, nodeIndex) => {
                if (node.tool_result_node) {
                    markdown += `${nodeIndex + 1}. **工具结果**:\n`;
                    markdown += `   - 工具ID: \`${node.tool_result_node.tool_use_id || '未知'}\`\n`;
                    markdown += `   - 是否错误: ${node.tool_result_node.is_error ? '是' : '否'}\n`;
                    if (node.tool_result_node.content) {
                        const truncatedContent = node.tool_result_node.content.length > 1000
                            ? node.tool_result_node.content.substring(0, 1000) + '\n...[内容过长，已截断]...'
                            : node.tool_result_node.content;
                        markdown += `   - 内容: \n\`\`\`\n${truncatedContent}\n\`\`\`\n`;
                    }
                    markdown += `\n`;
                }
            });
        }

        // 元数据
        if (CONFIG.includeMetadata) {
            markdown += `<details>\n<summary>📊 消息元数据</summary>\n\n`;
            markdown += `- **消息序号**: ${index + 1} / ${sortedMessages.length}\n`;
            markdown += `- **消息ID**: \`${message.uuid || message.exchangeId || '未知'}\`\n`;
            markdown += `- **数据源**: ${message.source || '未知'}\n`;

            // 添加排序相关的调试信息
            if (message.timestamp) {
                markdown += `- **时间戳**: \`${message.timestamp}\`\n`;
                markdown += `- **时间戳数值**: \`${new Date(message.timestamp).getTime()}\`\n`;
            }
            if (message.exchangeId) {
                markdown += `- **交换ID**: \`${message.exchangeId}\`\n`;

                // 解析exchangeId的数值
                let exchangeIdValue = 'NaN';
                const numericMatch = message.exchangeId.match(/^(\d+)$/);
                if (numericMatch) {
                    exchangeIdValue = numericMatch[1];
                } else {
                    const uuidMatch = message.exchangeId.match(/^([0-9a-f]+)-/i);
                    if (uuidMatch) {
                        exchangeIdValue = `0x${uuidMatch[1]} (${parseInt(uuidMatch[1], 16)})`;
                    } else {
                        const leadingNumberMatch = message.exchangeId.match(/^(\d+)/);
                        if (leadingNumberMatch) {
                            exchangeIdValue = leadingNumberMatch[1];
                        }
                    }
                }
                markdown += `- **交换ID数值**: \`${exchangeIdValue}\`\n`;
            }

            // 显示排序键
            const getSortKeyForDisplay = (msg) => {
                if (msg.timestamp) return new Date(msg.timestamp).getTime();
                if (msg.exchangeId) {
                    // 使用与排序相同的逻辑
                    const numericMatch = msg.exchangeId.match(/^(\d+)$/);
                    if (numericMatch) {
                        return parseInt(numericMatch[1], 10);
                    }
                    const uuidMatch = msg.exchangeId.match(/^([0-9a-f]+)-/i);
                    if (uuidMatch) {
                        const hexValue = parseInt(uuidMatch[1], 16);
                        return isNaN(hexValue) ? 0 : hexValue;
                    }
                    const leadingNumberMatch = msg.exchangeId.match(/^(\d+)/);
                    if (leadingNumberMatch) {
                        return parseInt(leadingNumberMatch[1], 10);
                    }
                    // 字符串哈希
                    let hash = 0;
                    for (let i = 0; i < msg.exchangeId.length; i++) {
                        const char = msg.exchangeId.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }
                    return Math.abs(hash);
                }
                return 0;
            };
            markdown += `- **排序键**: \`${getSortKeyForDisplay(message)}\`\n`;

            markdown += `\n</details>\n\n`;
        }

        markdown += `---\n\n`;
    });

    // 页脚
    markdown += `\n---\n\n`;
    markdown += `*导出时间: ${new Date().toLocaleString('zh-CN')}*\n`;
    markdown += `*导出工具: Augment聊天记录完整导出器 v3.0*\n`;

    return markdown;
}



// 生成索引文件
function generateIndex(conversations, workspaces, stats) {
    let indexContent = `# Augment聊天记录导出\n\n`;

    // 导出统计
    indexContent += `## 📊 导出统计\n\n`;
    indexContent += `- **导出时间**: ${stats.exportTime}\n`;
    indexContent += `- **工作区数量**: ${workspaces.length}\n`;
    indexContent += `- **总对话数**: ${conversations.size}\n`;
    indexContent += `- **生成文件数**: ${stats.newConversations}\n`;
    indexContent += `- **总消息数**: ${stats.totalMessages}\n\n`;

    // 按工作区分组
    indexContent += `## 📁 按工作区分类\n\n`;

    const workspaceGroups = new Map();
    for (const [, conversation] of conversations) {
        const workspaceId = conversation.workspaceId;
        if (!workspaceGroups.has(workspaceId)) {
            workspaceGroups.set(workspaceId, []);
        }
        workspaceGroups.get(workspaceId).push(conversation);
    }

    for (const [workspaceId, convs] of workspaceGroups) {
        const workspace = workspaces.find(w => w.id === workspaceId);
        const workspaceName = workspace ? workspace.name : workspaceId.substring(0, 12);

        indexContent += `### 🏢 ${workspaceName} (${convs.length} 个对话)\n\n`;

        convs.forEach(conversation => {
            const title = utils.extractTitle(conversation);
            const timeRange = utils.extractMessageTimeRange(conversation);
            const filename = `${workspaceName}_${timeRange}_${utils.sanitizeFilename(title)}_${conversation.conversationId.substring(0, 8)}.md`;
            indexContent += `- [${title}](./${filename}) (${conversation.messages.length} 条消息)\n`;
        });

        indexContent += `\n`;
    }

    // 最近对话
    indexContent += `## 🕒 最近对话 (按时间排序)\n\n`;

    const sortedConversations = Array.from(conversations.values())
        .sort((a, b) => new Date(b.metadata.lastSeen) - new Date(a.metadata.lastSeen))
        .slice(0, 10);

    sortedConversations.forEach(conversation => {
        const workspace = workspaces.find(w => w.id === conversation.workspaceId);
        const workspaceName = workspace ? workspace.name : conversation.workspaceId.substring(0, 12);
        const title = utils.extractTitle(conversation);
        const timeRange = utils.extractMessageTimeRange(conversation);
        const filename = `${workspaceName}_${timeRange}_${utils.sanitizeFilename(title)}_${conversation.conversationId.substring(0, 8)}.md`;
        const lastUpdate = new Date(conversation.metadata.lastSeen).toLocaleString('zh-CN');

        indexContent += `- [${title}](./${filename}) - ${lastUpdate} (${conversation.messages.length} 条消息)\n`;
    });

    indexContent += `\n---\n\n`;
    indexContent += `*生成时间: ${new Date().toLocaleString('zh-CN')}*\n`;
    indexContent += `*工具版本: Augment聊天记录完整导出器 v3.0*\n`;

    return indexContent;
}

// 主函数
async function main() {
    console.log('🚀 Augment聊天记录完整导出器 v3.0');
    console.log('=====================================\n');

    // 创建输出目录
    utils.ensureDir(CONFIG.outputDir);
    utils.ensureDir(CONFIG.tempDir);

    // 发现所有工作区
    const workspaces = discoverWorkspaces();

    if (workspaces.length === 0) {
        console.log('❌ 未发现任何Augment工作区');
        console.log('💡 请确保VSCode已安装Augment插件并有聊天记录');
        return;
    }

    console.log(`\n📊 开始处理 ${workspaces.length} 个工作区...\n`);

    const allData = [];
    const stats = {
        newConversations: 0,
        totalMessages: 0,
        exportTime: new Date().toLocaleString('zh-CN')
    };

    // 处理每个工作区
    for (let i = 0; i < workspaces.length; i++) {
        const workspace = workspaces[i];
        console.log(`🔍 [${i + 1}/${workspaces.length}] 处理工作区: ${workspace.name}`);
        console.log(`📂 路径: ${workspace.path}`);

        try {
            // 方法1: 字符串提取
            console.log(`   📝 使用字符串提取方法...`);
            const stringsData = extractWithStrings(workspace.path, workspace.id);
            console.log(`   ✅ 字符串方法: ${stringsData.length} 条记录`);

            // 方法2: LevelDB提取
            console.log(`   💾 使用LevelDB提取方法...`);
            const leveldbData = await extractWithLevelDB(workspace.path, workspace.id);
            console.log(`   ✅ LevelDB方法: ${leveldbData.length} 条记录`);

            allData.push(...stringsData, ...leveldbData);

        } catch (error) {
            console.error(`   ❌ 处理失败: ${error.message}`);
        }

        console.log('');
    }

    console.log(`📊 数据提取完成，共 ${allData.length} 条原始记录\n`);

    // 解析对话数据
    console.log('🔄 解析对话数据...');
    const conversations = parseConversations(allData);
    console.log(`✅ 解析完成，发现 ${conversations.size} 个对话\n`);

    if (conversations.size === 0) {
        console.log('❌ 未发现任何有效对话');
        return;
    }

    // 生成Markdown文件
    console.log('📝 生成Markdown文件...\n');

    for (const [convId, conversation] of conversations) {
        try {
            const workspace = workspaces.find(w => w.id === conversation.workspaceId);
            const workspaceName = workspace ? workspace.name : conversation.workspaceId.substring(0, 12);
            const title = utils.extractTitle(conversation);
            const timeRange = utils.extractMessageTimeRange(conversation);
            const filename = `${workspaceName}_${timeRange}_${utils.sanitizeFilename(title)}_${convId.substring(0, 8)}.md`;
            const outputPath = path.join(CONFIG.outputDir, filename);

            // 生成Markdown文件
            const markdown = generateMarkdown(conversation, workspaceName);
            fs.writeFileSync(outputPath, markdown, 'utf8');

            stats.newConversations++;
            console.log(`✅ 生成: ${filename}`);

            stats.totalMessages += conversation.messages.length;

        } catch (error) {
            console.error(`❌ 生成对话 ${convId} 失败: ${error.message}`);
        }
    }

    // 生成索引文件
    console.log(`\n📋 生成索引文件...`);
    const indexContent = generateIndex(conversations, workspaces, stats);
    const indexPath = path.join(CONFIG.outputDir, 'README.md');
    fs.writeFileSync(indexPath, indexContent, 'utf8');
    console.log(`✅ 索引文件: ${indexPath}`);

    // 清理临时文件
    try {
        fs.rmSync(CONFIG.tempDir, { recursive: true, force: true });
    } catch (cleanupError) {
        // 清理失败，忽略
    }

    // 显示完成报告
    console.log('\n🎉 导出完成！');
    console.log('=====================================');
    console.log(`📂 输出目录: ${CONFIG.outputDir}`);
    console.log(`📊 处理统计:`);
    console.log(`   - 工作区数量: ${workspaces.length}`);
    console.log(`   - 总对话数: ${conversations.size}`);
    console.log(`   - 总消息数: ${stats.totalMessages}`);
    console.log(`   - 生成文件数: ${stats.newConversations}`);
    console.log(`\n💡 查看结果:`);
    console.log(`   - 打开文件夹: start "${CONFIG.outputDir}"`);
    console.log(`   - 查看索引: start "${indexPath}"`);
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        console.error(error.stack);
        process.exit(1);
    });
}

module.exports = { main, discoverWorkspaces, extractWithStrings, extractWithLevelDB };
