#!/usr/bin/env node

/**
 * 将导出的聊天记录转换为Markdown格式
 * 每个对话生成一个独立的Markdown文件，便于预览
 */

const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
    inputFile: path.join(__dirname, 'TargetWorkspaceConversations', 'target_workspace_219eaf1da08a5e8387de19f31f58d75e_2025-08-21T05-53-49-864Z.json'),
    outputDir: path.join(__dirname, 'conversations_markdown_current'),
    maxMessageLength: 15000, // 增加最大消息长度
    includeMetadata: true // 是否包含元数据
};

// 工具函数
const utils = {
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`✅ 创建目录: ${dirPath}`);
        }
    },

    formatDate(dateString) {
        if (!dateString) return '未知时间';
        try {
            return new Date(dateString).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch {
            return dateString;
        }
    },

    sanitizeFilename(filename) {
        // 移除或替换文件名中的非法字符
        return filename
            .replace(/[<>:"/\\|?*]/g, '_')
            .replace(/\s+/g, '_')
            .substring(0, 100); // 限制文件名长度
    },

    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '\n\n...[内容过长，已截断]...';
    },

    extractTitle(conversation) {
        // 尝试从对话中提取标题
        const messages = conversation.messages || [];

        // 查找第一个有意义的用户消息
        for (const message of messages) {
            if (message.request_message && message.request_message.trim()) {
                let title = message.request_message.trim();
                // 清理标题
                title = title.replace(/[\r\n]+/g, ' ').substring(0, 50);
                if (title.length > 47) title += '...';
                return title;
            }
        }

        // 如果没有用户消息，查找AI回复
        for (const message of messages) {
            if (message.response_text && message.response_text.trim()) {
                let title = message.response_text.trim();
                title = title.replace(/[\r\n]+/g, ' ').substring(0, 50);
                if (title.length > 47) title += '...';
                return title;
            }
        }

        return `对话_${conversation.conversationId?.substring(0, 8) || '未知'}`;
    }
};

// 解析聊天记录数据
function parseConversationData(data) {
    console.log('🔍 开始解析聊天记录数据...');
    
    const conversations = new Map();
    
    // 处理strings方法的结果
    if (data.methods?.strings?.results) {
        console.log(`📄 处理Strings方法结果: ${data.methods.strings.results.length} 条`);
        
        data.methods.strings.results.forEach(item => {
            try {
                let conversationData;
                
                if (typeof item.data === 'object' && item.data.conversationId) {
                    conversationData = item.data;
                } else if (typeof item.data === 'string') {
                    // 尝试解析JSON字符串，使用更健壮的方法
                    try {
                        // 首先尝试直接解析
                        conversationData = JSON.parse(item.data);
                    } catch {
                        // 如果失败，尝试提取JSON部分
                        const jsonMatches = item.data.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g);
                        if (jsonMatches) {
                            for (const jsonStr of jsonMatches) {
                                try {
                                    const parsed = JSON.parse(jsonStr);
                                    if (parsed.conversationId) {
                                        conversationData = parsed;
                                        break;
                                    }
                                } catch {
                                    // 继续尝试下一个
                                }
                            }
                        }
                    }
                }
                
                if (conversationData && conversationData.conversationId) {
                    const convId = conversationData.conversationId;
                    
                    if (!conversations.has(convId)) {
                        conversations.set(convId, {
                            conversationId: convId,
                            title: '',
                            messages: [],
                            metadata: {
                                source: 'strings',
                                firstSeen: conversationData.timestamp,
                                lastSeen: conversationData.timestamp
                            }
                        });
                    }
                    
                    const conv = conversations.get(convId);
                    conv.messages.push({
                        uuid: conversationData.uuid,
                        timestamp: conversationData.timestamp,
                        request_message: conversationData.request_message || '',
                        response_text: conversationData.response_text || '',
                        status: conversationData.status,
                        seen_state: conversationData.seen_state,
                        source: item.source,
                        lineNumber: item.lineNumber
                    });
                    
                    // 更新最后见到的时间
                    if (conversationData.timestamp > conv.metadata.lastSeen) {
                        conv.metadata.lastSeen = conversationData.timestamp;
                    }
                }
            } catch (error) {
                console.warn(`⚠️  解析strings数据时出错: ${error.message}`);
            }
        });
    }
    
    // 处理leveldb方法的结果
    if (data.methods?.leveldb?.results) {
        console.log(`💾 处理LevelDB方法结果: ${data.methods.leveldb.results.length} 条`);
        
        data.methods.leveldb.results.forEach(item => {
            try {
                if (item.key && item.key.startsWith('exchange:')) {
                    const keyParts = item.key.split(':');
                    if (keyParts.length >= 3) {
                        const convId = keyParts[1];
                        const exchangeId = keyParts[2];
                        
                        if (!conversations.has(convId)) {
                            conversations.set(convId, {
                                conversationId: convId,
                                title: '',
                                messages: [],
                                metadata: {
                                    source: 'leveldb',
                                    firstSeen: new Date().toISOString(),
                                    lastSeen: new Date().toISOString()
                                }
                            });
                        }
                        
                        const conv = conversations.get(convId);
                        const messageData = item.value || {};
                        
                        conv.messages.push({
                            exchangeId: exchangeId,
                            request_id: messageData.request_id,
                            request_message: messageData.request_message || '',
                            response_text: messageData.response_text || '',
                            request_nodes: messageData.request_nodes || [],
                            source: 'leveldb_exchange'
                        });
                    }
                }
            } catch (error) {
                console.warn(`⚠️  解析leveldb数据时出错: ${error.message}`);
            }
        });
    }
    
    console.log(`📊 解析完成，找到 ${conversations.size} 个对话`);
    return conversations;
}

// 生成Markdown内容
function generateMarkdown(conversation) {
    const title = utils.extractTitle(conversation);
    conversation.title = title;
    
    let markdown = '';
    
    // 标题
    markdown += `# ${title}\n\n`;
    
    // 元数据
    if (CONFIG.includeMetadata) {
        markdown += `## 📋 对话信息\n\n`;
        markdown += `- **对话ID**: \`${conversation.conversationId}\`\n`;
        markdown += `- **消息数量**: ${conversation.messages.length}\n`;
        markdown += `- **数据源**: ${conversation.metadata.source}\n`;
        markdown += `- **开始时间**: ${utils.formatDate(conversation.metadata.firstSeen)}\n`;
        markdown += `- **最后更新**: ${utils.formatDate(conversation.metadata.lastSeen)}\n\n`;
        markdown += `---\n\n`;
    }
    
    // 对话内容
    markdown += `## 💬 对话内容\n\n`;
    
    // 按时间排序消息
    const sortedMessages = conversation.messages.sort((a, b) => {
        const timeA = a.timestamp || a.request_id || '0';
        const timeB = b.timestamp || b.request_id || '0';
        return timeA.localeCompare(timeB);
    });
    
    sortedMessages.forEach((message, index) => {
        markdown += `### 消息 ${index + 1}\n\n`;
        
        // 时间戳
        if (message.timestamp) {
            markdown += `**时间**: ${utils.formatDate(message.timestamp)}\n\n`;
        }
        
        // 用户消息
        if (message.request_message && message.request_message.trim()) {
            markdown += `**👤 用户**:\n\n`;
            markdown += `${utils.truncateText(message.request_message, CONFIG.maxMessageLength)}\n\n`;
        }
        
        // AI回复
        if (message.response_text && message.response_text.trim()) {
            markdown += `**🤖 助手**:\n\n`;
            markdown += `${utils.truncateText(message.response_text, CONFIG.maxMessageLength)}\n\n`;
        }
        
        // 工具使用信息
        if (message.request_nodes && message.request_nodes.length > 0) {
            markdown += `**🔧 工具使用**:\n\n`;
            message.request_nodes.forEach((node, nodeIndex) => {
                if (node.tool_result_node) {
                    markdown += `${nodeIndex + 1}. **工具结果**:\n`;
                    markdown += `   - 工具ID: \`${node.tool_result_node.tool_use_id || '未知'}\`\n`;
                    markdown += `   - 是否错误: ${node.tool_result_node.is_error ? '是' : '否'}\n`;
                    if (node.tool_result_node.content) {
                        markdown += `   - 内容: \n\`\`\`\n${utils.truncateText(node.tool_result_node.content, 1000)}\n\`\`\`\n`;
                    }
                    markdown += `\n`;
                }
            });
        }
        
        // 元数据
        if (CONFIG.includeMetadata) {
            markdown += `<details>\n<summary>📊 消息元数据</summary>\n\n`;
            markdown += `- **消息ID**: \`${message.uuid || message.exchangeId || '未知'}\`\n`;
            markdown += `- **请求ID**: \`${message.request_id || '未知'}\`\n`;
            markdown += `- **状态**: ${message.status || '未知'}\n`;
            markdown += `- **查看状态**: ${message.seen_state || '未知'}\n`;
            markdown += `- **数据源**: ${message.source || '未知'}\n`;
            if (message.lineNumber) {
                markdown += `- **行号**: ${message.lineNumber}\n`;
            }
            markdown += `\n</details>\n\n`;
        }
        
        markdown += `---\n\n`;
    });
    
    // 页脚
    markdown += `\n---\n\n`;
    markdown += `*导出时间: ${utils.formatDate(new Date().toISOString())}*\n`;
    markdown += `*导出工具: Augment聊天记录导出器 v2.0*\n`;
    
    return markdown;
}

// 主函数
async function main() {
    console.log('🚀 开始转换聊天记录为Markdown格式...');
    
    // 检查输入文件
    if (!fs.existsSync(CONFIG.inputFile)) {
        console.error(`❌ 输入文件不存在: ${CONFIG.inputFile}`);
        return;
    }
    
    // 创建输出目录
    utils.ensureDir(CONFIG.outputDir);
    
    // 读取和解析数据
    console.log(`📖 读取文件: ${CONFIG.inputFile}`);
    const rawData = fs.readFileSync(CONFIG.inputFile, 'utf8');
    const data = JSON.parse(rawData);
    
    // 解析对话数据
    const conversations = parseConversationData(data);
    
    if (conversations.size === 0) {
        console.log('⚠️  没有找到有效的对话数据');
        return;
    }
    
    // 生成Markdown文件
    console.log(`📝 开始生成Markdown文件...`);
    let successCount = 0;
    
    for (const [convId, conversation] of conversations) {
        try {
            const markdown = generateMarkdown(conversation);
            const filename = utils.sanitizeFilename(`${conversation.title}_${convId.substring(0, 8)}.md`);
            const filepath = path.join(CONFIG.outputDir, filename);
            
            fs.writeFileSync(filepath, markdown, 'utf8');
            console.log(`✅ 生成: ${filename}`);
            successCount++;
            
        } catch (error) {
            console.error(`❌ 生成对话 ${convId} 的Markdown失败: ${error.message}`);
        }
    }
    
    // 生成索引文件
    const indexPath = path.join(CONFIG.outputDir, 'README.md');
    let indexContent = `# Augment聊天记录导出\n\n`;
    indexContent += `## 📊 导出统计\n\n`;
    indexContent += `- **总对话数**: ${conversations.size}\n`;
    indexContent += `- **成功生成**: ${successCount}\n`;
    indexContent += `- **导出时间**: ${utils.formatDate(new Date().toISOString())}\n\n`;
    indexContent += `## 📁 对话列表\n\n`;
    
    for (const [convId, conversation] of conversations) {
        const filename = utils.sanitizeFilename(`${conversation.title}_${convId.substring(0, 8)}.md`);
        indexContent += `- [${conversation.title}](./${filename}) (${conversation.messages.length} 条消息)\n`;
    }
    
    fs.writeFileSync(indexPath, indexContent, 'utf8');
    
    console.log(`\n🎉 转换完成！`);
    console.log(`📂 输出目录: ${CONFIG.outputDir}`);
    console.log(`📄 生成文件: ${successCount} 个对话 + 1 个索引`);
    console.log(`📋 查看索引: ${indexPath}`);
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { main, parseConversationData, generateMarkdown };
