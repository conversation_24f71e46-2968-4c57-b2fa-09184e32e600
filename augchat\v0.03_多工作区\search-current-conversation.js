#!/usr/bin/env node

/**
 * 专门搜索当前对话的脚本
 * 基于我们已知的成功方法，但扩展搜索范围
 */

const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
    outputDir: path.join(__dirname, 'CurrentConversationSearch'),
    searchKeywords: [
        '导出.*聊天记录',
        'convert.*markdown',
        'extract.*conversations',
        '预览.*markdown',
        '不适合预览',
        '一个一个对话',
        'open-conversations',
        'conversations_markdown',
        'augchat',
        '移动.*文件',
        '获取.*最新.*聊天',
        '现在有新的问题',
        '导出来的不适合',
        '正常都是一个一个对话',
        'VSCode.*Augment',
        'RAGFlow.*组件',
        'API.*初始化',
        '前端.*后端.*代码库'
    ]
};

// 所有可能的工作区路径（从您提供的列表）
const ALL_WORKSPACES = [
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\219eaf1da08a5e8387de19f31f58d75e\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\91a2d8828768bfdcc8646e574d310d75\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\2ba6bc3b1b2a1f3ba7a4a8ea1105cd57\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\ccef8695afe889a5115b76456b29a41b\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\fa39d3dd4cc9582898a169ca6ce47e57\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\f69c4dd7acbd82e5d292fd208beae2dd\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\dfb7adde504ea8bee353a40950c45a36\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\d936be34f07e8cf57dc708858dc61051\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\b6d890d3f556d2e6e839579e3049b903\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\93e3fcb5538492ab23bde2738d5b5943\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\841f87d0dcd2557c2c63ae1811040913\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\50d248fde9cb6028e63cc7a5222f9163\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\50c4287b8fb1a115aa9cc1c101639aba\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\43ef5d38dbe60a500811a7825f3e6e70\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\3f0eee39ea191a2cc88c4c6bbd97548d\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\396d6b9c01523ad7cabe8a7d664d7852\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\1c10c6d49ca5c5c837f4aedf23adbde2\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\0a1a719385069c57568eca409e126cc8\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\be0818f388a073cd7f2825038ea6bf6f\\Augment.vscode-augment\\augment-kv-store'
];

// 工具函数
const utils = {
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
    },

    formatDate() {
        return new Date().toISOString().replace(/[:.]/g, '-').split('.')[0];
    },

    getWorkspaceId(workspacePath) {
        const match = workspacePath.match(/workspaceStorage[\\\/]([^\\\/]+)[\\\/]/);
        return match ? match[1] : 'unknown';
    },

    checkForKeywords(text) {
        if (!text) return [];
        const matches = [];
        CONFIG.searchKeywords.forEach(keyword => {
            const regex = new RegExp(keyword, 'gi');
            const keywordMatches = text.match(regex);
            if (keywordMatches) {
                matches.push({
                    keyword: keyword,
                    matches: keywordMatches,
                    count: keywordMatches.length
                });
            }
        });
        return matches;
    },

    getFileStats(filePath) {
        try {
            const stats = fs.statSync(filePath);
            return {
                size: stats.size,
                modified: stats.mtime.toISOString(),
                sizeFormatted: (stats.size / 1024 / 1024).toFixed(2) + 'MB'
            };
        } catch {
            return null;
        }
    }
};

// 使用PowerShell的findstr命令搜索文件内容
function searchInFiles(dbPath, workspaceId) {
    console.log(`🔍 [${workspaceId}] 搜索文件内容...`);
    
    try {
        if (!fs.existsSync(dbPath)) {
            console.log(`⏭️  [${workspaceId}] 路径不存在，跳过`);
            return [];
        }

        const files = fs.readdirSync(dbPath);
        const results = [];
        
        for (const file of files) {
            if (file.endsWith('.log') || file.endsWith('.ldb')) {
                const filePath = path.join(dbPath, file);
                const fileStats = utils.getFileStats(filePath);
                
                console.log(`📄 [${workspaceId}] 检查文件: ${file} (${fileStats?.sizeFormatted || '未知大小'})`);
                
                // 对每个关键词进行搜索
                for (const keyword of CONFIG.searchKeywords) {
                    try {
                        const { execSync } = require('child_process');
                        
                        // 使用PowerShell的Select-String命令搜索
                        const command = `powershell -Command "Select-String -Path '${filePath}' -Pattern '${keyword}' -AllMatches | Select-Object -First 10"`;
                        
                        const output = execSync(command, { 
                            encoding: 'utf8',
                            timeout: 10000, // 10秒超时
                            maxBuffer: 10 * 1024 * 1024 // 10MB buffer
                        });
                        
                        if (output.trim()) {
                            console.log(`🎯 [${workspaceId}] 在 ${file} 中发现关键词 "${keyword}"`);
                            
                            results.push({
                                workspaceId: workspaceId,
                                file: file,
                                keyword: keyword,
                                matches: output.trim().split('\n').slice(0, 5), // 只保留前5个匹配
                                fileStats: fileStats,
                                timestamp: new Date().toISOString()
                            });
                        }
                        
                    } catch (searchError) {
                        // 搜索失败，继续下一个关键词
                        if (searchError.message.includes('timeout')) {
                            console.warn(`⏰ [${workspaceId}] 搜索 ${file} 超时，跳过`);
                            break; // 跳过这个文件的其他关键词
                        }
                    }
                }
            }
        }
        
        console.log(`📊 [${workspaceId}] 搜索完成: ${results.length} 个匹配`);
        return results;
        
    } catch (error) {
        console.error(`❌ [${workspaceId}] 搜索失败: ${error.message}`);
        return [];
    }
}

// 主函数
async function main() {
    console.log('🚀 开始搜索当前对话内容...');
    console.log(`📂 将搜索 ${ALL_WORKSPACES.length} 个工作区`);
    console.log(`🔍 搜索关键词: ${CONFIG.searchKeywords.length} 个`);
    
    utils.ensureDir(CONFIG.outputDir);
    
    const allResults = {
        timestamp: new Date().toISOString(),
        searchKeywords: CONFIG.searchKeywords,
        workspaces: {},
        summary: {
            totalWorkspaces: ALL_WORKSPACES.length,
            processedWorkspaces: 0,
            totalMatches: 0,
            workspacesWithMatches: 0
        }
    };
    
    // 处理每个工作区
    for (const workspacePath of ALL_WORKSPACES) {
        const workspaceId = utils.getWorkspaceId(workspacePath);
        console.log(`\n🔍 处理工作区: ${workspaceId}`);
        
        try {
            const matches = searchInFiles(workspacePath, workspaceId);
            
            allResults.workspaces[workspaceId] = {
                path: workspacePath,
                matches: matches,
                matchCount: matches.length,
                processed: true
            };
            
            allResults.summary.processedWorkspaces++;
            allResults.summary.totalMatches += matches.length;
            
            if (matches.length > 0) {
                allResults.summary.workspacesWithMatches++;
                console.log(`🎉 [${workspaceId}] 发现 ${matches.length} 个匹配！`);
            }
            
        } catch (error) {
            console.error(`❌ [${workspaceId}] 处理失败: ${error.message}`);
            allResults.workspaces[workspaceId] = {
                path: workspacePath,
                error: error.message,
                processed: false
            };
        }
    }
    
    // 保存结果
    const outputPath = path.join(CONFIG.outputDir, `current_conversation_search_${utils.formatDate()}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2), 'utf8');
    
    console.log(`\n🎉 搜索完成！`);
    console.log(`📄 结果已保存到: ${outputPath}`);
    console.log(`\n📊 搜索摘要:`);
    console.log(`   - 总工作区数: ${allResults.summary.totalWorkspaces}`);
    console.log(`   - 已处理工作区: ${allResults.summary.processedWorkspaces}`);
    console.log(`   - 总匹配数: ${allResults.summary.totalMatches}`);
    console.log(`   - 有匹配的工作区: ${allResults.summary.workspacesWithMatches}`);
    
    // 显示匹配最多的工作区
    console.log(`\n🎯 匹配最多的工作区:`);
    const sortedWorkspaces = Object.entries(allResults.workspaces)
        .filter(([_, data]) => data.matchCount > 0)
        .sort(([_, a], [__, b]) => b.matchCount - a.matchCount)
        .slice(0, 5);
    
    for (const [workspaceId, data] of sortedWorkspaces) {
        console.log(`   - ${workspaceId}: ${data.matchCount} 个匹配`);
        
        // 显示匹配的关键词
        const keywords = [...new Set(data.matches.map(m => m.keyword))];
        console.log(`     关键词: ${keywords.join(', ')}`);
    }
    
    if (allResults.summary.totalMatches > 0) {
        console.log(`\n💡 建议: 检查匹配最多的工作区，很可能包含当前对话！`);
    } else {
        console.log(`\n🤔 没有找到匹配，可能需要：`);
        console.log(`   1. 等待对话保存到数据库`);
        console.log(`   2. 检查是否有新的工作区`);
        console.log(`   3. 尝试不同的搜索关键词`);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { main };
