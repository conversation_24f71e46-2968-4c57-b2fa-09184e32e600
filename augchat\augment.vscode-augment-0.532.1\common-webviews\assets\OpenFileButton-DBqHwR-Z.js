var ce=Object.defineProperty;var de=(_,e,t)=>e in _?ce(_,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):_[e]=t;var c=(_,e,t)=>de(_,typeof e!="symbol"?e+"":e,t);import{w as le,ar as G,f as ue,a as _e,o as z,b as R,x as pe,R as me,y as L,D as fe,F as ve,M as B,I as J,J as Ae,L as Se,C as y,m as U,z as k,G as we,u as ye,Q as be,O as Ce,Y as Ee}from"./legacy-YP6Kq8lu.js";import{l as Ie,s as Re,p as m,b as xe,e as Me,a as Oe}from"./SpinnerAugment-Dpcl1cXc.js";import{b as Te}from"./CardAugment-YBzgmAzG.js";import{T as S,g as Pe}from"./chat-model-context-DZ2DTs5O.js";import{b as V,A as H,s as C,c as x,R as f,a as q}from"./index-B528snJk.js";import{W as M}from"./host-BNehKqab.js";import{E as X,S as Fe,a as j}from"./chat-types-BfwvR7Kn.js";import{E as De,b,S as Le}from"./index-CKSGO-M1.js";import{t as Ue}from"./index-4vhrZf9p.js";import{S as O,i as ke,b as He,R as qe,c as Ne}from"./remote-agents-client-DbhVjGoZ.js";import{R as We}from"./ra-diff-ops-model-BNum2ZUy.js";import{h as $e}from"./IconButtonAugment-CbpcmeFk.js";import{S as Ge}from"./TextAreaAugment-DXi02sx3.js";function ze(_,e){if(_.length===0)return e;if(e.length===0)return _;const t=[];let s=0,n=0;for(;s<_.length&&n<e.length;){const i=_[s].sequence_id,r=e[n].sequence_id;r!==void 0?i!==void 0?i<r?(t.push(_[s]),s++):i>r?(t.push(e[n]),n++):(t.push(e[n]),s++,n++):(console.warn("Existing history has an exchange with an undefined sequence ID"),s++):(console.warn("New history has an exchange with an undefined sequence ID"),n++)}for(;s<_.length;)t.push(_[s]),s++;for(;n<e.length;)t.push(e[n]),n++;return t}class Be{constructor(e){c(this,"_pollingTimers",new Map);c(this,"_pollingInterval");c(this,"_failedAttempts",0);c(this,"_lastSuccessfulFetch",0);this._config=e,this._pollingInterval=e.defaultInterval}start(e){e&&this._pollingTimers.has(e)?this.stop(e):!e&&this._pollingTimers.has("global")&&this.stop("global"),this.refresh(e);const t=setInterval(()=>{this.refresh(e)},this._pollingInterval);e?this._pollingTimers.set(e,t):this._pollingTimers.set("global",t)}stop(e){if(e){const t=this._pollingTimers.get(e);t&&(clearInterval(t),this._pollingTimers.delete(e))}else for(const[t,s]of this._pollingTimers.entries())clearInterval(s),this._pollingTimers.delete(t)}async refresh(e){try{const t=await this._config.refreshFn(e);return this._failedAttempts=0,this._lastSuccessfulFetch=Date.now(),this._pollingInterval=this._config.defaultInterval,this._config.stopCondition&&e&&this._config.stopCondition(t,e)&&this.stop(e),t}catch{return this._failedAttempts++,this._failedAttempts>3?this._pollingInterval=1e4:this._pollingInterval=Math.min(1e3*Math.pow(2,this._failedAttempts),1e4),null}}isPolling(e){return e?this._pollingTimers.has(e):this._pollingTimers.size>0}get timeSinceLastSuccessfulFetch(){return Date.now()-this._lastSuccessfulFetch}get failedAttempts(){return this._failedAttempts}resetFailedAttempts(){this._failedAttempts=0}}class Je{constructor(e,t){c(this,"_state",{agentOverviews:[],agentConversations:new Map,agentLogs:new Map,maxRemoteAgents:0,maxActiveRemoteAgents:0,overviewError:void 0,conversationError:void 0,logsError:void 0,isOverviewsLoading:!1,isConversationLoading:!1,isLogsLoading:!1,logPollFailedCount:0});c(this,"_loggingMaxRetries",8);c(this,"_logsPollingManager");c(this,"_isInitialOverviewFetch",!0);c(this,"_lastOverviewUpdateTimestamp");c(this,"_stateUpdateSubscribers",new Set);c(this,"_pendingStateUpdateOpts");this._flagsModel=e,this._remoteAgentsClient=t,this._logsPollingManager=new Be({defaultInterval:1e3,refreshFn:async s=>{if(!s)throw new Error("Agent ID is required for logs polling");return this.refreshAgentLogs(s)},stopCondition:(s,n)=>{if(!n)return!0;if(!this._state.agentOverviews.find(a=>a.remote_agent_id===n))return this._state.logPollFailedCount++,this._state.logPollFailedCount>this._loggingMaxRetries&&(this._state.logPollFailedCount=0,!0);const i=this.state.agentLogs.get(n),r=i==null?void 0:i.steps.at(-1);return(r==null?void 0:r.step_description)==="Indexing"&&r.status===V.success}}),this._flagsModel.subscribe(s=>{const n=this._remoteAgentsClient.hasActiveOverviewsStream()||this._remoteAgentsClient.activeHistoryStreams.size>0||this._logsPollingManager.isPolling(),i=s.enableBackgroundAgents;i&&!n?this.startStateUpdates(this._pendingStateUpdateOpts):!i&&n&&this.stopStateUpdates()})}get state(){return this._state}startStateUpdates(e){var t,s;if(!this._flagsModel.enableBackgroundAgents)return this._pendingStateUpdateOpts={...this._pendingStateUpdateOpts,...e},void(e||(this._pendingStateUpdateOpts.overviews=!0));e?(e.overviews&&this.startOverviewsStream(),(t=e.conversation)!=null&&t.agentId&&this.startConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&(this._state.logPollFailedCount=0,this._logsPollingManager.start(e.logs.agentId))):this.startOverviewsStream()}_removePendingStateUpdates(e){var t,s,n,i;this._pendingStateUpdateOpts&&(e?(e.overviews&&(this._pendingStateUpdateOpts.overviews=!1),((t=e.conversation)==null?void 0:t.agentId)===((s=this._pendingStateUpdateOpts.conversation)==null?void 0:s.agentId)&&(this._pendingStateUpdateOpts.conversation=void 0),((n=e.logs)==null?void 0:n.agentId)===((i=this._pendingStateUpdateOpts.logs)==null?void 0:i.agentId)&&(this._pendingStateUpdateOpts.logs=void 0)):this._pendingStateUpdateOpts=void 0)}stopStateUpdates(e){var t,s;if(this._removePendingStateUpdates(e),!e)return this.stopOverviewsStream(),this._logsPollingManager.stop(),void this.stopAllConversationStreams();e.overviews&&this.stopOverviewsStream(),(t=e.conversation)!=null&&t.agentId&&this.stopConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&this._logsPollingManager.stop(e.logs.agentId)}async refreshCurrentAgent(e){this.startConversationStream(e)}async refreshAgentOverviews(){return this.startOverviewsStream(),this._state.agentOverviews}async refreshAgentLogs(e){try{const t=this.state.agentLogs.get(e);let s,n;const i=t==null?void 0:t.steps.at(-1);i?(s=i.step_number,n=i.step_number===0?0:i.sequence_id+1):(s=0,n=0);const r=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(e,s,n);if(!r.data.workspaceSetupStatus)return;const a=r.data.workspaceSetupStatus;if(a.steps.length===0)return t;const o=function(h,d){return{steps:[...h.steps,...d.steps].sort((u,p)=>u.step_number!==p.step_number?u.step_number-p.step_number:u.sequence_id-p.sequence_id)}}(t??{steps:[]},a),g={steps:o.steps.reduce((h,d)=>{const u=h[h.length-1];return u&&u.step_number===d.step_number?(u.status!==V.success&&(u.status=d.status),u.step_number===0?u.logs=d.logs:u.sequence_id<d.sequence_id&&(u.logs+=`
${d.logs}`,u.sequence_id=d.sequence_id)):h.push(d),h},[])};return this._state.agentLogs.set(e,g),this._state.logsError=void 0,this.notifySubscribers({type:"logs",agentId:e,data:g}),g}catch(t){const s=t instanceof Error?t.message:String(t);return this._state.logsError={errorMessage:s},this.notifySubscribers({type:"logs",agentId:e,data:this._state.agentLogs.get(e)||{steps:[]},error:this._state.logsError}),this._state.agentLogs.get(e)}}onStateUpdate(e){return this._stateUpdateSubscribers.add(e),e({type:"all",data:this._state}),()=>{this._stateUpdateSubscribers.delete(e)}}dispose(){this.stopStateUpdates(),this._stateUpdateSubscribers.clear()}notifySubscribers(e){this._stateUpdateSubscribers.forEach(t=>t(e))}async startConversationStream(e){this._remoteAgentsClient.hasActiveHistoryStream(e)&&this.stopConversationStream(e);const t=this._state.agentConversations.get(e)||[];let s=0;t.length>0&&(s=Math.max(...t.filter(n=>n.sequence_id!==void 0).map(n=>n.sequence_id||0))-1,s<0&&(s=0)),this._state.isConversationLoading=!0,this._state.conversationError=void 0,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError});try{const n=this._remoteAgentsClient.startRemoteAgentHistoryStreamWithRetry(e,s);(async()=>{var i;try{for await(const r of n){if(!this._remoteAgentsClient.hasActiveHistoryStream(e)||(i=this._remoteAgentsClient.getActiveHistoryStream(e))!=null&&i.isCancelled)break;this.processHistoryStreamUpdate(e,r)}}catch(r){if(this._remoteAgentsClient.hasActiveHistoryStream(e)){let a;r instanceof O?(a=`Failed to connect: ${r.message}`,console.error(`Stream retry exhausted for agent ${e}: ${r.message}`)):(a=r instanceof Error?r.message:String(r),console.error(`Stream error for agent ${e}: ${a}`)),this._state.conversationError={errorMessage:a},this._state.isConversationLoading=!1;const o=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:o,error:this._state.conversationError})}}finally{this._state.isConversationLoading=!1}})()}catch(n){let i;i=n instanceof O?`Failed to connect: ${n.message}`:n instanceof Error?n.message:String(n),this._state.conversationError={errorMessage:i},this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError})}}stopConversationStream(e){this._remoteAgentsClient.cancelRemoteAgentHistoryStream(e)}stopAllConversationStreams(){this._remoteAgentsClient.cancelAllRemoteAgentHistoryStreams()}processHistoryStreamUpdate(e,t){var s;if(ke(t)){this._state.conversationError=void 0;for(const n of t.updates){const i=this._state.agentConversations.get(e)||[];switch(n.type){case H.AGENT_HISTORY_EXCHANGE:if(n.exchange){const r=ze(i,[n.exchange]);this._state.agentConversations.set(e,r)}break;case H.AGENT_HISTORY_EXCHANGE_UPDATE:if(n.exchange_update){const r=n.exchange_update.sequence_id,a=i.findIndex(o=>o.sequence_id===r);if(a>=0){const o=i[a],g=((s=o.exchange)==null?void 0:s.response_text)||"";o.exchange.response_text=g+n.exchange_update.appended_text;const h=n.exchange_update.appended_nodes;if(h&&h.length>0){const u=o.exchange.response_nodes??[];o.exchange.response_nodes=[...u,...h]}const d=n.exchange_update.appended_changed_files;if(d&&d.length>0){const u=o.changed_files??[];o.changed_files=[...u,...d]}}}break;case H.AGENT_HISTORY_AGENT_STATUS:if(n.agent){const r=this._state.agentOverviews.findIndex(a=>a.remote_agent_id===e);r>=0?this._state.agentOverviews[r]=n.agent:(this._state.agentOverviews.push(n.agent),this._state.agentOverviews=C(this._state.agentOverviews)),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}}this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:this._state.agentConversations.get(e)||[],error:this._state.conversationError})}else{this.state.conversationError=t;const n=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:n,error:this._state.conversationError})}}async startOverviewsStream(){if(this._state.isOverviewsLoading=!0,this._state.overviewError=void 0,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError}),!this._remoteAgentsClient.hasActiveOverviewsStream())try{const e=this._remoteAgentsClient.startRemoteAgentsListStreamWithRetry(this._lastOverviewUpdateTimestamp);(async()=>{try{for await(const t of e){if(!this._remoteAgentsClient.hasActiveOverviewsStream())break;this.processOverviewsStreamUpdate(t)}}catch(t){if(this._remoteAgentsClient.hasActiveOverviewsStream()){let s;t instanceof O?(s=`Failed to connect: ${t.message}`,console.error(`Overview stream retry exhausted: ${t.message}`)):(s=t instanceof Error?t.message:String(t),console.error(`Overview stream error: ${s}`)),this._state.overviewError={errorMessage:s},this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}finally{this._state.isOverviewsLoading=!1}})()}catch(e){let t;t=e instanceof O?`Failed to connect: ${e.message}`:e instanceof Error?e.message:String(e),this._state.overviewError={errorMessage:t},this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}stopOverviewsStream(){this._remoteAgentsClient.cancelRemoteAgentOverviewsStream()}processOverviewsStreamUpdate(e){if(!He(e))return this._state.overviewError=e,void this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError});this._state.overviewError=void 0;for(const t of e.updates)switch(t.update_timestamp&&(this._lastOverviewUpdateTimestamp=t.update_timestamp),t.max_agents!==void 0&&(this._state.maxRemoteAgents=t.max_agents),t.max_active_agents!==void 0&&(this._state.maxActiveRemoteAgents=t.max_active_agents),t.type){case x.AGENT_LIST_ALL_AGENTS:t.all_agents&&(this._state.agentOverviews=C(t.all_agents));break;case x.AGENT_LIST_AGENT_ADDED:t.agent&&(this._state.agentOverviews.push(t.agent),this._state.agentOverviews=C(this._state.agentOverviews));break;case x.AGENT_LIST_AGENT_UPDATED:if(t.agent){const s=this._state.agentOverviews.findIndex(n=>n.remote_agent_id===t.agent.remote_agent_id);s>=0&&(this._state.agentOverviews[s]=t.agent,this._state.agentOverviews=C(this._state.agentOverviews))}break;case x.AGENT_LIST_AGENT_DELETED:t.deleted_agent_id&&(this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==t.deleted_agent_id))}this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:structuredClone(this._state.agentOverviews),error:this._state.overviewError})}}var w=(_=>(_.chatRequestFailed="chat_request_failed",_.messageTimeout="message_timeout",_.agentFailed="agent_failed",_))(w||{});const N="This agent is in a failed state and can no longer accept messages",ht=!1;class Q{constructor({msgBroker:e,isActive:t,flagsModel:s,host:n,gitRefModel:i,stateModel:r}){c(this,"_state",{isActive:!1,isPanelFocused:!1,currentAgentId:void 0,currentConversation:void 0,currentAgent:void 0,agentOverviews:[],chatConversations:[],localAgentConversations:[],isLoading:!1,isCurrentAgentDetailsLoading:!1,lastSuccessfulOverviewFetch:0,failedRefreshAttempts:0,maxRemoteAgents:0,maxActiveRemoteAgents:0,isDiffPanelOpen:!1,diffPanelAgentId:void 0,focusedFilePath:null,isCreatingAgent:!1,error:void 0,agentThreadsError:void 0,agentLogsError:void 0,agentChatHistoryError:void 0,remoteAgentCreationError:null,newAgentDraft:null,notificationSettings:{},pinnedAgents:{},setCurrentAgent:this.setCurrentAgent.bind(this),clearCurrentAgent:this.clearCurrentAgent.bind(this),sendMessage:this.sendMessage.bind(this),interruptAgent:this.interruptAgent.bind(this),createRemoteAgent:this.createRemoteAgent.bind(this),createRemoteAgentFromDraft:this.createRemoteAgentFromDraft.bind(this),deleteAgent:this.deleteAgent.bind(this),setNewAgentDraft:this.setNewAgentDraft.bind(this),setRemoteAgentCreationError:this.setRemoteAgentCreationError.bind(this),setGenerateSetupScriptSelected:this.setGenerateSetupScriptSelected.bind(this),hasFetchedOnce:!1,showRemoteAgentDiffPanel:this.showRemoteAgentDiffPanel.bind(this),closeRemoteAgentDiffPanel:this.closeRemoteAgentDiffPanel.bind(this),setIsCreatingAgent:this.setIsCreatingAgent.bind(this),toggleAgentPinned:this.toggleAgentPinned.bind(this),setPinnedAgents:this.setPinnedAgents.bind(this),pauseRemoteAgentWorkspace:this.pauseRemoteAgentWorkspace.bind(this),resumeRemoteAgentWorkspace:this.resumeRemoteAgentWorkspace.bind(this),isRemoteAgentSshWindow:!1,remoteAgentSshWindowId:void 0,generateSetupScriptSelected:!1});c(this,"_agentConversations",new Map);c(this,"_initialPrompts",new Map);c(this,"_agentSetupLogsCache",new Map);c(this,"_creationMetrics");c(this,"_preloadedDiffExplanations",new Map);c(this,"maxCacheEntries",10);c(this,"maxCacheSizeBytes",10485760);c(this,"_diffOpsModel");c(this,"subscribers",new Set);c(this,"agentSetupLogs");c(this,"_remoteAgentsClient");c(this,"_stateModel");c(this,"_extensionClient");c(this,"_flagsModel");c(this,"_gitRefModel");c(this,"_cachedUrls",new Map);c(this,"_pendingMessageTracking",new Map);c(this,"_agentSendMessageErrors",new Map);c(this,"sendMessageTimeoutMs",9e4);c(this,"_hasEverUsedRemoteAgent",le(void 0));c(this,"dispose",()=>{this._stateModel.dispose(),this._remoteAgentsClient.dispose(),this._agentConversations.clear(),this._agentSetupLogsCache.clear(),this._preloadedDiffExplanations.clear(),this._cachedUrls.clear(),this._pendingMessageTracking.forEach(e=>{e.forEach(t=>clearTimeout(t.timeout))}),this._pendingMessageTracking.clear(),this._agentSendMessageErrors.clear(),this.subscribers.clear()});c(this,"setHasEverUsedRemoteAgent",e=>{this._extensionClient.setHasEverUsedRemoteAgent(e),this._hasEverUsedRemoteAgent.set(e)});c(this,"refreshHasEverUsedRemoteAgent",async()=>{if(G(this.hasEverUsedRemoteAgent)!==void 0)return;const e=await this._extensionClient.checkHasEverUsedRemoteAgent();G(this.hasEverUsedRemoteAgent)===void 0&&this._hasEverUsedRemoteAgent.set(e)});c(this,"throttledGetDiffExplanation",Ue(async e=>await this._diffOpsModel.getDiffExplanation(e,void 0,6e4),1e3));this._state.isActive=t,this._flagsModel=s,this._gitRefModel=i,this._diffOpsModel=new We(e),this._remoteAgentsClient=new qe(e),this._extensionClient=new De(n,e,s),this._stateModel=r||new Je(this._flagsModel,this._remoteAgentsClient),this._stateModel.onStateUpdate(this.handleStateUpdate.bind(this)),this._flagsModel.subscribe(a=>{a.enableBackgroundAgents?this._stateModel.startStateUpdates():(this.setIsActive(!1),this._stateModel.stopStateUpdates())}),this.loadPinnedAgentsFromStore(),this.refreshHasEverUsedRemoteAgent(),this.checkRemoteAgentStatus()}async checkRemoteAgentStatus(){const e=await this._remoteAgentsClient.getRemoteAgentStatus();this._state.isRemoteAgentSshWindow=e.data.isRemoteAgentSshWindow,this._state.remoteAgentSshWindowId=e.data.remoteAgentId,this.notifySubscribers()}handleOverviewsUpdate(e){var r,a;const t=e.data,s=this._state.agentOverviews,n=t;if(this.isActive&&n.forEach(o=>{o.remote_agent_id===this._state.currentAgentId&&(o.has_updates=!1)}),this._state.currentAgentId&&(this._state.currentAgent=this._state.agentOverviews.find(o=>o.remote_agent_id===this._state.currentAgentId)),this._state.currentAgentId){const o=this._state.currentAgentId,g=o?(r=s.find(d=>d.remote_agent_id===o))==null?void 0:r.status:void 0,h=o?(a=n.find(d=>d.remote_agent_id===o))==null?void 0:a.status:void 0;if(h!==g)if(h===f.agentFailed){const d={type:w.agentFailed,errorMessage:N,canRetry:!1};this._agentSendMessageErrors.set(o,d)}else this._agentSendMessageErrors.delete(o)}this.maybeSendNotifications(n,s);const i=C(n);this._state.agentOverviews=i,this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.error,this._state.lastSuccessfulOverviewFetch=e.error?this._state.lastSuccessfulOverviewFetch:Date.now(),i.findIndex(o=>o.remote_agent_id===this._state.currentAgentId)===-1&&this.clearCurrentAgent()}handleConversationUpdate(e){var t;if(e.agentId===this._state.currentAgentId){const s={exchanges:e.data,lastFetched:new Date};this._agentConversations.set(e.agentId,s),this._state.currentConversation=s,this._state.agentChatHistoryError=e.error;const n=this._agentSendMessageErrors.get(e.agentId);if(n!=null&&n.failedExchangeId){const i=n.failedExchangeId;((t=this._state.currentConversation)==null?void 0:t.exchanges.some(a=>a.exchange.request_id===i))||this._agentSendMessageErrors.delete(e.agentId)}this._state.isCurrentAgentDetailsLoading=!1}}handleLogsUpdate(e){e.agentId===this._state.currentAgentId&&(this.agentSetupLogs=e.data,this._agentSetupLogsCache.set(e.agentId,e.data))}handleStateUpdate(e){switch(this._state.maxRemoteAgents=this._stateModel.state.maxRemoteAgents,this._state.maxActiveRemoteAgents=this._stateModel.state.maxActiveRemoteAgents,e.type){case"overviews":this.handleOverviewsUpdate(e);break;case"conversation":this.handleConversationUpdate(e);break;case"logs":this.handleLogsUpdate(e);break;case"all":this.handleOverviewsUpdate({type:"overviews",data:e.data.agentOverviews,error:e.data.overviewError}),e.data.agentConversations.forEach((t,s)=>{this._agentConversations.set(s,{exchanges:t,lastFetched:new Date})}),e.data.agentLogs.forEach((t,s)=>{t&&(this._agentSetupLogsCache.set(s,t),s===this._state.currentAgentId&&(this.agentSetupLogs=t))}),this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.data.overviewError,this._state.agentChatHistoryError=e.data.conversationError,this._state.agentLogsError=e.data.logsError}this.currentAgentId&&this.checkForHistoryErrors(this.currentAgentId),this.notifySubscribers()}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}showRemoteAgentDiffPanel(e){const t=this._state.currentAgentId;if(this.reportRemoteAgentEvent({eventName:b.diffPanel,remoteAgentId:t??"",eventData:{diffPanelData:{applied:!1,loadingTimeMs:0}}}),t&&e.changedFiles.length>0&&e.turnIdx===-1&&e.isShowingAggregateChanges){const s=`${t}-${this.generateChangedFilesHash(e.changedFiles)}`,n=this._preloadedDiffExplanations.get(s);if(n)return n.lastAccessed=Date.now(),this._preloadedDiffExplanations.set(s,n),this._remoteAgentsClient.showRemoteAgentDiffPanel({...e,preloadedExplanation:n.explanation,remoteAgentId:t}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,void this.notifySubscribers()}this._remoteAgentsClient.showRemoteAgentDiffPanel({...e,remoteAgentId:t}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,this.notifySubscribers()}closeRemoteAgentDiffPanel(){this._remoteAgentsClient.closeRemoteAgentDiffPanel(),this._state.isDiffPanelOpen=!1,this._state.diffPanelAgentId=void 0,this.notifySubscribers()}_getChatHistory(e){const t=this._agentConversations.get(e);if(!t)return[];const s=this.isAgentRunning(e);return t.exchanges.map(({exchange:n},i)=>{const r=n.request_id.startsWith("pending-");return{seen_state:Fe.seen,structured_request_nodes:n.request_nodes??[],status:r||i===t.exchanges.length-1&&s?X.sent:X.success,request_message:n.request_message,response_text:r?"":n.response_text,structured_output_nodes:n.response_nodes??[],request_id:n.request_id??`remote-agent-${i}`}})}getCurrentChatHistory(){const e=this.agentSetupLogs;return this.currentAgentId&&!e&&(this.agentSetupLogs={steps:[]},this._stateModel.startStateUpdates({logs:{agentId:this.currentAgentId}})),this._getChatHistory(this.currentAgentId??"")}getToolStates(){var a,o,g;const e=new Map,t=new Set,s=new Map;(a=this.currentConversation)==null||a.exchanges.forEach(h=>{var d,u;(d=h.exchange.response_nodes)==null||d.forEach(p=>{p.tool_use&&t.add(p.tool_use.tool_use_id)}),(u=h.exchange.request_nodes)==null||u.forEach(p=>{p.type===j.TOOL_RESULT&&p.tool_result_node&&s.set(p.tool_result_node.tool_use_id,p.tool_result_node)})});const n=(o=this.currentConversation)==null?void 0:o.exchanges[this.currentConversation.exchanges.length-1];let i=0,r=null;return(g=n==null?void 0:n.exchange.response_nodes)==null||g.forEach(h=>{var d;h.id>i&&(i=h.id,r=(d=h.tool_use)!=null&&d.tool_use_id?h.tool_use.tool_use_id:null)}),t.forEach(h=>{const d=s.get(h);if(d)e.set(h,{phase:d.is_error?S.error:S.completed,result:{isError:d.is_error,text:d.content},requestId:"",toolUseId:h});else{const u=this.isCurrentAgentRunning;h===r?e.set(h,{phase:u?S.running:S.cancelled,requestId:"",toolUseId:h}):e.set(h,{phase:S.cancelled,requestId:"",toolUseId:h})}}),e}getLastToolUseState(){const e=this.getToolStates(),t=[...e.keys()].pop();return e.get(t??"")??{phase:S.unknown}}getToolUseState(e){const t=e;return this.getToolStates().get(t)??{phase:S.completed,requestId:"",toolUseId:e}}async setCurrentAgent(e){if(this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=e,this._state.isCurrentAgentDetailsLoading=!!e,e&&this._agentSetupLogsCache.has(e)?this.agentSetupLogs=this._agentSetupLogsCache.get(e):this.agentSetupLogs=void 0,e&&this.checkForHistoryErrors(e),this.notifySubscribers(),e){this._stateModel.startStateUpdates({conversation:{agentId:e},logs:{agentId:e}});try{const t=this._state.agentOverviews.find(s=>s.remote_agent_id===e);!t||t.workspace_status!==q.workspacePaused&&t.workspace_status!==q.workspacePausing||await this._remoteAgentsClient.resumeHintRemoteAgent(e)}catch(t){console.warn("Failed to send resume hint to remote agent:",t)}this.preloadDiffExplanation(e)}}clearCurrentAgent(){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=void 0,this.agentSetupLogs=void 0,this.notifySubscribers()}async preloadDiffExplanation(e){const t=this._agentConversations.get(e);if(!t||t.exchanges.length===0)return;const s=Ne(t.exchanges);if(s.length===0)return;const n=`${e}-${this.generateChangedFilesHash(s)}`;if(this._preloadedDiffExplanations.get(n)||s.length>12)return;let i=0;if(s.forEach(r=>{var a,o;i+=(((a=r.old_contents)==null?void 0:a.length)||0)+(((o=r.new_contents)==null?void 0:o.length)||0)}),!(i>512e3))try{const r=await this.throttledGetDiffExplanation(s);if(r&&r.length>0){const a=this.generateChangedFilesHash(s),o=`${e}-${a}`;this._preloadedDiffExplanations.set(o,{explanation:r,changedFiles:s,userPrompt:this.getUserMessagePrecedingTurn(t.exchanges,0),timestamp:Date.now(),lastAccessed:Date.now(),changedFilesHash:a,turnIdx:-1}),this.manageCacheSize()}}catch(r){console.error("Failed to preload diff explanation:",r)}}async getDiffDescriptions(e,t){return this._diffOpsModel.getDescriptions(e,t)}getUserMessagePrecedingTurn(e,t){return e.length===0||t<0||t>=e.length?"":e[t].exchange.request_message||""}generateChangedFilesHash(e){const t=e.map(s=>{var n,i;return{oldPath:s.old_path,newPath:s.new_path,oldSize:((n=s.old_contents)==null?void 0:n.length)||0,newSize:((i=s.new_contents)==null?void 0:i.length)||0,oldHash:this.simpleHash(s.old_contents||""),newHash:this.simpleHash(s.new_contents||"")}});return this.simpleHash(JSON.stringify(t))}simpleHash(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t|=0;return t.toString(36)}manageCacheSize(){if(this._preloadedDiffExplanations.size<=this.maxCacheEntries)return;const e=Array.from(this._preloadedDiffExplanations.entries()).map(([s,n])=>({key:s,value:n,accessTime:n.lastAccessed||n.timestamp})).sort((s,n)=>s.accessTime-n.accessTime);let t=0;for(e.forEach(s=>{const n=JSON.stringify(s.value.explanation).length,i=s.value.changedFiles.reduce((r,a)=>{var o,g;return r+(((o=a.old_contents)==null?void 0:o.length)||0)+(((g=a.new_contents)==null?void 0:g.length)||0)},0);t+=n+i});e.length>0&&(e.length>this.maxCacheEntries||t>this.maxCacheSizeBytes);){const s=e.shift();if(s){this._preloadedDiffExplanations.delete(s.key);const n=JSON.stringify(s.value.explanation).length,i=s.value.changedFiles.reduce((r,a)=>{var o,g;return r+(((o=a.old_contents)==null?void 0:o.length)||0)+(((g=a.new_contents)==null?void 0:g.length)||0)},0);t-=n+i}}}async sendMessage(e,t){const s=this._state.currentAgentId;if(!s)return this._state.error="No active remote agent",this.notifySubscribers(),!1;const n=this._state.agentOverviews.find(r=>r.remote_agent_id===s);if((n==null?void 0:n.status)===f.agentFailed){const r={type:w.agentFailed,errorMessage:N,canRetry:!1};return this._agentSendMessageErrors.set(s,r),this.notifySubscribers(),!1}let i;this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const r=this._agentConversations.get(s)||{exchanges:[],lastFetched:new Date},a=(this.getfinalSequenceId(s)||0)+1;i="pending-"+Date.now();const o={exchange:{request_message:e,response_text:"",request_id:i,response_nodes:[],request_nodes:[]},changed_files:[],sequence_id:a};r.exchanges.push(o),this._agentConversations.set(s,r),this._state.currentConversation=r,this.notifySubscribers(),this.setupMessageTimeout(s,i);const g={request_nodes:[{id:1,type:j.TEXT,text_node:{content:e}}],model_id:t},h=await this._remoteAgentsClient.sendRemoteAgentChatRequest(s,g,this.sendMessageTimeoutMs);if(h.data.error)throw new Error(h.data.error);return this.clearMessageTimeout(s,i),this._state.currentAgentId&&setTimeout(()=>{this.preloadDiffExplanation(this._state.currentAgentId)},0),this.preloadDiffExplanation(s),!0}catch(r){i&&this.clearMessageTimeout(s,i);const a={type:w.chatRequestFailed,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${s}. ${r}`,canRetry:!0,failedExchangeId:i};return this._agentSendMessageErrors.set(s,a),console.error("Failed to send message:",r),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}setupMessageTimeout(e,t){const s=setTimeout(()=>{this.handleMessageTimeout(e,t)},this.sendMessageTimeoutMs);this._pendingMessageTracking.has(e)||this._pendingMessageTracking.set(e,new Map),this._pendingMessageTracking.get(e).set(t,{timeout:s,timestamp:Date.now()})}clearMessageTimeout(e,t){const s=this._pendingMessageTracking.get(e);if(s){const n=s.get(t);n&&(clearTimeout(n.timeout),s.delete(t),s.size===0&&this._pendingMessageTracking.delete(e))}}async handleMessageTimeout(e,t){const s=this._pendingMessageTracking.get(e);s&&(s.delete(t),s.size===0&&this._pendingMessageTracking.delete(e));const n=this._state.agentOverviews.find(a=>a.remote_agent_id===e);if((n==null?void 0:n.status)===f.agentRunning)return;const i=this._agentConversations.get(e);if(!i||!i.exchanges.find(a=>a.exchange.request_id===t))return;const r={type:w.messageTimeout,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${e}`,canRetry:!0,failedExchangeId:t};this._agentSendMessageErrors.set(e,r);try{await this._remoteAgentsClient.interruptRemoteAgent(e)}catch(a){console.error("Failed to interrupt agent after timeout:",a)}this.notifySubscribers()}removeOptimisticExchange(e,t){const s=this._agentConversations.get(e);s&&(s.exchanges=s.exchanges.filter(n=>n.exchange.request_id!==t),this._agentConversations.set(e,s),e===this._state.currentAgentId&&(this._state.currentConversation=s))}async retryFailedMessage(e,t){const s=this._agentConversations.get(e);if(!s)return!1;const n=s.exchanges.find(i=>i.exchange.request_id===t);return!!n&&(this.removeOptimisticExchange(e,t),this._agentSendMessageErrors.delete(e),this.notifySubscribers(),this.sendMessage(n.exchange.request_message))}async interruptAgent(){const e=this._state.currentAgentId;if(!e)return this._state.error="No active remote agent",void this.notifySubscribers();this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{await this._remoteAgentsClient.interruptRemoteAgent(e)}catch(t){this._state.error=t instanceof Error?t.message:String(t)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgent(e,t,s,n,i){var r;if(!e||!e.trim())return this._state.error="Cannot create a remote agent with an empty prompt",void this.notifySubscribers();this.agentSetupLogs=void 0,this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const a=await this._remoteAgentsClient.createRemoteAgent(e,t,s,n,i,this._creationMetrics);if(a.data.error)throw new Error(a.data.error);if(a.data.agentId&&a.data.success)return this._initialPrompts.set(a.data.agentId,e),await this.setNotificationEnabled(a.data.agentId,((r=this.newAgentDraft)==null?void 0:r.enableNotification)??!0),await this.setCurrentAgent(a.data.agentId),a.data.agentId;throw new Error("Failed to create remote agent: No agent ID returned")}catch(a){throw this._state.error=a instanceof Error?a.message:String(a),this.notifySubscribers(),a}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgentFromDraft(e,t){var i,r;if(this.setRemoteAgentCreationError(null),this.agentSetupLogs=void 0,!e||!e.trim())return void this.setRemoteAgentCreationError("Cannot create a remote agent with an empty prompt");const s=this._state.newAgentDraft;if(!s)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");if(s.isDisabled)return void this.setRemoteAgentCreationError("Cannot create agent with current workspace selection. Please resolve the issues with your workspace selection.");if(!s.commitRef||!s.selectedBranch)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");const n={starting_files:s.commitRef};this._state.isLoading=!0,this.notifySubscribers();try{const a=s.isSetupScriptAgent||((i=s.setupScript)==null?void 0:i.isGenerateOption)===!0;let o=a||(r=s.setupScript)==null?void 0:r.content;if(s.setupScript&&!a){const g=(await this.listSetupScripts()).find(h=>h.path===s.setupScript.path);g&&(o=g.content)}try{return await this.createRemoteAgent(e,n,o,a,t)}catch(g){let h="Failed to create remote agent. Please try again.";return g instanceof Error&&(g.message.includes("too large")||g.message.includes("413")?h="Repository or selected files are too large. Please select a smaller repository or branch.":g.message.includes("timeout")||g.message.includes("504")?h="Request timed out. The repository might be too large or the server is busy.":g.message.includes("rate limit")||g.message.includes("429")?h="Rate limit exceeded. Please try again later.":g.message.includes("unauthorized")||g.message.includes("401")?h="Authentication failed. Please check your GitHub credentials.":g.message.includes("not found")||g.message.includes("404")?h="Repository or branch not found. Please check your selection.":g.message.includes("bad request")||g.message.includes("400")?h="Invalid request. Please check your workspace setup and try again.":g.message.length>0&&(h=`Failed to create remote agent: ${g.message}`)),void this.setRemoteAgentCreationError(h)}}finally{this._state.isLoading=!1,this.notifySubscribers()}}async deleteAgent(e,t=!1){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{if(!await this._remoteAgentsClient.deleteRemoteAgent(e,t))return this._state.error="Failed to delete remote agent",this.notifySubscribers(),!1;this._agentConversations.delete(e),this._agentSetupLogsCache.delete(e),this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==e),this.removeNotificationEnabled(e),this._state.currentAgentId===e&&this.clearCurrentAgent()}catch(s){this._state.error=s instanceof Error?s.message:String(s)}finally{this._state.isLoading=!1,this.notifySubscribers()}return!this._state.error}async sshToRemoteAgent(e){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{return e.workspace_status!==q.workspaceRunning&&(await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e.remote_agent_id),await new Promise(t=>setTimeout(t,5e3))),await this._remoteAgentsClient.sshToRemoteAgent(e.remote_agent_id)}catch(t){return this._state.error=t instanceof Error?t.message:String(t),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async maybeSendNotifications(e,t){const s=new Map(t.map(i=>[i.remote_agent_id,i])),n=await this._remoteAgentsClient.getRemoteAgentNotificationEnabled(e.map(i=>i.remote_agent_id));e.forEach(i=>{const r=s.get(i.remote_agent_id),a=n[i.remote_agent_id],o=(r==null?void 0:r.status)===f.agentRunning,g=i.status===f.agentIdle||i.status===f.agentFailed,h=i.remote_agent_id!==this._state.currentAgentId,d=this._state.isPanelFocused;a&&o&&g&&(h||!d)&&this._remoteAgentsClient.notifyRemoteAgentReady(i)})}async setNotificationEnabled(e,t){await this._remoteAgentsClient.setRemoteAgentNotificationEnabled(e,t),this._state={...this._state,notificationSettings:{...this._state.notificationSettings,[e]:t}},this.notifySubscribers()}async removeNotificationEnabled(e){await this._remoteAgentsClient.deleteRemoteAgentNotificationEnabled(e);const{[e]:t,...s}=this._state.notificationSettings;this._state={...this._state,notificationSettings:s},this.notifySubscribers()}get hasFetchedOnce(){return this._state.hasFetchedOnce}get focusedFilePath(){return this._state.focusedFilePath}setFocusedFilePath(e){this._state.focusedFilePath=e,this.notifySubscribers()}handleMessageFromExtension(e){switch(e.data.type){case M.diffViewFileFocus:return this.setFocusedFilePath(e.data.data.filePath.replace(/^\/+/,"")),!0;case M.showRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!0,!0;case M.closeRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!1,!0;case M.remoteAgentStatusChanged:{const t=e.data;return this._state.isRemoteAgentSshWindow=t.data.isRemoteAgentSshWindow,this._state.remoteAgentSshWindowId=t.data.remoteAgentId,this.notifySubscribers(),!0}default:return!1}}get currentAgentId(){return this._state.currentAgentId}get currentConversation(){return this._agentConversations.get(this._state.currentAgentId??"")??void 0}_getAgentExchanges(e){var t;return((t=this._agentConversations.get(e))==null?void 0:t.exchanges)||[]}get currentExchanges(){const e=this._state.currentAgentId;return e?this._getAgentExchanges(e):[]}get currentStatus(){var t;const e=this._state.currentAgentId;return e&&((t=this._state.agentOverviews.find(s=>s.remote_agent_id===e))==null?void 0:t.status)||f.agentIdle}get currentAgent(){const e=this._state.currentAgentId;return e?this._state.agentOverviews.find(t=>t.remote_agent_id===e):void 0}get agentOverviews(){return this._state.agentOverviews}get isLoading(){return this._state.isLoading}get isCurrentAgentDetailsLoading(){return this._state.isCurrentAgentDetailsLoading}get lastSuccessfulOverviewFetch(){return this._state.lastSuccessfulOverviewFetch}get error(){return this._state.error}get agentThreadsError(){return this._state.agentThreadsError}get agentChatHistoryError(){return this._state.agentChatHistoryError}clearSendMessageError(){this._state.currentAgentId&&(this._agentSendMessageErrors.delete(this._state.currentAgentId),this.notifySubscribers())}get sendMessageError(){return this._agentSendMessageErrors.get(this._state.currentAgentId??"")??void 0}checkForHistoryErrors(e){var i;const t=this._getAgentExchanges(e);if(((i=this._state.agentOverviews.find(r=>r.remote_agent_id===e))==null?void 0:i.status)===f.agentFailed){const r={type:w.agentFailed,errorMessage:N,canRetry:!1};return this._agentSendMessageErrors.set(e,r),void this.notifySubscribers()}const s=t.length>0&&t[t.length-1].exchange.request_id.startsWith("pending-"),n=this._agentSendMessageErrors.get(e);if(s&&!n){const r=t[t.length-1].exchange.request_id,a=this._pendingMessageTracking.get(e),o=a==null?void 0:a.get(r);if(o&&Date.now()-o.timestamp>this.sendMessageTimeoutMs){const g={type:w.messageTimeout,errorMessage:`There was an error sending your message. Please try again. Agent ID: ${e}`,canRetry:!0,failedExchangeId:r};this._agentSendMessageErrors.set(e,g),this.clearMessageTimeout(e,r),this.notifySubscribers()}}}isAgentRunning(e){const t=this._state.agentOverviews.find(a=>a.remote_agent_id===e),s=!(!t||t.status!==f.agentRunning&&t.status!==f.agentStarting),n=this._getAgentExchanges(e),i=this._agentSendMessageErrors.get(e),r=n.length>0&&n[n.length-1].exchange.request_id.startsWith("pending-")&&!i;return s||r}get isCurrentAgentRunning(){return!!this._state.currentAgentId&&this.isAgentRunning(this._state.currentAgentId)}get maxRemoteAgents(){return this._state.maxRemoteAgents}get maxActiveRemoteAgents(){return this._state.maxActiveRemoteAgents}getInitialPrompt(e){return this._initialPrompts.get(e)}clearInitialPrompt(e){this._initialPrompts.delete(e)}get notificationSettings(){return this._state.notificationSettings}get pinnedAgents(){return this._state.pinnedAgents}getfinalSequenceId(e){var n;const t=this._agentConversations.get(e),s=t==null?void 0:t.exchanges;if(s)return((n=s[s.length-1])==null?void 0:n.sequence_id)??void 0}async listSetupScripts(){try{return(await this._remoteAgentsClient.listSetupScripts()).data.scripts}catch(e){return this._state.error=e instanceof Error?e.message:String(e),this.notifySubscribers(),[]}}async saveSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.saveSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to save setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}async deleteSetupScript(e,t){try{const s=await this._remoteAgentsClient.deleteSetupScript(e,t);return s.data.success||(this._state.error=s.data.error||"Failed to delete setup script",this.notifySubscribers()),s.data}catch(s){return this._state.error=s instanceof Error?s.message:String(s),this.notifySubscribers(),{success:!1,error:s instanceof Error?s.message:String(s)}}}async renameSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.renameSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to rename setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}get isActive(){return this._state.isActive}setIsActive(e){this._state.isActive=e;const t=this._state.currentAgentId,s={conversation:t?{agentId:t}:void 0,logs:t?{agentId:t}:void 0};e?this._stateModel.startStateUpdates(s):this._stateModel.stopStateUpdates(s),this.notifySubscribers()}get isPanelFocused(){return this._state.isPanelFocused}setIsPanelFocused(e){this._state.isPanelFocused=e,this.notifySubscribers()}optimisticallyClearAgentUpdates(e){var s;const t=this._state.agentOverviews.findIndex(n=>n.remote_agent_id===e);if(t!==-1&&this._state.agentOverviews[t].has_updates){const n=[...this._state.agentOverviews];n[t]={...n[t],has_updates:!1},this._state.agentOverviews=n,((s=this._state.currentAgent)==null?void 0:s.remote_agent_id)===e&&(this._state.currentAgent={...this._state.currentAgent,has_updates:!1}),this.notifySubscribers()}}async updateRemoteAgentTitle(e,t){var i,r,a;const s=this._state.agentOverviews.findIndex(o=>o.remote_agent_id===e);if(s===-1)return void console.warn(`Agent with ID ${e} not found in overviews`);const n=this._state.agentOverviews[s].title;try{const o=[...this._state.agentOverviews];o[s]={...o[s],title:t},this._state.agentOverviews=o,((i=this._state.currentAgent)==null?void 0:i.remote_agent_id)===e&&(this._state.currentAgent={...this._state.currentAgent,title:t}),this.notifySubscribers();const g=await this._remoteAgentsClient.updateRemoteAgentTitle(e,t);if(!g.data.success||!g.data.agent)throw console.error("Failed to update remote agent title:",g.data.error),this._state.error=g.data.error||"Failed to update remote agent title",new Error(this._state.error);{const h=[...this._state.agentOverviews];h[s]=g.data.agent,this._state.agentOverviews=h,((r=this._state.currentAgent)==null?void 0:r.remote_agent_id)===e&&(this._state.currentAgent=g.data.agent),this.notifySubscribers()}}catch(o){const g=[...this._state.agentOverviews];g[s]={...g[s],title:n},this._state.agentOverviews=g,((a=this._state.currentAgent)==null?void 0:a.remote_agent_id)===e&&(this._state.currentAgent={...this._state.currentAgent,title:n});const h=o instanceof Error?o.message:String(o);throw this._state.error=h,this.notifySubscribers(),o}}setRemoteAgentCreationError(e){this._state.remoteAgentCreationError=e,this.notifySubscribers()}get isDiffPanelOpen(){return this._state.isDiffPanelOpen}get diffPanelAgentId(){return this._state.diffPanelAgentId}get remoteAgentCreationError(){return this._state.remoteAgentCreationError}setNewAgentDraft(e){this._state.newAgentDraft=e,this.notifySubscribers()}get generateSetupScriptSelected(){return this._state.generateSetupScriptSelected}setGenerateSetupScriptSelected(e){this._state.generateSetupScriptSelected=e,this.notifySubscribers()}setCreationMetrics(e){this._creationMetrics=e}get creationMetrics(){return this._creationMetrics}refreshAgentChatHistory(e){this._stateModel.refreshCurrentAgent(e)}get newAgentDraft(){return this._state.newAgentDraft}setIsCreatingAgent(e){this._state.isCreatingAgent=e,this.notifySubscribers()}get isCreatingAgent(){return this._state.isCreatingAgent}get isRemoteAgentSshWindow(){return this._state.isRemoteAgentSshWindow}get remoteAgentSshWindowId(){return this._state.remoteAgentSshWindowId}async showRemoteAgentHomePanel(){await this._remoteAgentsClient.showRemoteAgentHomePanel()}async closeRemoteAgentHomePanel(){await this._remoteAgentsClient.closeRemoteAgentHomePanel()}async saveLastRemoteAgentSetup(e,t,s){try{await this._remoteAgentsClient.saveLastRemoteAgentSetup(e,t,s)}catch(n){console.error("Failed to save last remote agent setup:",n)}}async getLastRemoteAgentSetup(){try{return(await this._remoteAgentsClient.getLastRemoteAgentSetup()).data}catch(e){return console.error("Failed to get last remote agent setup:",e),{lastRemoteAgentGitRepoUrl:null,lastRemoteAgentGitBranch:null,lastRemoteAgentSetupScript:null}}}async loadPinnedAgentsFromStore(){try{const e=await this._remoteAgentsClient.getPinnedAgentsFromStore();this._state.pinnedAgents=e,this.notifySubscribers()}catch(e){console.error("Failed to load pinned agents from store:",e)}}async toggleAgentPinned(e,t){if(!e)return this._state.pinnedAgents;t=t??!1;try{if(this._state.pinnedAgents={...this._state.pinnedAgents,[e]:!t},t){const{[e]:s,...n}=this._state.pinnedAgents;this._state.pinnedAgents=n,await this._remoteAgentsClient.deletePinnedAgentFromStore(e)}else await this._remoteAgentsClient.savePinnedAgentToStore(e,!0);return this.notifySubscribers(),await this._remoteAgentsClient.getPinnedAgentsFromStore()}catch(s){return console.error("Failed to toggle pinned status for remote agent:",s),this._state.pinnedAgents}}async getConversationUrl(e){var o;const t=this._cachedUrls.get(e),s=this._agentConversations.get(e),n=(s==null?void 0:s.exchanges.length)??0;if(t&&s&&t[0]===n)return t[1];const i=this._getChatHistory(e).map(g=>({...g,request_id:g.request_id||"",request_message:g.request_message,response_text:g.response_text||""}));if(i.length===0)throw new Error("No chat history to share");const r=await this._extensionClient.saveChat(e,i,`Remote Agent ${e}`);if(!r.data)throw new Error("Failed to create URL");const a=(o=r.data)==null?void 0:o.url;return a&&this._cachedUrls.set(e,[n,a]),a}async refreshAgentThreads(){this._state.agentThreadsError=void 0,this._state.isLoading=!0,this.notifySubscribers();try{await this._stateModel.refreshAgentOverviews()}catch(e){console.error("Failed to refresh agent threads:",e)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async openDiffInBuffer(e,t,s){await this._remoteAgentsClient.openDiffInBuffer(e,t,s)}async getShouldShowSSHConfigPermissionPrompt(){return this._remoteAgentsClient.getShouldShowSSHConfigPermissionPrompt()}async setPermissionToWriteToSSHConfig(e){await this._remoteAgentsClient.setPermissionToWriteToSSHConfig(e)}async pauseRemoteAgentWorkspace(e){await this._remoteAgentsClient.pauseRemoteAgentWorkspace(e)}async resumeRemoteAgentWorkspace(e){await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e)}async reportRemoteAgentEvent(e){await this._remoteAgentsClient.reportRemoteAgentEvent(e)}getSourceControlType(){return Le.unknownSourceControl}async reportChatModeEvent(e,t){try{await this.reportRemoteAgentEvent({eventName:b.modeSelector,remoteAgentId:"",eventData:{modeSelectorData:{action:e,mode:t,sourceControl:this.getSourceControlType()}}})}catch(s){console.error("Failed to report chat mode event:",s)}}getCurrentRepoHash(){var t,s,n;const e=(n=(s=(t=this._state.newAgentDraft)==null?void 0:t.commitRef)==null?void 0:s.github_commit_ref)==null?void 0:n.repository_url;return e?this.simpleHash(e):""}getCurrentBranchHash(){var t,s;const e=(s=(t=this._state.newAgentDraft)==null?void 0:t.selectedBranch)==null?void 0:s.name;return e?this.simpleHash(e):""}getHasSetupScriptSelected(){var e;return!!((e=this._state.newAgentDraft)!=null&&e.setupScript)}async getGithubRepoInfo(){try{let e=0,t=!0,s=1;for(;t;){const{repos:n,hasNextPage:i,nextPage:r}=await this._gitRefModel.listUserRepos(s);e+=n.length,t=i,s=r}return{numReposAvailable:e,githubIntegrationEnabled:await this._gitRefModel.isGithubAuthenticated()}}catch{return{numReposAvailable:0,githubIntegrationEnabled:!1}}}async reportRemoteAgentSetupWindowEvent(e){try{const{numReposAvailable:t,githubIntegrationEnabled:s}=await this.getGithubRepoInfo(),n={action:e,repoHash:this.getCurrentRepoHash(),branchHash:this.getCurrentBranchHash(),hasSetupScriptSelected:this.getHasSetupScriptSelected(),githubIntegrationEnabled:s,numReposAvailable:t,sourceControl:this.getSourceControlType()};await this.reportRemoteAgentEvent({eventName:b.remoteAgentSetupWindow,remoteAgentId:"",eventData:{remoteAgentSetupWindowData:n}})}catch(t){console.error("Failed to report remote agent setup window event:",t)}}async reportRemoteAgentThreadListEvent(e,t,s){try{const n={action:e,numAgentsInList:t,sourceControl:this.getSourceControlType()};await this.reportRemoteAgentEvent({eventName:b.remoteAgentThreadList,remoteAgentId:s||"",eventData:{remoteAgentThreadListData:n}})}catch(n){console.error("Failed to report remote agent thread list event:",n)}}async reportRemoteAgentNewThreadButtonEvent(e,t,s){try{const n={action:e,threadType:t,sourceControl:this.getSourceControlType()};await this.reportRemoteAgentEvent({eventName:b.remoteAgentNewThreadButton,remoteAgentId:s||"",eventData:{remoteAgentNewThreadButtonData:n}})}catch(n){console.error("Failed to report remote agent new thread button event:",n)}}setPinnedAgents(e){this._state.pinnedAgents={...e},this.notifySubscribers()}get hasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}}c(Q,"key","remoteAgentsModel");var Ve=ue("<svg><!></svg>"),Xe=we("<span><!></span>");function ct(_,e){const t=Re(e);pe(e,!1);const[s,n]=xe(),i=()=>Me(se,"$remoteAgentsModel",s),r=U(),a=U(),o=U();let g=m(e,"path",24,()=>{}),h=m(e,"start",8,0),d=m(e,"stop",8,0),u=m(e,"codeSnippetToLocate",24,()=>{}),p=m(e,"size",8,0),K=m(e,"color",8,"neutral"),Z=m(e,"variant",8,"ghost-block"),Y=m(e,"stickyColor",8,!1),ee=m(e,"tooltip",24,()=>({neutral:"Open File In Editor",success:"Opening file..."})),W=m(e,"metric",24,()=>{}),te=m(e,"onOpenLocalFile",8,async function(l){var A,E;if((A=l==null?void 0:l.stopPropagation)==null||A.call(l),(E=l==null?void 0:l.preventDefault)==null||E.call(l),W()&&(v==null||v.extensionClient.reportWebviewClientEvent(W())),g())return await T(),"success"});const T=async()=>{const l=await(v==null?void 0:v.extensionClient.resolvePath({rootPath:"",relPath:g()})),A={repoRoot:(l==null?void 0:l.repoRoot)??"",pathName:(l==null?void 0:l.pathName)??"",range:{start:Math.max(h(),0),stop:Math.max(d(),0)}};return u()&&(A.snippet=u()),v==null?void 0:v.extensionClient.openFile(A)},v=Pe(),se=me(Q.key);L(()=>i(),()=>{var l;k(r,(l=i())==null?void 0:l.isRemoteAgentSshWindow)}),L(()=>i(),()=>{var l;k(a,!!((l=i())!=null&&l.isActive))}),L(()=>(y(a),y(r)),()=>{k(o,!y(a)||y(r))}),fe(),ve();var $=B(),ne=J($),ie=l=>{var A=Xe(),E=z(A);const ae=be(()=>ee()||void 0);Ge(E,{get defaultColor(){return K()},get stickyColor(){return Y()},get size(){return p()},get variant(){return Z()},get tooltip(){return y(ae)},stateVariant:{success:"soft"},onClick:te(),icon:ye(()=>!t.text),children:(P,oe)=>{var I=B(),F=J(I);Oe(F,e,"text",{},null),R(P,I)},$$slots:{default:!0,iconLeft:(P,oe)=>{(function(I,F){const ge=Ie(F,["children","$$slots","$$events","$$legacy"]);var D=Ve();_e(D,()=>({xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16","data-ds-icon":"fa",viewBox:"0 0 16 16",...ge}));var he=z(D);$e(he,()=>'<path fill-opacity=".01" d="M0 0h16v16H0z"/><path fill-opacity=".71" d="M9.125 2.563c0 .311.25.562.563.562h2.393L6.85 8.352a.562.562 0 0 0 .795.795l5.227-5.227.002 2.393c0 .311.25.562.563.562a.56.56 0 0 0 .562-.562v-3.75A.56.56 0 0 0 13.438 2h-3.75a.56.56 0 0 0-.563.563m-5.437.187C2.755 2.75 2 3.505 2 4.438v7.875C2 13.245 2.755 14 3.688 14h7.874c.933 0 1.688-.755 1.688-1.687v-3a.56.56 0 0 0-.562-.563.56.56 0 0 0-.563.563v3a.56.56 0 0 1-.562.562H3.686a.56.56 0 0 1-.562-.562V4.437a.56.56 0 0 1 .563-.562h3a.561.561 0 1 0 0-1.125z"/>',!0),R(I,D)})(P,{slot:"iconLeft"})}}}),Ce(()=>Ee(A,1,`c-open-file-button-container c-open-file-button__size--${p()??""}`,"svelte-pdfhuj")),R(l,A)};Ae(ne,l=>{y(o)&&l(ie)}),R(_,$),Te(e,"openFile",T);var re=Se({openFile:T});return n(),re}export{ct as O,Q as R,w as S,ht as a};
