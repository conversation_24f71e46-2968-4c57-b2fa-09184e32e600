{"name": "ragflow", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"level": "^10.0.0"}}, "node_modules/abstract-level": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/abstract-level/-/abstract-level-3.1.0.tgz", "integrity": "sha512-j2e+TsAxy7Ri+0h7dJqwasymgt0zHBWX4+nMk3XatyuqgHfdstBJ9wsMfbiGwE1O+QovRyPcVAqcViMYdyPaaw==", "license": "MIT", "dependencies": {"buffer": "^6.0.3", "is-buffer": "^2.0.5", "level-supports": "^6.2.0", "level-transcoder": "^1.0.1", "maybe-combine-errors": "^1.0.0", "module-error": "^1.0.1"}, "engines": {"node": ">=18"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmmirror.com/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/browser-level": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/browser-level/-/browser-level-3.0.0.tgz", "integrity": "sha512-kGXtLh29jMwqKaskz5xeDLtCtN1KBz/DbQSqmvH7QdJiyGRC7RAM8PPg6gvUiNMa+wVnaxS9eSmEtP/f5ajOVw==", "license": "MIT", "dependencies": {"abstract-level": "^3.1.0"}}, "node_modules/buffer": {"version": "6.0.3", "resolved": "https://registry.npmmirror.com/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/classic-level": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/classic-level/-/classic-level-3.0.0.tgz", "integrity": "sha512-yGy8j8LjPbN0Bh3+ygmyYvrmskVita92pD/zCoalfcC9XxZj6iDtZTAnz+ot7GG8p9KLTG+MZ84tSA4AhkgVZQ==", "hasInstallScript": true, "license": "MIT", "dependencies": {"abstract-level": "^3.1.0", "module-error": "^1.0.1", "napi-macros": "^2.2.2", "node-gyp-build": "^4.3.0"}, "engines": {"node": ">=18"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/is-buffer": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/is-buffer/-/is-buffer-2.0.5.tgz", "integrity": "sha512-i2R6zNFDwgEHJyQUtJEk0XFi1i0dPFn/oqjK3/vPCcDeJvW5NQ83V8QbicfF1SupOaB0h8ntgBC2YiE7dfyctQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/level": {"version": "10.0.0", "resolved": "https://registry.npmmirror.com/level/-/level-10.0.0.tgz", "integrity": "sha512-aZJvdfRr/f0VBbSRF5C81FHON47ZsC2TkGxbBezXpGGXAUEL/s6+GP73nnhAYRSCIqUNsmJjfeOF4lzRDKbUig==", "license": "MIT", "dependencies": {"abstract-level": "^3.1.0", "browser-level": "^3.0.0", "classic-level": "^3.0.0"}, "engines": {"node": ">=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/level"}}, "node_modules/level-supports": {"version": "6.2.0", "resolved": "https://registry.npmmirror.com/level-supports/-/level-supports-6.2.0.tgz", "integrity": "sha512-QNxVXP0IRnBmMsJIh+sb2kwNCYcKciQZJEt+L1hPCHrKNELllXhvrlClVHXBYZVT+a7aTSM6StgNXdAldoab3w==", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/level-transcoder": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/level-transcoder/-/level-transcoder-1.0.1.tgz", "integrity": "sha512-t7bFwFtsQeD8cl8NIoQ2iwxA0CL/9IFw7/9gAjOonH0PWTTiRfY7Hq+Ejbsxh86tXobDQ6IOiddjNYIfOBs06w==", "license": "MIT", "dependencies": {"buffer": "^6.0.3", "module-error": "^1.0.1"}, "engines": {"node": ">=12"}}, "node_modules/maybe-combine-errors": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/maybe-combine-errors/-/maybe-combine-errors-1.0.0.tgz", "integrity": "sha512-eefp6IduNPT6fVdwPp+1NgD0PML1NU5P6j1Mj5nz1nidX8/sWY7119WL8vTAHgqfsY74TzW0w1XPgdYEKkGZ5A==", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/module-error": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/module-error/-/module-error-1.0.2.tgz", "integrity": "sha512-0yuvsqSCv8LbaOKhnsQ/T5JhyFlCYLPXK3U2sgV10zoKQwzs/MyfuQUOZQ1V/6OCOJsK/TRgNVrPuPDqtdMFtA==", "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/napi-macros": {"version": "2.2.2", "resolved": "https://registry.npmmirror.com/napi-macros/-/napi-macros-2.2.2.tgz", "integrity": "sha512-hmEVtAGYzVQpCKdbQea4skABsdXW4RUh5t5mJ2zzqowJS2OyXZTU1KhDVFhx+NlWZ4ap9mqR9TcDO3LTTttd+g==", "license": "MIT"}, "node_modules/node-gyp-build": {"version": "4.8.4", "resolved": "https://registry.npmmirror.com/node-gyp-build/-/node-gyp-build-4.8.4.tgz", "integrity": "sha512-LA4ZjwlnUblHVgq0oBF3Jl/6h/Nvs5fzBLwdEF4nuxnFdsfajde4WfxtJr3CaiH+F6ewcIB/q4jQ4UzPyid+CQ==", "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}}}