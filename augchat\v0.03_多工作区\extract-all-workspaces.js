#!/usr/bin/env node

/**
 * 增强版Augment聊天记录导出工具
 * 搜索所有工作区的聊天记录
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 所有发现的工作区路径
const ALL_WORKSPACES = [
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\219eaf1da08a5e8387de19f31f58d75e\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\91a2d8828768bfdcc8646e574d310d75\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\2ba6bc3b1b2a1f3ba7a4a8ea1105cd57\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\ccef8695afe889a5115b76456b29a41b\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\fa39d3dd4cc9582898a169ca6ce47e57\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\f69c4dd7acbd82e5d292fd208beae2dd\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\dfb7adde504ea8bee353a40950c45a36\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\d936be34f07e8cf57dc708858dc61051\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\b6d890d3f556d2e6e839579e3049b903\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\93e3fcb5538492ab23bde2738d5b5943\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\841f87d0dcd2557c2c63ae1811040913\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\50d248fde9cb6028e63cc7a5222f9163\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\50c4287b8fb1a115aa9cc1c101639aba\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\43ef5d38dbe60a500811a7825f3e6e70\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\3f0eee39ea191a2cc88c4c6bbd97548d\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\396d6b9c01523ad7cabe8a7d664d7852\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\1c10c6d49ca5c5c837f4aedf23adbde2\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\0a1a719385069c57568eca409e126cc8\\Augment.vscode-augment\\augment-kv-store',
    'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\be0818f388a073cd7f2825038ea6bf6f\\Augment.vscode-augment\\augment-kv-store'
];

// 配置
const CONFIG = {
    outputDir: path.join(__dirname, 'AllWorkspacesConversations'),
    tempDir: path.join(__dirname, 'temp_all_workspaces'),
    searchKeywords: [
        '导出.*聊天记录',
        'convert.*markdown',
        'extract.*conversations',
        '预览.*markdown',
        '不适合预览',
        '一个一个对话',
        'open-conversations',
        'conversations_markdown'
    ]
};

// 工具函数
const utils = {
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`✅ 创建目录: ${dirPath}`);
        }
    },

    formatDate() {
        return new Date().toISOString().replace(/[:.]/g, '-').split('.')[0];
    },

    getWorkspaceId(workspacePath) {
        const match = workspacePath.match(/workspaceStorage[\\\/]([^\\\/]+)[\\\/]/);
        return match ? match[1] : 'unknown';
    },

    getFileStats(filePath) {
        try {
            const stats = fs.statSync(filePath);
            return {
                size: stats.size,
                modified: stats.mtime.toISOString(),
                sizeFormatted: (stats.size / 1024 / 1024).toFixed(2) + 'MB'
            };
        } catch {
            return null;
        }
    }
};

// 使用strings命令提取文本内容（同步版本）
function extractWithStrings(dbPath, workspaceId) {
    console.log(`🔍 [${workspaceId}] 使用strings命令提取文本内容...`);

    try {
        if (!fs.existsSync(dbPath)) {
            console.log(`⏭️  [${workspaceId}] 路径不存在，跳过`);
            return [];
        }

        const files = fs.readdirSync(dbPath);
        const conversations = [];

        for (const file of files) {
            if (file.endsWith('.log') || file.endsWith('.ldb')) {
                const filePath = path.join(dbPath, file);
                const fileStats = utils.getFileStats(filePath);

                console.log(`📄 [${workspaceId}] 处理文件: ${file} (${fileStats?.sizeFormatted || '未知大小'})`);

                try {
                    // 使用同步的execSync执行strings命令
                    const output = execSync(`wsl strings "${filePath}"`, {
                        encoding: 'utf8',
                        maxBuffer: 50 * 1024 * 1024 // 50MB buffer
                    });

                    const lines = output.split('\n');

                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i].trim();

                        // 检查是否包含我们感兴趣的关键词
                        const hasKeyword = CONFIG.searchKeywords.some(keyword => {
                            const regex = new RegExp(keyword, 'i');
                            return regex.test(line);
                        });

                        if (hasKeyword || line.includes('conversationId') || line.includes('request_message')) {
                            try {
                                // 尝试解析JSON
                                const jsonMatch = line.match(/\{.*\}/);
                                if (jsonMatch) {
                                    const parsed = JSON.parse(jsonMatch[0]);
                                    if (parsed.conversationId || parsed.uuid || parsed.request_message) {
                                        conversations.push({
                                            workspaceId: workspaceId,
                                            source: file,
                                            lineNumber: i + 1,
                                            data: parsed,
                                            rawLine: line.substring(0, 1000),
                                            hasKeyword: hasKeyword,
                                            fileStats: fileStats
                                        });

                                        if (hasKeyword) {
                                            console.log(`🎯 [${workspaceId}] 发现关键词匹配: ${parsed.conversationId || parsed.uuid || '未知ID'}`);
                                        }
                                    }
                                }
                            } catch (parseError) {
                                // 如果包含关键词但不是JSON，也保存
                                if (hasKeyword) {
                                    conversations.push({
                                        workspaceId: workspaceId,
                                        source: file,
                                        lineNumber: i + 1,
                                        data: { rawText: line },
                                        rawLine: line.substring(0, 1000),
                                        hasKeyword: true,
                                        fileStats: fileStats
                                    });
                                    console.log(`🎯 [${workspaceId}] 发现关键词文本: ${line.substring(0, 100)}...`);
                                }
                            }
                        }
                    }

                } catch (error) {
                    console.warn(`⚠️  [${workspaceId}] 处理文件 ${file} 时出错: ${error.message}`);
                }
            }
        }

        console.log(`📊 [${workspaceId}] 提取完成: ${conversations.length} 条记录`);
        return conversations;

    } catch (error) {
        console.error(`❌ [${workspaceId}] Strings提取失败: ${error.message}`);
        return [];
    }
}

// 主函数
async function main() {
    console.log('🚀 开始搜索所有工作区的聊天记录...');
    console.log(`📂 发现 ${ALL_WORKSPACES.length} 个工作区`);
    
    utils.ensureDir(CONFIG.outputDir);
    
    const allResults = {
        timestamp: new Date().toISOString(),
        searchKeywords: CONFIG.searchKeywords,
        workspaces: {},
        summary: {
            totalWorkspaces: ALL_WORKSPACES.length,
            processedWorkspaces: 0,
            totalConversations: 0,
            keywordMatches: 0
        }
    };
    
    // 处理每个工作区
    for (const workspacePath of ALL_WORKSPACES) {
        const workspaceId = utils.getWorkspaceId(workspacePath);
        console.log(`\n🔍 处理工作区: ${workspaceId}`);
        console.log(`📂 路径: ${workspacePath}`);
        
        try {
            const conversations = extractWithStrings(workspacePath, workspaceId);
            
            allResults.workspaces[workspaceId] = {
                path: workspacePath,
                conversations: conversations,
                keywordMatches: conversations.filter(c => c.hasKeyword).length,
                totalConversations: conversations.length,
                processed: true
            };
            
            allResults.summary.processedWorkspaces++;
            allResults.summary.totalConversations += conversations.length;
            allResults.summary.keywordMatches += conversations.filter(c => c.hasKeyword).length;
            
            if (conversations.filter(c => c.hasKeyword).length > 0) {
                console.log(`🎉 [${workspaceId}] 发现 ${conversations.filter(c => c.hasKeyword).length} 个关键词匹配！`);
            }
            
        } catch (error) {
            console.error(`❌ [${workspaceId}] 处理失败: ${error.message}`);
            allResults.workspaces[workspaceId] = {
                path: workspacePath,
                error: error.message,
                processed: false
            };
        }
    }
    
    // 保存结果
    const outputPath = path.join(CONFIG.outputDir, `all_workspaces_conversations_${utils.formatDate()}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2), 'utf8');
    
    console.log(`\n🎉 搜索完成！`);
    console.log(`📄 结果已保存到: ${outputPath}`);
    console.log(`\n📊 搜索摘要:`);
    console.log(`   - 总工作区数: ${allResults.summary.totalWorkspaces}`);
    console.log(`   - 已处理工作区: ${allResults.summary.processedWorkspaces}`);
    console.log(`   - 总对话记录: ${allResults.summary.totalConversations}`);
    console.log(`   - 关键词匹配: ${allResults.summary.keywordMatches}`);
    
    // 显示关键词匹配的工作区
    console.log(`\n🎯 包含关键词的工作区:`);
    for (const [workspaceId, data] of Object.entries(allResults.workspaces)) {
        if (data.keywordMatches > 0) {
            console.log(`   - ${workspaceId}: ${data.keywordMatches} 个匹配`);
        }
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { main };
