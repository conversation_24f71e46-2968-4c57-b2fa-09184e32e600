import{x as Ht,a8 as Jt,z as t,m as h,C as e,ak as g,F as Qt,G as S,I as Xt,K as l,o as s,J as $,O as j,a1 as m,b,L as Yt,N as Ge,al as He,am as kt,Q as lt,P as J,an as xt,_ as Je,ao as Zt,ap as ea}from"./legacy-YP6Kq8lu.js";import"./design-system-init-BkqeNcXX.js";import{h as ta,W as ee,e as nt,i as St,b as Qe}from"./host-BNehKqab.js";import{b as te,a as aa}from"./input-C2nR_fsN.js";import{s as Et}from"./event-modifiers-Bz4QCcZc.js";import{M as sa}from"./message-broker-DRrss2z_.js";import"./async-messaging-gS_K9w3p.js";var la=S('<div class="loading-state svelte-f2wma1"><div>Loading secrets...</div></div>'),na=S('<div class="error-state svelte-f2wma1"><div class="error-state-title svelte-f2wma1">Error Loading Secrets</div> <div class="error-state-description svelte-f2wma1"> </div></div>'),ia=S(`<div class="empty-state svelte-f2wma1"><div class="empty-state-title svelte-f2wma1">No Secrets Found</div> <div class="empty-state-description svelte-f2wma1">You haven't created any secrets yet. Secrets allow you to securely store sensitive
          information like API keys, passwords, and tokens that can be used by remote agents and
          other Augment features.</div></div>`),ra=S('<div class="secret-description svelte-f2wma1"> </div>'),ca=S('<div class="value-update-section svelte-f2wma1"><label class="svelte-f2wma1">New Value:</label> <div class="value-input-container svelte-f2wma1"><textarea class="secret-value-input svelte-f2wma1" placeholder="Enter new secret value" rows="3"></textarea> <div class="value-buttons svelte-f2wma1"><button class="update-button svelte-f2wma1" title="Update value">Update</button> <button class="cancel-button svelte-f2wma1" title="Cancel">Cancel</button></div></div></div>'),oa=S('<div class="tag-item svelte-f2wma1"><input type="text" class="tag-key-input svelte-f2wma1" placeholder="Tag key" title="Tag key"/> <span class="tag-separator svelte-f2wma1">:</span> <input type="text" class="tag-value-input svelte-f2wma1" placeholder="Tag value" title="Tag value"/> <button class="delete-tag-button svelte-f2wma1" title="Delete tag">×</button></div>'),da=S('<div class="tag-item add-tag-item svelte-f2wma1"><input type="text" class="tag-key-input svelte-f2wma1" placeholder="Tag key" title="Tag key"/> <span class="tag-separator svelte-f2wma1">:</span> <input type="text" class="tag-value-input svelte-f2wma1" placeholder="Tag value" title="Tag value"/> <button class="add-tag-confirm-button svelte-f2wma1" title="Add tag">✓</button> <button class="add-tag-cancel-button svelte-f2wma1" title="Cancel">×</button></div>'),va=S('<div class="no-tags svelte-f2wma1">No tags</div>'),ma=S('<button class="add-tag-button svelte-f2wma1" title="Add tag">Add Tag</button>'),ua=S('<div class="mount-path-container svelte-f2wma1"><input type="text" class="mount-path-input svelte-f2wma1" placeholder="Enter mount path (e.g., /tmp/secret)"/></div>'),fa=S('<div class="mount-submit-container svelte-f2wma1"><button class="mount-submit-button svelte-f2wma1" title="Apply mount settings"><!></button></div>'),pa=S('<div class="secret-details svelte-f2wma1"><div class="details-meta svelte-f2wma1"><span class="secret-created"> </span> <span class="secret-version"> </span></div> <div class="secret-tags svelte-f2wma1"><div class="tags-header svelte-f2wma1"><strong class="svelte-f2wma1">Tags:</strong></div> <div class="tags-container svelte-f2wma1"><!> <!> <!></div> <div class="tags-bottom-actions svelte-f2wma1"><!></div></div> <div class="secret-mount svelte-f2wma1"><div class="mount-header svelte-f2wma1"><strong class="svelte-f2wma1">Mount to Filesystem:</strong></div> <div class="mount-container svelte-f2wma1"><div class="mount-checkbox-container svelte-f2wma1"><input type="checkbox" class="mount-checkbox svelte-f2wma1"/> <label class="mount-checkbox-label svelte-f2wma1">Mount secret to filesystem</label></div> <!> <!></div></div></div>'),wa=S('<div class="secret-item svelte-f2wma1"><div class="secret-header svelte-f2wma1"><div class="secret-info svelte-f2wma1"><h3 class="secret-name svelte-f2wma1"> </h3> <!> <div class="secret-meta svelte-f2wma1"><span class="secret-updated"> </span> <span class="secret-size"> </span></div></div> <div class="secret-actions svelte-f2wma1"><button class="update-value-button svelte-f2wma1" title="Edit secret value">Edit</button> <button class="details-toggle svelte-f2wma1" title="Toggle details"> </button> <button class="delete-button svelte-f2wma1" title="Delete secret">Delete</button></div></div> <!> <!></div>'),ga=S('<div class="secrets-list svelte-f2wma1"></div>'),ba=S('<div class="error-message svelte-f2wma1"><strong>Error:</strong> </div>'),ha=S('<div class="tag-item svelte-f2wma1"><input type="text" class="tag-key-input svelte-f2wma1"/> <span class="tag-separator svelte-f2wma1">=</span> <input type="text" class="tag-value-input svelte-f2wma1"/> <button class="remove-tag-button svelte-f2wma1" title="Remove tag">×</button></div>'),ya=S('<div class="mount-path-container svelte-f2wma1"><input type="text" class="mount-path-input svelte-f2wma1" placeholder="Enter mount path (e.g., /tmp/secret)"/></div>'),ka=S('<div class="modal-overlay svelte-f2wma1" role="dialog" aria-modal="true" tabindex="-1"><div class="modal-dialog create-secret-dialog svelte-f2wma1" role="document"><div class="modal-header svelte-f2wma1"><h2 class="svelte-f2wma1">Create New Secret</h2> <button class="close-button svelte-f2wma1" title="Close">×</button></div> <div class="modal-body svelte-f2wma1"><!> <div class="form-group svelte-f2wma1"><label for="secret-name" class="svelte-f2wma1">Secret Name *</label> <input id="secret-name" type="text" placeholder="Enter secret name" class="svelte-f2wma1"/></div> <div class="form-group svelte-f2wma1"><label for="secret-value" class="svelte-f2wma1">Secret Value *</label> <textarea id="secret-value" placeholder="Enter secret value" rows="4" class="svelte-f2wma1"></textarea></div> <div class="form-group svelte-f2wma1"><label for="secret-description" class="svelte-f2wma1">Description</label> <input id="secret-description" type="text" placeholder="Optional description" class="svelte-f2wma1"/></div> <div class="form-group svelte-f2wma1"><label for="tags-section" class="svelte-f2wma1">Tags</label> <div id="tags-section" class="tags-section svelte-f2wma1"><!> <div class="add-tag-section svelte-f2wma1"><input type="text" placeholder="Tag key" class="tag-key-input svelte-f2wma1"/> <span class="tag-separator svelte-f2wma1">=</span> <input type="text" placeholder="Tag value" class="tag-value-input svelte-f2wma1"/> <button class="add-tag-button svelte-f2wma1" title="Add tag">+</button></div></div></div> <div class="form-group svelte-f2wma1"><label for="new-secret-mount" class="svelte-f2wma1">Mount to Filesystem</label> <div class="mount-container svelte-f2wma1"><div class="mount-checkbox-container svelte-f2wma1"><input type="checkbox" id="new-secret-mount" class="mount-checkbox svelte-f2wma1"/> <label for="new-secret-mount" class="mount-checkbox-label svelte-f2wma1">Mount secret to filesystem</label></div> <!></div></div></div> <div class="modal-footer svelte-f2wma1"><button class="cancel-button svelte-f2wma1">Cancel</button> <button class="create-button svelte-f2wma1"> </button></div></div></div>'),xa=S('<div class="modal-overlay svelte-f2wma1" role="dialog" aria-modal="true" tabindex="-1"><div class="modal-dialog svelte-f2wma1" role="document"><div class="modal-header svelte-f2wma1"><h3 class="svelte-f2wma1">Delete Secret</h3></div> <div class="modal-body svelte-f2wma1"><p class="svelte-f2wma1">Are you sure you want to delete the secret <strong> </strong>?</p> <p class="svelte-f2wma1">This action cannot be undone.</p></div> <div class="modal-footer svelte-f2wma1"><button class="cancel-button svelte-f2wma1">Cancel</button> <button class="confirm-delete-button svelte-f2wma1">Delete</button></div></div></div>'),Sa=S('<div class="secrets-panel svelte-f2wma1"><div class="secrets-header svelte-f2wma1"><h1 class="secrets-title svelte-f2wma1">Secrets Manager</h1> <div class="secrets-actions svelte-f2wma1"><button class="refresh-button svelte-f2wma1"><!></button> <button class="new-secret-button svelte-f2wma1">New Secret</button></div></div> <div class="secrets-content svelte-f2wma1"><!></div></div> <!> <!>',1);ea(function(Ft,De){Ht(De,!1);let f=h([]),he=h(!0),ye=h(null),E=h({}),Q=h({}),me=h(null),c=h({}),y=h({}),q=h(""),B=h(""),D=h({}),k=h({}),ae={},W={},se={},N=h({});function Tt(){e(f)&&e(f).length>0&&(e(f).forEach(a=>{if(!(a.name in e(D))){const i=ut(a.tags),o=!!i,p=i||"";t(D,{...e(D),[a.name]:o}),t(k,{...e(k),[a.name]:p}),ae={...ae,[a.name]:p},W={...W,[a.name]:p},se={...se,[a.name]:o},t(N,{...e(N),[a.name]:!1})}}),t(it,JSON.stringify({enabled:e(D),paths:e(k),originalPaths:ae,originalEnabled:se})))}let it=h(""),Xe=h(!1),re=h(""),le=h(""),ke=h(""),O=h({}),_=h(!1),xe=h(null),Se=h(!1),Ee=h("");const ue=new sa(ta);function rt(){t(he,!0),t(ye,null),ue.send({type:ee.listSecretsRequest,data:{}},1e4).then(a=>{var i;i=a.data,t(he,!1),t(f,i.secrets),e(f).forEach(o=>{e(E)[o.name]||g(E,e(E)[o.name]={showValueInput:!1,newValue:""}),e(c)[o.name]||g(c,e(c)[o.name]={tags:{...o.tags}}),e(y)[o.name]||g(y,e(y)[o.name]=!1),ft(o.name,o.tags)}),Tt(),e(f).length===0&&t(ye,null)}).catch(a=>{t(he,!1),a.name==="MessageTimeout"?t(ye,"Request timed out. Please try again."):t(ye,`Failed to load secrets: ${a.message||a}`),console.error("Failed to load secrets:",a)})}function Ie(){t(Xe,!1),t(re,""),t(le,""),t(ke,""),t(O,{}),t(_,!1),t(xe,null),t(q,""),t(B,""),t(Se,!1),t(Ee,"")}async function _t(){var a;if(e(re).trim()&&e(le).trim()){t(_,!0),t(xe,null);try{const i={...e(O)};e(Se)&&e(Ee).trim()&&(i["augment:mount_point"]=e(Ee).trim());const o=await ue.send({type:ee.createSecretRequest,data:{name:e(re).trim(),value:e(le),tags:i,description:e(ke).trim()}});if(o.type!==ee.createSecretResponse||!o.data.success)throw new Error(((a=o.data)==null?void 0:a.error)||"Failed to create secret");{const p={name:e(re).trim(),value:e(le),tags:i,description:e(ke).trim(),created_at:new Date().toISOString(),updated_at:o.data.updatedAt||new Date().toISOString(),version:o.data.version||"1",value_size_bytes:new TextEncoder().encode(e(le)).length};t(f,[p,...e(f)]),g(c,e(c)[p.name]={tags:{...p.tags}}),t(c,{...e(c)}),ft(p.name,p.tags),Ie()}}catch(i){console.error("Failed to create secret:",i),t(xe,i instanceof Error?i.message:"Failed to create secret")}finally{t(_,!1)}}}function ct(a,i){a.trim()&&(g(O,e(O)[a.trim()]=i),t(O,{...e(O)}))}function ot(a){delete e(O)[a],t(O,{...e(O)})}function dt(a){g(E,e(E)[a]={showValueInput:!1,newValue:""}),t(E,{...e(E)})}async function Ve(a){var i,o;try{const p=e(f).find(R=>R.name===a);if(!p)throw new Error("Secret not found");const I=((i=e(c)[a])==null?void 0:i.tags)||{},A=await ue.send({type:ee.updateSecretRequest,data:{name:a,value:"",tags:I,description:p.description,expectedVersion:p.version}});if(A.type!==ee.updateSecretResponse||!A.data.success)throw new Error(((o=A.data)==null?void 0:o.error)||"Failed to update secret tags");{const R=e(f).findIndex(ne=>ne.name===a);R!==-1&&(g(f,e(f)[R]={...e(f)[R],tags:I,updated_at:A.data.updatedAt||new Date().toISOString(),version:A.data.version||e(f)[R].version}),t(f,[...e(f)]))}}catch(p){console.error("Failed to update secret tags:",p)}}function Ye(){t(me,null)}function vt(a){try{return new Date(a).toLocaleString()}catch{return a}}function mt(a){const i={};for(const[o,p]of Object.entries(a))o.startsWith("augment:")||(i[o]=p);return i}function ut(a){return a["augment:mount_point"]||null}function ft(a,i){const o=ut(i),p=!!o,I=o||"";g(D,e(D)[a]=p),g(k,e(k)[a]=I),ae[a]=I,W[a]=I,se[a]=p,g(N,e(N)[a]=!1),t(D,{...e(D)}),t(k,{...e(k)}),ae={...ae},W={...W},se={...se},t(N,{...e(N)})}Jt(()=>(ue.send({type:ee.secretsHomePanelLoaded,data:{}}),rt(),()=>{ue.dispose()})),Qt();var pt=Sa(),wt=Xt(pt),gt=s(wt),Dt=l(s(gt),2),je=s(Dt),It=s(je),Vt=a=>{var i=Ge("Refreshing...");b(a,i)},$t=a=>{var i=Ge("Refresh");b(a,i)};$(It,a=>{e(he)?a(Vt):a($t,!1)});var Ct=l(je,2),Ot=l(gt,2),At=s(Ot),Rt=a=>{var i=la();b(a,i)},Mt=(a,i)=>{var o=I=>{var A=na(),R=l(s(A),2),ne=s(R);j(()=>J(ne,e(ye))),b(I,A)},p=(I,A)=>{var R=z=>{var ie=ia();b(z,ie)},ne=z=>{var ie=ga();nt(ie,5,()=>e(f),F=>F.name,(F,n)=>{var K=wa(),fe=s(K),$e=s(fe),qe=s($e),ze=s(qe),Le=l(qe,2),Ce=d=>{var x=ra(),X=s(x);j(()=>J(X,e(n).description)),b(d,x)};$(Le,d=>{e(n).description&&d(Ce)});var Oe=l(Le,2),Ae=s(Oe),Ze=s(Ae),et=l(Ae,2),Pe=s(et),Be=l($e,2),We=s(Be),Re=l(We,2),tt=s(Re),Me=l(Re,2),Fe=l(fe,2),at=d=>{var x=ca(),X=s(x),pe=l(X,2),Y=s(pe),ce=l(Y,2),we=s(ce),oe=l(we,2);j(()=>{Je(X,"for",`secret-value-${e(n).name??""}`),Je(Y,"id",`secret-value-${e(n).name??""}`)}),te(Y,()=>e(E)[e(n).name].newValue,V=>g(E,e(E)[e(n).name].newValue=V)),m("click",we,()=>async function(V){var Ne,Ke;const G=(Ne=e(E)[V])==null?void 0:Ne.newValue;if(G)try{const de=e(f).find(ve=>ve.name===V);if(!de)throw new Error("Secret not found");const ge=await ue.send({type:ee.updateSecretRequest,data:{name:V,value:G,tags:de.tags,description:de.description,expectedVersion:de.version}});if(ge.type!==ee.updateSecretResponse||!ge.data.success)throw new Error(((Ke=ge.data)==null?void 0:Ke.error)||"Failed to update secret");{const ve=e(f).findIndex(st=>st.name===V);ve!==-1&&(g(f,e(f)[ve]={...e(f)[ve],value:G,updated_at:ge.data.updatedAt||new Date().toISOString(),version:ge.data.version||e(f)[ve].version,value_size_bytes:new TextEncoder().encode(G).length}),t(f,[...e(f)])),dt(V)}}catch(de){console.error("Failed to update secret value:",de)}}(e(n).name)),m("click",oe,()=>dt(e(n).name)),b(d,x)};$(Fe,d=>{var x;(x=e(E)[e(n).name])!=null&&x.showValueInput&&d(at)});var u=l(Fe,2),U=d=>{var x=pa(),X=s(x),pe=s(X),Y=s(pe),ce=l(pe,2),we=s(ce),oe=l(X,2),V=l(s(oe),2),G=s(V);nt(G,1,()=>{var v;return Object.entries(mt(((v=e(c)[e(n).name])==null?void 0:v.tags)||{}))},St,(v,w)=>{var T=kt(()=>xt(e(w),2));let M=()=>e(T)[0];var H=oa(),be=s(H),r=l(be,4),L=l(r,2);j(()=>{He(be,M()),He(r,e(T)[1])}),m("input",be,C=>{const P=C.target;(function(Z,Te,_e){if(_e!==Te&&_e.trim()){const Gt=e(c)[Z].tags[Te];delete e(c)[Z].tags[Te],g(c,e(c)[Z].tags[_e.trim()]=Gt),t(c,{...e(c)}),Ve(Z)}})(e(n).name,M(),P?P.value:"")}),m("input",r,C=>{const P=C.target;var Z,Te,_e;Z=e(n).name,Te=M(),_e=P?P.value:"",g(c,e(c)[Z].tags[Te]=_e),t(c,{...e(c)}),Ve(Z)}),m("click",L,()=>{return C=e(n).name,P=M(),delete e(c)[C].tags[P],t(c,{...e(c)}),void Ve(C);var C,P}),b(v,H)});var Ne=l(G,2),Ke=v=>{var w=da(),T=s(w),M=l(T,4),H=l(M,2),be=l(H,2);te(T,()=>e(q),r=>t(q,r)),te(M,()=>e(B),r=>t(B,r)),m("click",H,()=>{return r=e(n).name,L=e(q),C=e(B),L.trim()&&(g(c,e(c)[r].tags[L.trim()]=C),t(c,{...e(c)}),Ve(r)),t(q,""),t(B,""),g(y,e(y)[r]=!1),void t(y,{...e(y)});var r,L,C}),m("click",be,()=>{return r=e(n).name,t(q,""),t(B,""),g(y,e(y)[r]=!1),void t(y,{...e(y)});var r}),b(v,w)};$(Ne,v=>{e(y)[e(n).name]&&v(Ke)});var de=l(Ne,2),ge=v=>{var w=va();b(v,w)};$(de,v=>{var w;Object.keys(mt(((w=e(c)[e(n).name])==null?void 0:w.tags)||{})).length!==0||e(y)[e(n).name]||v(ge)});var ve=l(V,2),st=s(ve),zt=v=>{var w=ma();m("click",w,()=>{return T=e(n).name,g(y,e(y)[T]=!0),void t(y,{...e(y)});var T}),b(v,w)};$(st,v=>{e(y)[e(n).name]||v(zt)});var Lt=l(oe,2),Pt=l(s(Lt),2),ht=s(Pt),Ue=s(ht),Bt=l(Ue,2),yt=l(ht,2),Wt=v=>{var w=ua(),T=s(w);te(T,()=>e(k)[e(n).name],M=>g(k,e(k)[e(n).name]=M)),b(v,w)};$(yt,v=>{e(D)[e(n).name]&&v(Wt)});var Kt=l(yt,2),Ut=v=>{var w=fa(),T=s(w),M=s(T),H=r=>{var L=Ge("⏳");b(r,L)},be=r=>{var L=Ge("✓");b(r,L)};$(M,r=>{e(N)[e(n).name]?r(H):r(be,!1)}),j(()=>T.disabled=e(N)[e(n).name]),m("click",T,()=>async function(r){var L;if(!e(N)[r]){t(N,{...e(N),[r]:!0});try{const C=e(f).find(Z=>Z.name===r);if(!C)throw new Error("Secret not found");e(c)[r]||g(c,e(c)[r]={tags:{...C.tags}});const P={...C.tags,...e(c)[r].tags};e(D)[r]&&((L=e(k)[r])!=null&&L.trim())?(P["augment:mount_point"]=e(k)[r].trim(),W[r]=e(k)[r].trim(),W={...W}):delete P["augment:mount_point"],g(c,e(c)[r].tags=P),t(c,{...e(c)}),await Ve(r),ae={...ae,[r]:e(k)[r]},se={...se,[r]:e(D)[r]}}catch(C){console.error("Failed to update mount settings:",C)}finally{t(N,{...e(N),[r]:!1})}}}(e(n).name)),b(v,w)};$(Kt,v=>{e(it)&&function(w){const T=e(k)[w]||"",M=ae[w]||"",H=e(D)[w]||!1;return H!==(se[w]||!1)||(H?T.trim()!==M.trim():!H&&M.trim()!=="")}(e(n).name)&&v(Ut)}),j(v=>{J(Y,`Created: ${v??""}`),J(we,`Version: ${e(n).version??""}`),Je(Ue,"id",`mount-${e(n).name??""}`),Zt(Ue,e(D)[e(n).name]||!1),Je(Bt,"for",`mount-${e(n).name??""}`)},[()=>vt(e(n).created_at)],lt),m("change",Ue,()=>function(v){var w;if(t(D,{...e(D),[v]:!e(D)[v]}),e(D)[v]){const T=W[v];T&&t(k,{...e(k),[v]:T})}else(w=e(k)[v])!=null&&w.trim()&&(W={...W,[v]:e(k)[v]}),t(k,{...e(k),[v]:""})}(e(n).name)),b(d,x)};$(u,d=>{e(Q)[e(n).name]&&d(U)}),j((d,x)=>{J(ze,e(n).name),J(Ze,`Updated: ${d??""}`),J(Pe,`Size: ${x??""}`),J(tt,(e(Q)[e(n).name]?"▼":"▶")+" Details")},[()=>vt(e(n).updated_at),()=>{return(d=e(n).value_size_bytes)<1024?`${d} B`:d<1048576?`${(d/1024).toFixed(1)} KB`:`${(d/1048576).toFixed(1)} MB`;var d}],lt),m("click",We,()=>{return d=e(n).name,g(E,e(E)[d]={showValueInput:!0,newValue:""}),void t(E,{...e(E)});var d}),m("click",Re,()=>{return d=e(n).name,g(Q,e(Q)[d]=!e(Q)[d]),void t(Q,{...e(Q)});var d}),m("click",Me,()=>{return d=e(n).name,void t(me,d);var d}),b(F,K)}),b(z,ie)};$(I,z=>{e(f).length===0?z(R):z(ne,!1)},A)};$(a,I=>{e(ye)?I(o):I(p,!1)},i)};$(At,a=>{e(he)?a(Rt):a(Mt,!1)});var bt=l(wt,2),Nt=a=>{var i=ka(),o=s(i),p=s(o),I=l(s(p),2),A=l(p,2),R=s(A),ne=u=>{var U=ba(),d=l(s(U));j(()=>J(d,` ${e(xe)??""}`)),b(u,U)};$(R,u=>{e(xe)&&u(ne)});var z=l(R,2),ie=l(s(z),2),F=l(z,2),n=l(s(F),2),K=l(F,2),fe=l(s(K),2),$e=l(K,2),qe=l(s($e),2),ze=s(qe);nt(ze,1,()=>Object.entries(e(O)),St,(u,U)=>{var d=kt(()=>xt(e(U),2));let x=()=>e(d)[0],X=()=>e(d)[1];var pe=ha(),Y=s(pe),ce=l(Y,4),we=l(ce,2);j(()=>{He(Y,x()),Y.disabled=e(_),He(ce,X()),ce.disabled=e(_),we.disabled=e(_)}),m("blur",Y,oe=>{const V=oe.target,G=V&&V.value?V.value.trim():"";G!==x()&&G&&(ot(x()),ct(G,X()))}),m("input",ce,oe=>{const V=oe.target;g(O,e(O)[x()]=V?V.value:""),t(O,{...e(O)})}),m("click",we,()=>ot(x())),b(u,pe)});var Le=l(ze,2),Ce=s(Le),Oe=l(Ce,4),Ae=l(Oe,2),Ze=l($e,2),et=l(s(Ze),2),Pe=s(et),Be=s(Pe),We=l(Pe,2),Re=u=>{var U=ya(),d=s(U);j(()=>d.disabled=e(_)),te(d,()=>e(Ee),x=>t(Ee,x)),b(u,U)};$(We,u=>{e(Se)&&u(Re)});var tt=l(A,2),Me=s(tt),Fe=l(Me,2),at=s(Fe);j((u,U)=>{ie.disabled=e(_),n.disabled=e(_),fe.disabled=e(_),Ce.disabled=e(_),Oe.disabled=e(_),Ae.disabled=u,Be.disabled=e(_),Me.disabled=e(_),Fe.disabled=U,J(at,e(_)?"Creating...":"Create Secret")},[()=>e(_)||!e(q).trim(),()=>e(_)||!e(re).trim()||!e(le).trim()],lt),m("click",I,Ie),te(ie,()=>e(re),u=>t(re,u)),te(n,()=>e(le),u=>t(le,u)),te(fe,()=>e(ke),u=>t(ke,u)),te(Ce,()=>e(q),u=>t(q,u)),te(Oe,()=>e(B),u=>t(B,u)),m("click",Ae,()=>{ct(e(q),e(B)),t(q,""),t(B,"")}),aa(Be,()=>e(Se),u=>t(Se,u)),m("click",Me,Ie),m("click",Fe,_t),m("click",o,Et(function(u){Qe.call(this,De,u)})),m("keydown",o,function(u){Qe.call(this,De,u)}),m("click",i,Ie),m("keydown",i,u=>u.key==="Escape"&&Ie()),b(a,i)};$(bt,a=>{e(Xe)&&a(Nt)});var jt=l(bt,2),qt=a=>{var i=xa(),o=s(i),p=l(s(o),2),I=s(p),A=l(s(I)),R=s(A),ne=l(p,2),z=s(ne),ie=l(z,2);j(()=>J(R,e(me))),m("click",z,Ye),m("click",ie,()=>e(me)&&async function(F){var n;try{const K=await ue.send({type:ee.deleteSecretRequest,data:{name:F}});if(K.type!==ee.deleteSecretResponse||!K.data.success)throw new Error(((n=K.data)==null?void 0:n.error)||"Failed to delete secret");t(f,e(f).filter(fe=>fe.name!==F)),delete e(E)[F],delete e(c)[F],delete e(y)[F],delete e(Q)[F],t(E,{...e(E)}),t(c,{...e(c)}),t(y,{...e(y)}),t(Q,{...e(Q)})}catch(K){console.error("Failed to delete secret:",K)}finally{t(me,null)}}(e(me))),m("click",o,Et(function(F){Qe.call(this,De,F)})),m("keydown",o,function(F){Qe.call(this,De,F)}),m("click",i,Ye),m("keydown",i,F=>F.key==="Escape"&&Ye()),b(a,i)};$(jt,a=>{e(me)&&a(qt)}),j(()=>je.disabled=e(he)),m("click",je,rt),m("click",Ct,function(){t(Xe,!0),t(re,""),t(le,""),t(ke,""),t(O,{}),t(_,!1),t(xe,null),t(q,""),t(B,""),t(Se,!1),t(Ee,"")}),b(Ft,pt),Yt()},{target:document.getElementById("app")});
