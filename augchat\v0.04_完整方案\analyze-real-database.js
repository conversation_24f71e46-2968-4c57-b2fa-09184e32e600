#!/usr/bin/env node

/**
 * 分析真实的Augment数据库
 * 找出插件实际使用的排序逻辑
 */

const fs = require('fs');
const path = require('path');
const { Level } = require('level');

// 分析真实数据库
async function analyzeRealDatabase() {
    console.log('🔍 分析真实的Augment数据库...\n');
    
    const dbPath = path.join(__dirname, '../219eaf1da08a5e8387de19f31f58d75e/Augment.vscode-augment/augment-kv-store');
    
    if (!fs.existsSync(dbPath)) {
        console.log('❌ 数据库路径不存在');
        return null;
    }
    
    console.log(`📂 数据库路径: ${dbPath}`);
    
    try {
        const db = new Level(dbPath, { valueEncoding: 'json' });
        
        console.log('📊 正在读取数据库...');
        
        // 收集所有键和数据
        const allData = [];
        const conversationData = new Map();
        const keyPatterns = new Map();
        
        for await (const [key, value] of db.iterator()) {
            allData.push({ key, value });
            
            // 分析键的模式
            const keyType = analyzeKeyType(key);
            if (!keyPatterns.has(keyType)) {
                keyPatterns.set(keyType, []);
            }
            keyPatterns.get(keyType).push(key);
            
            // 收集对话相关数据
            if (key.startsWith('exchange:')) {
                const parts = key.split(':');
                if (parts.length >= 3) {
                    const conversationId = parts[1];
                    const exchangeId = parts[2];
                    
                    if (!conversationData.has(conversationId)) {
                        conversationData.set(conversationId, []);
                    }
                    
                    conversationData.get(conversationId).push({
                        key: key,
                        exchangeId: exchangeId,
                        value: value,
                        keyIndex: allData.length - 1,
                        timestamp: value.timestamp || null,
                        request_message: (value.request_message || '').substring(0, 100),
                        response_text: (value.response_text || '').substring(0, 100)
                    });
                }
            }
        }
        
        await db.close();
        
        console.log(`📊 总键数: ${allData.length}`);
        console.log(`📊 对话数: ${conversationData.size}\n`);
        
        // 分析键的模式
        console.log('🔍 键模式分析:');
        console.log('================================');
        for (const [pattern, keys] of keyPatterns) {
            console.log(`${pattern}: ${keys.length} 个`);
            if (keys.length > 0 && keys.length <= 5) {
                keys.forEach(key => console.log(`   - ${key}`));
            } else if (keys.length > 5) {
                keys.slice(0, 3).forEach(key => console.log(`   - ${key}`));
                console.log(`   ... 还有 ${keys.length - 3} 个`);
            }
        }
        console.log('');
        
        // 分析目标对话
        const targetConversationId = 'b78bd351-4c2b-4891-9ba8-f1d7a869d74b';
        if (conversationData.has(targetConversationId)) {
            await analyzeTargetConversation(conversationData.get(targetConversationId));
        } else {
            console.log(`❌ 未找到目标对话: ${targetConversationId}`);
            
            // 显示所有可用的对话ID
            console.log('\n📋 可用的对话ID:');
            for (const [convId, messages] of conversationData) {
                console.log(`   - ${convId}: ${messages.length} 条消息`);
            }
            
            // 分析第一个对话作为示例
            if (conversationData.size > 0) {
                const firstConvId = conversationData.keys().next().value;
                console.log(`\n🔍 分析第一个对话作为示例: ${firstConvId.substring(0, 8)}...`);
                await analyzeTargetConversation(conversationData.get(firstConvId));
            }
        }
        
        return { allData, conversationData, keyPatterns };
        
    } catch (error) {
        console.log(`❌ 分析失败: ${error.message}`);
        console.log(`错误详情: ${error.stack}`);
        return null;
    }
}

// 分析键的类型
function analyzeKeyType(key) {
    if (key.startsWith('exchange:')) return 'exchange';
    if (key.startsWith('conversation:')) return 'conversation';
    if (key.includes('session')) return 'session';
    if (key.includes('config')) return 'config';
    if (key.includes('state')) return 'state';
    return 'other';
}

// 分析目标对话
async function analyzeTargetConversation(messages) {
    console.log('🎯 分析目标对话:');
    console.log('================================');
    
    console.log(`📊 消息总数: ${messages.length}`);
    
    if (messages.length === 0) {
        console.log('❌ 没有消息数据');
        return;
    }
    
    // 按数据库中的自然顺序排序（keyIndex）
    const naturalOrder = [...messages].sort((a, b) => a.keyIndex - b.keyIndex);
    
    console.log('\n🔍 数据库自然顺序分析:');
    console.log('前10条消息:');
    naturalOrder.slice(0, 10).forEach((msg, index) => {
        console.log(`${index + 1}. exchangeId: ${msg.exchangeId}`);
        console.log(`   键索引: ${msg.keyIndex}`);
        console.log(`   时间戳: ${msg.timestamp || 'null'}`);
        console.log(`   请求: ${msg.request_message}...`);
        console.log('');
    });
    
    console.log('最后5条消息:');
    naturalOrder.slice(-5).forEach((msg, index) => {
        const actualIndex = naturalOrder.length - 5 + index;
        console.log(`${actualIndex + 1}. exchangeId: ${msg.exchangeId}`);
        console.log(`   键索引: ${msg.keyIndex}`);
        console.log(`   时间戳: ${msg.timestamp || 'null'}`);
        console.log(`   请求: ${msg.request_message}...`);
        console.log('');
    });
    
    // 分析exchangeId模式
    console.log('🔍 ExchangeId模式分析:');
    const exchangeIds = messages.map(msg => msg.exchangeId);
    const patterns = analyzeExchangeIdPatterns(exchangeIds);
    
    console.log(`总数: ${exchangeIds.length}`);
    console.log(`纯数字: ${patterns.numeric.length} (${(patterns.numeric.length/exchangeIds.length*100).toFixed(1)}%)`);
    console.log(`UUID格式: ${patterns.uuid.length} (${(patterns.uuid.length/exchangeIds.length*100).toFixed(1)}%)`);
    console.log(`temp-fe格式: ${patterns.tempFe.length} (${(patterns.tempFe.length/exchangeIds.length*100).toFixed(1)}%)`);
    console.log(`其他格式: ${patterns.other.length} (${(patterns.other.length/exchangeIds.length*100).toFixed(1)}%)`);
    
    // 测试不同排序方法
    console.log('\n🧪 测试不同排序方法:');
    await testSortingMethods(messages);
    
    // 推断最佳排序
    console.log('\n💡 推断最佳排序方法:');
    const recommendation = inferBestSorting(messages, patterns);
    console.log(recommendation);
    
    return messages;
}

// 分析exchangeId模式
function analyzeExchangeIdPatterns(exchangeIds) {
    const patterns = {
        numeric: [],
        uuid: [],
        tempFe: [],
        other: []
    };
    
    for (const id of exchangeIds) {
        if (/^\d+$/.test(id)) {
            patterns.numeric.push(id);
        } else if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
            patterns.uuid.push(id);
        } else if (/^temp-fe-/.test(id)) {
            patterns.tempFe.push(id);
        } else {
            patterns.other.push(id);
        }
    }
    
    return patterns;
}

// 测试不同排序方法
async function testSortingMethods(messages) {
    // 1. 数据库自然顺序
    const naturalOrder = [...messages].sort((a, b) => a.keyIndex - b.keyIndex);
    console.log('1. 数据库自然顺序（前3个）:');
    naturalOrder.slice(0, 3).forEach((msg, index) => {
        console.log(`   ${index + 1}. ${msg.exchangeId} - ${msg.request_message.substring(0, 50)}...`);
    });
    
    // 2. 按exchangeId字符串排序
    const stringSort = [...messages].sort((a, b) => a.exchangeId.localeCompare(b.exchangeId));
    console.log('\n2. exchangeId字符串排序（前3个）:');
    stringSort.slice(0, 3).forEach((msg, index) => {
        console.log(`   ${index + 1}. ${msg.exchangeId} - ${msg.request_message.substring(0, 50)}...`);
    });
    
    // 3. 按数字排序（如果适用）
    const numericMessages = messages.filter(msg => /^\d+$/.test(msg.exchangeId));
    if (numericMessages.length > 0) {
        const numericSort = numericMessages.sort((a, b) => parseInt(a.exchangeId, 10) - parseInt(b.exchangeId, 10));
        console.log('\n3. 数字排序（前3个）:');
        numericSort.slice(0, 3).forEach((msg, index) => {
            console.log(`   ${index + 1}. ${msg.exchangeId} - ${msg.request_message.substring(0, 50)}...`);
        });
    }
    
    // 4. 按UUID十六进制排序
    const uuidMessages = messages.filter(msg => /^[0-9a-f]{8}-/.test(msg.exchangeId));
    if (uuidMessages.length > 0) {
        const uuidSort = uuidMessages.sort((a, b) => {
            const aHex = parseInt(a.exchangeId.split('-')[0], 16);
            const bHex = parseInt(b.exchangeId.split('-')[0], 16);
            return aHex - bHex;
        });
        console.log('\n4. UUID十六进制排序（前3个）:');
        uuidSort.slice(0, 3).forEach((msg, index) => {
            const hexValue = parseInt(msg.exchangeId.split('-')[0], 16);
            console.log(`   ${index + 1}. ${msg.exchangeId} (${hexValue}) - ${msg.request_message.substring(0, 50)}...`);
        });
    }
    
    // 5. 按时间戳排序（如果有）
    const timestampMessages = messages.filter(msg => msg.timestamp);
    if (timestampMessages.length > 0) {
        const timestampSort = timestampMessages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        console.log('\n5. 时间戳排序（前3个）:');
        timestampSort.slice(0, 3).forEach((msg, index) => {
            console.log(`   ${index + 1}. ${msg.timestamp} - ${msg.request_message.substring(0, 50)}...`);
        });
    }
}

// 推断最佳排序方法
function inferBestSorting(messages, patterns) {
    let recommendation = '';
    
    // 检查数据库顺序的连续性
    const keyIndices = messages.map(msg => msg.keyIndex).sort((a, b) => a - b);
    const isSequential = keyIndices.every((index, i) => i === 0 || index > keyIndices[i-1]);
    
    if (isSequential) {
        recommendation += '✅ 数据库自然顺序是连续的\n';
        recommendation += '   这很可能就是Augment插件使用的顺序！\n';
        recommendation += '   建议: 使用LevelDB iterator的自然顺序\n';
    } else {
        recommendation += '⚠️  数据库自然顺序不连续\n';
    }
    
    // 分析时间戳覆盖率
    const withTimestamp = messages.filter(msg => msg.timestamp);
    const timestampCoverage = withTimestamp.length / messages.length;
    
    if (timestampCoverage > 0.5) {
        recommendation += `\n📊 时间戳覆盖率: ${(timestampCoverage * 100).toFixed(1)}%\n`;
        recommendation += '   建议: 优先使用timestamp排序\n';
    } else {
        recommendation += `\n📊 时间戳覆盖率低: ${(timestampCoverage * 100).toFixed(1)}%\n`;
        recommendation += '   建议: 使用exchangeId排序\n';
    }
    
    // 分析exchangeId模式
    const totalMessages = messages.length;
    if (patterns.numeric.length > totalMessages * 0.8) {
        recommendation += '\n✅ 大部分exchangeId是数字\n';
        recommendation += '   建议: parseInt(exchangeId, 10)\n';
    } else if (patterns.uuid.length > totalMessages * 0.8) {
        recommendation += '\n✅ 大部分exchangeId是UUID\n';
        recommendation += '   建议: parseInt(exchangeId.split("-")[0], 16)\n';
    }
    
    return recommendation;
}

// 主函数
async function main() {
    console.log('🔧 真实Augment数据库分析工具');
    console.log('================================\n');
    
    try {
        const result = await analyzeRealDatabase();
        
        if (result) {
            console.log('\n🎯 关键发现:');
            console.log('================================');
            console.log('1. 成功分析了真实的Augment数据库');
            console.log('2. 发现了数据库中的实际键顺序');
            console.log('3. 测试了多种排序方法');
            console.log('4. 提供了基于真实数据的排序建议');
            console.log('\n💡 下一步: 根据这些发现修复我们的导出工具排序逻辑');
        } else {
            console.log('❌ 无法分析数据库');
        }
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
    }
}

// 运行分析
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { analyzeRealDatabase };
