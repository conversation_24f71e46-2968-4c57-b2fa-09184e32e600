#!/usr/bin/env node

/**
 * 时间戳问题诊断工具
 * 基于现有导出数据分析时间排序问题
 */

const fs = require('fs');
const path = require('path');

// 分析导出的Markdown文件中的时间戳问题
function analyzeExportedTimestamps() {
    console.log('🔍 分析导出文件中的时间戳问题...\n');
    
    // 查找最新的导出目录
    const currentDir = __dirname;
    const files = fs.readdirSync(currentDir);
    
    let latestExportDir = null;
    for (const file of files) {
        if (file.startsWith('conversations_export_') && fs.statSync(path.join(currentDir, file)).isDirectory()) {
            if (!latestExportDir || file > latestExportDir) {
                latestExportDir = file;
            }
        }
    }
    
    if (!latestExportDir) {
        console.log('❌ 未找到导出目录');
        return null;
    }
    
    console.log(`📂 分析目录: ${latestExportDir}`);
    
    // 查找目标对话文件
    const exportPath = path.join(currentDir, latestExportDir);
    const exportFiles = fs.readdirSync(exportPath);
    
    let targetFile = null;
    for (const file of exportFiles) {
        if (file.includes('b78bd351') && file.endsWith('.md')) {
            targetFile = file;
            break;
        }
    }
    
    if (!targetFile) {
        console.log('❌ 未找到目标对话文件');
        return null;
    }
    
    console.log(`📄 分析文件: ${targetFile}\n`);
    
    // 读取文件内容
    const filePath = path.join(exportPath, targetFile);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 提取所有消息的元数据
    const messageData = extractMessageMetadata(content);
    
    console.log(`📊 提取到 ${messageData.length} 条消息元数据\n`);
    
    // 分析时间戳分布
    analyzeTimestampDistribution(messageData);
    
    // 分析排序问题
    analyzeSortingIssues(messageData);
    
    // 分析exchangeId模式
    analyzeExchangeIdPatterns(messageData);
    
    return messageData;
}

// 提取消息元数据
function extractMessageMetadata(content) {
    const messageData = [];
    
    // 匹配消息块
    const messagePattern = /### 消息 (\d+)\n\n[\s\S]*?<details>\n<summary>📊 消息元数据<\/summary>\n\n([\s\S]*?)\n<\/details>/g;
    
    let match;
    while ((match = messagePattern.exec(content)) !== null) {
        const messageNumber = parseInt(match[1], 10);
        const metadataText = match[2];
        
        // 解析元数据
        const metadata = parseMetadata(metadataText);
        metadata.messageNumber = messageNumber;
        
        messageData.push(metadata);
    }
    
    return messageData;
}

// 解析元数据文本
function parseMetadata(metadataText) {
    const metadata = {};
    
    const patterns = {
        messageId: /- \*\*消息ID\*\*: `([^`]+)`/,
        dataSource: /- \*\*数据源\*\*: ([^\n]+)/,
        timestamp: /- \*\*时间戳\*\*: `([^`]+)`/,
        timestampValue: /- \*\*时间戳数值\*\*: `([^`]+)`/,
        exchangeId: /- \*\*交换ID\*\*: `([^`]+)`/,
        exchangeIdValue: /- \*\*交换ID数值\*\*: `([^`]+)`/,
        sortKey: /- \*\*排序键\*\*: `([^`]+)`/
    };
    
    for (const [key, pattern] of Object.entries(patterns)) {
        const match = metadataText.match(pattern);
        if (match) {
            metadata[key] = match[1];
        }
    }
    
    return metadata;
}

// 分析时间戳分布
function analyzeTimestampDistribution(messageData) {
    console.log('📊 时间戳分布分析:');
    console.log('================================');
    
    const withTimestamp = messageData.filter(msg => msg.timestamp);
    const withExchangeId = messageData.filter(msg => msg.exchangeId);
    const withSortKey = messageData.filter(msg => msg.sortKey && msg.sortKey !== '0');
    
    console.log(`总消息数: ${messageData.length}`);
    console.log(`有timestamp: ${withTimestamp.length} (${(withTimestamp.length/messageData.length*100).toFixed(1)}%)`);
    console.log(`有exchangeId: ${withExchangeId.length} (${(withExchangeId.length/messageData.length*100).toFixed(1)}%)`);
    console.log(`有效排序键: ${withSortKey.length} (${(withSortKey.length/messageData.length*100).toFixed(1)}%)`);
    
    // 数据源分布
    const sourceDistribution = {};
    messageData.forEach(msg => {
        const source = msg.dataSource || 'unknown';
        sourceDistribution[source] = (sourceDistribution[source] || 0) + 1;
    });
    
    console.log('\n📋 数据源分布:');
    for (const [source, count] of Object.entries(sourceDistribution)) {
        console.log(`   ${source}: ${count} (${(count/messageData.length*100).toFixed(1)}%)`);
    }
    
    // 时间戳格式分析
    if (withTimestamp.length > 0) {
        console.log('\n🕒 时间戳样本:');
        withTimestamp.slice(0, 5).forEach((msg, index) => {
            console.log(`   ${index + 1}. ${msg.timestamp} (消息${msg.messageNumber})`);
        });
    }
    
    console.log('');
}

// 分析排序问题
function analyzeSortingIssues(messageData) {
    console.log('🔍 排序问题分析:');
    console.log('================================');
    
    // 检查消息序号连续性
    const messageNumbers = messageData.map(msg => msg.messageNumber).sort((a, b) => a - b);
    let discontinuities = 0;
    
    for (let i = 1; i < messageNumbers.length; i++) {
        if (messageNumbers[i] !== messageNumbers[i-1] + 1) {
            discontinuities++;
        }
    }
    
    console.log(`消息序号连续性: ${discontinuities === 0 ? '✅ 连续' : `❌ ${discontinuities}个不连续`}`);
    
    // 检查排序键顺序
    const sortKeyIssues = [];
    for (let i = 1; i < messageData.length; i++) {
        const prevSortKey = parseFloat(messageData[i-1].sortKey) || 0;
        const currSortKey = parseFloat(messageData[i].sortKey) || 0;
        
        if (prevSortKey > currSortKey && currSortKey !== 0) {
            sortKeyIssues.push({
                position: i,
                prevMessage: messageData[i-1].messageNumber,
                currMessage: messageData[i].messageNumber,
                prevSortKey: prevSortKey,
                currSortKey: currSortKey
            });
        }
    }
    
    console.log(`排序键顺序: ${sortKeyIssues.length === 0 ? '✅ 正确' : `❌ ${sortKeyIssues.length}个问题`}`);
    
    if (sortKeyIssues.length > 0) {
        console.log('\n⚠️  排序键问题详情:');
        sortKeyIssues.slice(0, 5).forEach((issue, index) => {
            console.log(`   ${index + 1}. 消息${issue.prevMessage}(${issue.prevSortKey}) → 消息${issue.currMessage}(${issue.currSortKey})`);
        });
    }
    
    // 分析排序键为0的消息
    const zeroSortKeyMessages = messageData.filter(msg => msg.sortKey === '0');
    console.log(`\n排序键为0的消息: ${zeroSortKeyMessages.length} (${(zeroSortKeyMessages.length/messageData.length*100).toFixed(1)}%)`);
    
    if (zeroSortKeyMessages.length > 0) {
        console.log('   原因分析:');
        const zeroReasons = {};
        zeroSortKeyMessages.forEach(msg => {
            let reason = 'unknown';
            if (!msg.timestamp && !msg.exchangeId) {
                reason = 'no_time_info';
            } else if (!msg.timestamp && msg.exchangeId) {
                reason = 'exchangeId_parse_failed';
            } else if (msg.timestamp) {
                reason = 'timestamp_parse_failed';
            }
            zeroReasons[reason] = (zeroReasons[reason] || 0) + 1;
        });
        
        for (const [reason, count] of Object.entries(zeroReasons)) {
            console.log(`     ${reason}: ${count}`);
        }
    }
    
    console.log('');
}

// 分析exchangeId模式
function analyzeExchangeIdPatterns(messageData) {
    console.log('🔍 ExchangeId模式分析:');
    console.log('================================');
    
    const exchangeIds = messageData.filter(msg => msg.exchangeId).map(msg => msg.exchangeId);
    
    if (exchangeIds.length === 0) {
        console.log('❌ 没有找到exchangeId');
        return;
    }
    
    // 分析exchangeId格式
    const patterns = {
        uuid: 0,        // UUID格式
        numeric: 0,     // 纯数字
        mixed: 0,       // 混合格式
        other: 0        // 其他格式
    };
    
    const samples = {
        uuid: [],
        numeric: [],
        mixed: [],
        other: []
    };
    
    exchangeIds.forEach(id => {
        if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
            patterns.uuid++;
            if (samples.uuid.length < 3) samples.uuid.push(id);
        } else if (/^\d+$/.test(id)) {
            patterns.numeric++;
            if (samples.numeric.length < 3) samples.numeric.push(id);
        } else if (/^\d/.test(id)) {
            patterns.mixed++;
            if (samples.mixed.length < 3) samples.mixed.push(id);
        } else {
            patterns.other++;
            if (samples.other.length < 3) samples.other.push(id);
        }
    });
    
    console.log('格式分布:');
    for (const [pattern, count] of Object.entries(patterns)) {
        if (count > 0) {
            console.log(`   ${pattern}: ${count} (${(count/exchangeIds.length*100).toFixed(1)}%)`);
            if (samples[pattern].length > 0) {
                console.log(`     样本: ${samples[pattern].join(', ')}`);
            }
        }
    }
    
    // 分析UUID解析问题
    if (patterns.uuid > 0) {
        console.log('\n🔍 UUID解析分析:');
        const uuidSamples = exchangeIds.filter(id => /^[0-9a-f]{8}-/i.test(id)).slice(0, 5);
        
        uuidSamples.forEach((id, index) => {
            const firstPart = id.split('-')[0];
            const hexValue = parseInt(firstPart, 16);
            console.log(`   ${index + 1}. ${id} → ${firstPart} → ${hexValue}`);
        });
    }
    
    console.log('');
}

// 提供修复建议
function provideSolutions(messageData) {
    console.log('💡 修复建议:');
    console.log('================================');
    
    const withTimestamp = messageData.filter(msg => msg.timestamp);
    const zeroSortKey = messageData.filter(msg => msg.sortKey === '0');
    
    if (withTimestamp.length / messageData.length > 0.5) {
        console.log('✅ 建议1: 优先使用timestamp排序');
        console.log('   - 超过50%的消息有timestamp');
        console.log('   - 应该将timestamp作为主要排序依据');
        console.log('   - 对于没有timestamp的消息，使用exchangeId作为备选');
    } else {
        console.log('⚠️  建议1: 改进数据提取');
        console.log('   - timestamp覆盖率低，需要寻找其他时间信息源');
        console.log('   - 检查原始数据是否包含更多时间信息');
    }
    
    if (zeroSortKey.length > messageData.length * 0.3) {
        console.log('\n❌ 建议2: 修复排序键解析');
        console.log('   - 超过30%的消息排序键为0');
        console.log('   - 需要改进exchangeId解析逻辑');
        console.log('   - 考虑使用消息在文件中的位置作为备选排序依据');
    }
    
    // 检查是否有明显的时间跳跃
    const timestampMessages = messageData.filter(msg => msg.timestamp).sort((a, b) => a.messageNumber - b.messageNumber);
    if (timestampMessages.length > 1) {
        const timeJumps = [];
        for (let i = 1; i < timestampMessages.length; i++) {
            const prevTime = new Date(timestampMessages[i-1].timestamp).getTime();
            const currTime = new Date(timestampMessages[i].timestamp).getTime();
            
            if (prevTime > currTime) {
                timeJumps.push({
                    from: timestampMessages[i-1].messageNumber,
                    to: timestampMessages[i].messageNumber,
                    timeDiff: prevTime - currTime
                });
            }
        }
        
        if (timeJumps.length > 0) {
            console.log('\n⚠️  建议3: 修复时间跳跃');
            console.log(`   - 发现${timeJumps.length}个时间倒退的情况`);
            console.log('   - 可能是数据合并时的排序问题');
            console.log('   - 建议在合并前对每个数据源单独排序');
        }
    }
    
    console.log('\n🔧 具体修复步骤:');
    console.log('1. 改进排序算法，优先使用timestamp');
    console.log('2. 修复exchangeId解析，正确处理UUID格式');
    console.log('3. 添加数据源内部排序，避免合并时的顺序问题');
    console.log('4. 为没有时间信息的消息使用位置索引作为排序依据');
}

// 主函数
function main() {
    console.log('🔧 时间戳问题诊断工具');
    console.log('================================\n');
    
    try {
        const messageData = analyzeExportedTimestamps();
        
        if (messageData && messageData.length > 0) {
            provideSolutions(messageData);
        } else {
            console.log('❌ 无法分析数据，请确保已运行导出工具');
        }
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
        console.error(error.stack);
    }
}

// 运行诊断
if (require.main === module) {
    main();
}

module.exports = { analyzeExportedTimestamps, provideSolutions };
