var to=Object.defineProperty;var eo=(e,t,n)=>t in e?to(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var M=(e,t,n)=>eo(e,typeof t!="symbol"?t+"":t,n);import{aR as no,aS as oo,S as Tn,w as _n,ar as ro,x as te,y as jt,D as Ie,F as ee,M as me,I as zt,b as ct,L as ne,B as dt,R as Cn,G as Nt,a0 as ge,O as ye,Y as An,o as Zt,a1 as Y,aK as io,T as ao,a9 as Ce,Q as Se,_ as Je,J as ke,C as st,m as ve,K as so,z as he,u as Ze,Z as co,N as uo,P as po,a as Ge}from"./legacy-YP6Kq8lu.js";import{c as pe,g as fo,p as D,a as Ct,e as Qe,b as lo,s as vo,i as ho,T as mo,l as tn,d as go}from"./SpinnerAugment-Dpcl1cXc.js";import{a as Dn}from"./IconButtonAugment-CbpcmeFk.js";import{b as ot}from"./host-BNehKqab.js";import{s as en}from"./event-modifiers-Bz4QCcZc.js";function be(e,t,n){var o=no(e,t);o&&o.set&&(e[t]=n,oo(()=>{e[t]=null}))}var nn=NaN,yo="[object Symbol]",bo=/^\s+|\s+$/g,xo=/^[-+]0x[0-9a-f]+$/i,wo=/^0b[01]+$/i,Eo=/^0o[0-7]+$/i,Oo=parseInt,To=typeof pe=="object"&&pe&&pe.Object===Object&&pe,_o=typeof self=="object"&&self&&self.Object===Object&&self,Co=To||_o||Function("return this")(),Ao=Object.prototype.toString,Do=Math.max,Lo=Math.min,Ae=function(){return Co.Date.now()};function Pe(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function on(e){if(typeof e=="number")return e;if(function(o){return typeof o=="symbol"||function(s){return!!s&&typeof s=="object"}(o)&&Ao.call(o)==yo}(e))return nn;if(Pe(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Pe(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(bo,"");var n=wo.test(e);return n||Eo.test(e)?Oo(e.slice(2),n?2:8):xo.test(e)?nn:+e}const rn=fo(function(e,t,n){var o,s,i,a,c,p,v=0,d=!1,l=!1,O=!0;if(typeof e!="function")throw new TypeError("Expected a function");function b(h){var r=o,m=s;return o=s=void 0,v=h,a=e.apply(m,r)}function E(h){var r=h-p;return p===void 0||r>=t||r<0||l&&h-v>=i}function x(){var h=Ae();if(E(h))return g(h);c=setTimeout(x,function(r){var m=t-(r-p);return l?Lo(m,i-(r-v)):m}(h))}function g(h){return c=void 0,O&&o?b(h):(o=s=void 0,a)}function C(){var h=Ae(),r=E(h);if(o=arguments,s=this,p=h,r){if(c===void 0)return function(m){return v=m,c=setTimeout(x,t),d?b(m):a}(p);if(l)return c=setTimeout(x,t),b(p)}return c===void 0&&(c=setTimeout(x,t)),a}return t=on(t)||0,Pe(n)&&(d=!!n.leading,i=(l="maxWait"in n)?Do(on(n.maxWait)||0,t):i,O="trailing"in n?!!n.trailing:O),C.cancel=function(){c!==void 0&&clearTimeout(c),v=0,o=p=s=c=void 0},C.flush=function(){return c===void 0?a:g(Ae())},C});var X="top",it="bottom",at="right",J="left",Re="auto",oe=[X,it,at,J],Ht="start",Gt="end",jo="clippingParents",Ln="viewport",It="popper",Ho="reference",an=oe.reduce(function(e,t){return e.concat([t+"-"+Ht,t+"-"+Gt])},[]),jn=[].concat(oe,[Re]).reduce(function(e,t){return e.concat([t,t+"-"+Ht,t+"-"+Gt])},[]),Mo=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function mt(e){return e?(e.nodeName||"").toLowerCase():null}function G(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function At(e){return e instanceof G(e).Element||e instanceof Element}function rt(e){return e instanceof G(e).HTMLElement||e instanceof HTMLElement}function Ue(e){return typeof ShadowRoot<"u"&&(e instanceof G(e).ShadowRoot||e instanceof ShadowRoot)}const Hn={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},s=t.attributes[n]||{},i=t.elements[n];rt(i)&&mt(i)&&(Object.assign(i.style,o),Object.keys(s).forEach(function(a){var c=s[a];c===!1?i.removeAttribute(a):i.setAttribute(a,c===!0?"":c)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var s=t.elements[o],i=t.attributes[o]||{},a=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]).reduce(function(c,p){return c[p]="",c},{});rt(s)&&mt(s)&&(Object.assign(s.style,a),Object.keys(i).forEach(function(c){s.removeAttribute(c)}))})}},requires:["computeStyles"]};function ht(e){return e.split("-")[0]}var _t=Math.max,xe=Math.min,Mt=Math.round;function Ne(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Mn(){return!/^((?!chrome|android).)*safari/i.test(Ne())}function St(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var o=e.getBoundingClientRect(),s=1,i=1;t&&rt(e)&&(s=e.offsetWidth>0&&Mt(o.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Mt(o.height)/e.offsetHeight||1);var a=(At(e)?G(e):window).visualViewport,c=!Mn()&&n,p=(o.left+(c&&a?a.offsetLeft:0))/s,v=(o.top+(c&&a?a.offsetTop:0))/i,d=o.width/s,l=o.height/i;return{width:d,height:l,top:v,right:p+d,bottom:v+l,left:p,x:p,y:v}}function Fe(e){var t=St(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Sn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ue(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function bt(e){return G(e).getComputedStyle(e)}function So(e){return["table","td","th"].indexOf(mt(e))>=0}function Et(e){return((At(e)?e.ownerDocument:e.document)||window.document).documentElement}function Te(e){return mt(e)==="html"?e:e.assignedSlot||e.parentNode||(Ue(e)?e.host:null)||Et(e)}function sn(e){return rt(e)&&bt(e).position!=="fixed"?e.offsetParent:null}function re(e){for(var t=G(e),n=sn(e);n&&So(n)&&bt(n).position==="static";)n=sn(n);return n&&(mt(n)==="html"||mt(n)==="body"&&bt(n).position==="static")?t:n||function(o){var s=/firefox/i.test(Ne());if(/Trident/i.test(Ne())&&rt(o)&&bt(o).position==="fixed")return null;var i=Te(o);for(Ue(i)&&(i=i.host);rt(i)&&["html","body"].indexOf(mt(i))<0;){var a=bt(i);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||s&&a.willChange==="filter"||s&&a.filter&&a.filter!=="none")return i;i=i.parentNode}return null}(e)||t}function ze(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ut(e,t,n){return _t(e,xe(t,n))}function kn(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Pn(e,t){return t.reduce(function(n,o){return n[o]=e,n},{})}const ko={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,s=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,c=ht(n.placement),p=ze(c),v=[J,at].indexOf(c)>=0?"height":"width";if(i&&a){var d=function(H,j){return kn(typeof(H=typeof H=="function"?H(Object.assign({},j.rects,{placement:j.placement})):H)!="number"?H:Pn(H,oe))}(s.padding,n),l=Fe(i),O=p==="y"?X:J,b=p==="y"?it:at,E=n.rects.reference[v]+n.rects.reference[p]-a[p]-n.rects.popper[v],x=a[p]-n.rects.reference[p],g=re(i),C=g?p==="y"?g.clientHeight||0:g.clientWidth||0:0,h=E/2-x/2,r=d[O],m=C-l[v]-d[b],f=C/2-l[v]/2+h,w=Ut(r,f,m),_=p;n.modifiersData[o]=((t={})[_]=w,t.centerOffset=w-f,t)}},effect:function(e){var t=e.state,n=e.options.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o!="string"||(o=t.elements.popper.querySelector(o)))&&Sn(t.elements.popper,o)&&(t.elements.arrow=o)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function kt(e){return e.split("-")[1]}var Po={top:"auto",right:"auto",bottom:"auto",left:"auto"};function cn(e){var t,n=e.popper,o=e.popperRect,s=e.placement,i=e.variation,a=e.offsets,c=e.position,p=e.gpuAcceleration,v=e.adaptive,d=e.roundOffsets,l=e.isFixed,O=a.x,b=O===void 0?0:O,E=a.y,x=E===void 0?0:E,g=typeof d=="function"?d({x:b,y:x}):{x:b,y:x};b=g.x,x=g.y;var C=a.hasOwnProperty("x"),h=a.hasOwnProperty("y"),r=J,m=X,f=window;if(v){var w=re(n),_="clientHeight",H="clientWidth";w===G(n)&&bt(w=Et(n)).position!=="static"&&c==="absolute"&&(_="scrollHeight",H="scrollWidth"),(s===X||(s===J||s===at)&&i===Gt)&&(m=it,x-=(l&&w===f&&f.visualViewport?f.visualViewport.height:w[_])-o.height,x*=p?1:-1),(s===J||(s===X||s===it)&&i===Gt)&&(r=at,b-=(l&&w===f&&f.visualViewport?f.visualViewport.width:w[H])-o.width,b*=p?1:-1)}var j,P=Object.assign({position:c},v&&Po),k=d===!0?function(W,$){var I=W.x,F=W.y,S=$.devicePixelRatio||1;return{x:Mt(I*S)/S||0,y:Mt(F*S)/S||0}}({x:b,y:x},G(n)):{x:b,y:x};return b=k.x,x=k.y,p?Object.assign({},P,((j={})[m]=h?"0":"",j[r]=C?"0":"",j.transform=(f.devicePixelRatio||1)<=1?"translate("+b+"px, "+x+"px)":"translate3d("+b+"px, "+x+"px, 0)",j)):Object.assign({},P,((t={})[m]=h?x+"px":"",t[r]=C?b+"px":"",t.transform="",t))}var fe={passive:!0},Ro={left:"right",right:"left",bottom:"top",top:"bottom"};function le(e){return e.replace(/left|right|bottom|top/g,function(t){return Ro[t]})}var No={start:"end",end:"start"};function un(e){return e.replace(/start|end/g,function(t){return No[t]})}function Ye(e){var t=G(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function $e(e){return St(Et(e)).left+Ye(e).scrollLeft}function Ke(e){var t=bt(e),n=t.overflow,o=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+o)}function Rn(e){return["html","body","#document"].indexOf(mt(e))>=0?e.ownerDocument.body:rt(e)&&Ke(e)?e:Rn(Te(e))}function Yt(e,t){var n;t===void 0&&(t=[]);var o=Rn(e),s=o===((n=e.ownerDocument)==null?void 0:n.body),i=G(o),a=s?[i].concat(i.visualViewport||[],Ke(o)?o:[]):o,c=t.concat(a);return s?c:c.concat(Yt(Te(a)))}function We(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function pn(e,t,n){return t===Ln?We(function(o,s){var i=G(o),a=Et(o),c=i.visualViewport,p=a.clientWidth,v=a.clientHeight,d=0,l=0;if(c){p=c.width,v=c.height;var O=Mn();(O||!O&&s==="fixed")&&(d=c.offsetLeft,l=c.offsetTop)}return{width:p,height:v,x:d+$e(o),y:l}}(e,n)):At(t)?function(o,s){var i=St(o,!1,s==="fixed");return i.top=i.top+o.clientTop,i.left=i.left+o.clientLeft,i.bottom=i.top+o.clientHeight,i.right=i.left+o.clientWidth,i.width=o.clientWidth,i.height=o.clientHeight,i.x=i.left,i.y=i.top,i}(t,n):We(function(o){var s,i=Et(o),a=Ye(o),c=(s=o.ownerDocument)==null?void 0:s.body,p=_t(i.scrollWidth,i.clientWidth,c?c.scrollWidth:0,c?c.clientWidth:0),v=_t(i.scrollHeight,i.clientHeight,c?c.scrollHeight:0,c?c.clientHeight:0),d=-a.scrollLeft+$e(o),l=-a.scrollTop;return bt(c||i).direction==="rtl"&&(d+=_t(i.clientWidth,c?c.clientWidth:0)-p),{width:p,height:v,x:d,y:l}}(Et(e)))}function $o(e,t,n,o){var s=t==="clippingParents"?function(p){var v=Yt(Te(p)),d=["absolute","fixed"].indexOf(bt(p).position)>=0&&rt(p)?re(p):p;return At(d)?v.filter(function(l){return At(l)&&Sn(l,d)&&mt(l)!=="body"}):[]}(e):[].concat(t),i=[].concat(s,[n]),a=i[0],c=i.reduce(function(p,v){var d=pn(e,v,o);return p.top=_t(d.top,p.top),p.right=xe(d.right,p.right),p.bottom=xe(d.bottom,p.bottom),p.left=_t(d.left,p.left),p},pn(e,a,o));return c.width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c}function Nn(e){var t,n=e.reference,o=e.element,s=e.placement,i=s?ht(s):null,a=s?kt(s):null,c=n.x+n.width/2-o.width/2,p=n.y+n.height/2-o.height/2;switch(i){case X:t={x:c,y:n.y-o.height};break;case it:t={x:c,y:n.y+n.height};break;case at:t={x:n.x+n.width,y:p};break;case J:t={x:n.x-o.width,y:p};break;default:t={x:n.x,y:n.y}}var v=i?ze(i):null;if(v!=null){var d=v==="y"?"height":"width";switch(a){case Ht:t[v]=t[v]-(n[d]/2-o[d]/2);break;case Gt:t[v]=t[v]+(n[d]/2-o[d]/2)}}return t}function Qt(e,t){t===void 0&&(t={});var n=t,o=n.placement,s=o===void 0?e.placement:o,i=n.strategy,a=i===void 0?e.strategy:i,c=n.boundary,p=c===void 0?jo:c,v=n.rootBoundary,d=v===void 0?Ln:v,l=n.elementContext,O=l===void 0?It:l,b=n.altBoundary,E=b!==void 0&&b,x=n.padding,g=x===void 0?0:x,C=kn(typeof g!="number"?g:Pn(g,oe)),h=O===It?Ho:It,r=e.rects.popper,m=e.elements[E?h:O],f=$o(At(m)?m:m.contextElement||Et(e.elements.popper),p,d,a),w=St(e.elements.reference),_=Nn({reference:w,element:r,placement:s}),H=We(Object.assign({},r,_)),j=O===It?H:w,P={top:f.top-j.top+C.top,bottom:j.bottom-f.bottom+C.bottom,left:f.left-j.left+C.left,right:j.right-f.right+C.right},k=e.modifiersData.offset;if(O===It&&k){var W=k[s];Object.keys(P).forEach(function($){var I=[at,it].indexOf($)>=0?1:-1,F=[X,it].indexOf($)>=0?"y":"x";P[$]+=W[F]*I})}return P}function Wo(e,t){t===void 0&&(t={});var n=t,o=n.placement,s=n.boundary,i=n.rootBoundary,a=n.padding,c=n.flipVariations,p=n.allowedAutoPlacements,v=p===void 0?jn:p,d=kt(o),l=d?c?an:an.filter(function(E){return kt(E)===d}):oe,O=l.filter(function(E){return v.indexOf(E)>=0});O.length===0&&(O=l);var b=O.reduce(function(E,x){return E[x]=Qt(e,{placement:x,boundary:s,rootBoundary:i,padding:a})[ht(x)],E},{});return Object.keys(b).sort(function(E,x){return b[E]-b[x]})}const Vo={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var s=n.mainAxis,i=s===void 0||s,a=n.altAxis,c=a===void 0||a,p=n.fallbackPlacements,v=n.padding,d=n.boundary,l=n.rootBoundary,O=n.altBoundary,b=n.flipVariations,E=b===void 0||b,x=n.allowedAutoPlacements,g=t.options.placement,C=ht(g),h=p||(C===g||!E?[le(g)]:function(R){if(ht(R)===Re)return[];var V=le(R);return[un(R),V,un(V)]}(g)),r=[g].concat(h).reduce(function(R,V){return R.concat(ht(V)===Re?Wo(t,{placement:V,boundary:d,rootBoundary:l,padding:v,flipVariations:E,allowedAutoPlacements:x}):V)},[]),m=t.rects.reference,f=t.rects.popper,w=new Map,_=!0,H=r[0],j=0;j<r.length;j++){var P=r[j],k=ht(P),W=kt(P)===Ht,$=[X,it].indexOf(k)>=0,I=$?"width":"height",F=Qt(t,{placement:P,boundary:d,rootBoundary:l,altBoundary:O,padding:v}),S=$?W?at:J:W?it:X;m[I]>f[I]&&(S=le(S));var N=le(S),Q=[];if(i&&Q.push(F[k]<=0),c&&Q.push(F[S]<=0,F[N]<=0),Q.every(function(R){return R})){H=P,_=!1;break}w.set(P,Q)}if(_)for(var tt=function(R){var V=r.find(function(pt){var ft=w.get(pt);if(ft)return ft.slice(0,R).every(function(xt){return xt})});if(V)return H=V,"break"},et=E?3:1;et>0&&tt(et)!=="break";et--);t.placement!==H&&(t.modifiersData[o]._skip=!0,t.placement=H,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function fn(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function ln(e){return[X,at,it,J].some(function(t){return e[t]>=0})}const Bo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,s=n.offset,i=s===void 0?[0,0]:s,a=jn.reduce(function(d,l){return d[l]=function(O,b,E){var x=ht(O),g=[J,X].indexOf(x)>=0?-1:1,C=typeof E=="function"?E(Object.assign({},b,{placement:O})):E,h=C[0],r=C[1];return h=h||0,r=(r||0)*g,[J,at].indexOf(x)>=0?{x:r,y:h}:{x:h,y:r}}(l,t.rects,i),d},{}),c=a[t.placement],p=c.x,v=c.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=p,t.modifiersData.popperOffsets.y+=v),t.modifiersData[o]=a}},qo={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,s=n.mainAxis,i=s===void 0||s,a=n.altAxis,c=a!==void 0&&a,p=n.boundary,v=n.rootBoundary,d=n.altBoundary,l=n.padding,O=n.tether,b=O===void 0||O,E=n.tetherOffset,x=E===void 0?0:E,g=Qt(t,{boundary:p,rootBoundary:v,padding:l,altBoundary:d}),C=ht(t.placement),h=kt(t.placement),r=!h,m=ze(C),f=m==="x"?"y":"x",w=t.modifiersData.popperOffsets,_=t.rects.reference,H=t.rects.popper,j=typeof x=="function"?x(Object.assign({},t.rects,{placement:t.placement})):x,P=typeof j=="number"?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,W={x:0,y:0};if(w){if(i){var $,I=m==="y"?X:J,F=m==="y"?it:at,S=m==="y"?"height":"width",N=w[m],Q=N+g[I],tt=N-g[F],et=b?-H[S]/2:0,R=h===Ht?_[S]:H[S],V=h===Ht?-H[S]:-_[S],pt=t.elements.arrow,ft=b&&pt?Fe(pt):{width:0,height:0},xt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},$t=xt[I],gt=xt[F],Ot=Ut(0,_[S],ft[S]),ie=r?_[S]/2-et-Ot-$t-P.mainAxis:R-Ot-$t-P.mainAxis,ae=r?-_[S]/2+et+Ot+gt+P.mainAxis:V+Ot+gt+P.mainAxis,Dt=t.elements.arrow&&re(t.elements.arrow),se=Dt?m==="y"?Dt.clientTop||0:Dt.clientLeft||0:0,Wt=($=k==null?void 0:k[m])!=null?$:0,ce=N+ae-Wt,Vt=Ut(b?xe(Q,N+ie-Wt-se):Q,N,b?_t(tt,ce):tt);w[m]=Vt,W[m]=Vt-N}if(c){var Bt,qt=m==="x"?X:J,ue=m==="x"?it:at,nt=w[f],u=f==="y"?"height":"width",y=nt+g[qt],T=nt-g[ue],A=[X,J].indexOf(C)!==-1,L=(Bt=k==null?void 0:k[f])!=null?Bt:0,B=A?y:nt-_[u]-H[u]-L+P.altAxis,U=A?nt+_[u]+H[u]-L-P.altAxis:T,z=b&&A?function(q,yt,K){var Z=Ut(q,yt,K);return Z>K?K:Z}(B,nt,U):Ut(b?B:y,nt,b?U:T);w[f]=z,W[f]=z-nt}t.modifiersData[o]=W}},requiresIfExists:["offset"]};function Io(e,t,n){n===void 0&&(n=!1);var o,s,i=rt(t),a=rt(t)&&function(l){var O=l.getBoundingClientRect(),b=Mt(O.width)/l.offsetWidth||1,E=Mt(O.height)/l.offsetHeight||1;return b!==1||E!==1}(t),c=Et(t),p=St(e,a,n),v={scrollLeft:0,scrollTop:0},d={x:0,y:0};return(i||!i&&!n)&&((mt(t)!=="body"||Ke(c))&&(v=(o=t)!==G(o)&&rt(o)?{scrollLeft:(s=o).scrollLeft,scrollTop:s.scrollTop}:Ye(o)),rt(t)?((d=St(t,!0)).x+=t.clientLeft,d.y+=t.clientTop):c&&(d.x=$e(c))),{x:p.left+v.scrollLeft-d.x,y:p.top+v.scrollTop-d.y,width:p.width,height:p.height}}function Uo(e){var t=new Map,n=new Set,o=[];function s(i){n.add(i.name),[].concat(i.requires||[],i.requiresIfExists||[]).forEach(function(a){if(!n.has(a)){var c=t.get(a);c&&s(c)}}),o.push(i)}return e.forEach(function(i){t.set(i.name,i)}),e.forEach(function(i){n.has(i.name)||s(i)}),o}var dn={placement:"bottom",modifiers:[],strategy:"absolute"};function vn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function Fo(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,s=t.defaultOptions,i=s===void 0?dn:s;return function(a,c,p){p===void 0&&(p=i);var v,d,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},dn,i),modifiersData:{},elements:{reference:a,popper:c},attributes:{},styles:{}},O=[],b=!1,E={state:l,setOptions:function(g){var C=typeof g=="function"?g(l.options):g;x(),l.options=Object.assign({},i,l.options,C),l.scrollParents={reference:At(a)?Yt(a):a.contextElement?Yt(a.contextElement):[],popper:Yt(c)};var h,r,m=function(f){var w=Uo(f);return Mo.reduce(function(_,H){return _.concat(w.filter(function(j){return j.phase===H}))},[])}((h=[].concat(o,l.options.modifiers),r=h.reduce(function(f,w){var _=f[w.name];return f[w.name]=_?Object.assign({},_,w,{options:Object.assign({},_.options,w.options),data:Object.assign({},_.data,w.data)}):w,f},{}),Object.keys(r).map(function(f){return r[f]})));return l.orderedModifiers=m.filter(function(f){return f.enabled}),l.orderedModifiers.forEach(function(f){var w=f.name,_=f.options,H=_===void 0?{}:_,j=f.effect;if(typeof j=="function"){var P=j({state:l,name:w,instance:E,options:H}),k=function(){};O.push(P||k)}}),E.update()},forceUpdate:function(){if(!b){var g=l.elements,C=g.reference,h=g.popper;if(vn(C,h)){l.rects={reference:Io(C,re(h),l.options.strategy==="fixed"),popper:Fe(h)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(j){return l.modifiersData[j.name]=Object.assign({},j.data)});for(var r=0;r<l.orderedModifiers.length;r++)if(l.reset!==!0){var m=l.orderedModifiers[r],f=m.fn,w=m.options,_=w===void 0?{}:w,H=m.name;typeof f=="function"&&(l=f({state:l,options:_,name:H,instance:E})||l)}else l.reset=!1,r=-1}}},update:(v=function(){return new Promise(function(g){E.forceUpdate(),g(l)})},function(){return d||(d=new Promise(function(g){Promise.resolve().then(function(){d=void 0,g(v())})})),d}),destroy:function(){x(),b=!0}};if(!vn(a,c))return E;function x(){O.forEach(function(g){return g()}),O=[]}return E.setOptions(p).then(function(g){!b&&p.onFirstUpdate&&p.onFirstUpdate(g)}),E}}var zo=Fo({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,s=o.scroll,i=s===void 0||s,a=o.resize,c=a===void 0||a,p=G(t.elements.popper),v=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&v.forEach(function(d){d.addEventListener("scroll",n.update,fe)}),c&&p.addEventListener("resize",n.update,fe),function(){i&&v.forEach(function(d){d.removeEventListener("scroll",n.update,fe)}),c&&p.removeEventListener("resize",n.update,fe)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Nn({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,s=o===void 0||o,i=n.adaptive,a=i===void 0||i,c=n.roundOffsets,p=c===void 0||c,v={placement:ht(t.placement),variation:kt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,cn(Object.assign({},v,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:p})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,cn(Object.assign({},v,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:p})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Hn,Bo,Vo,qo,ko,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,s=t.rects.popper,i=t.modifiersData.preventOverflow,a=Qt(t,{elementContext:"reference"}),c=Qt(t,{altBoundary:!0}),p=fn(a,o),v=fn(c,s,i),d=ln(p),l=ln(v);t.modifiersData[n]={referenceClippingOffsets:p,popperEscapeOffsets:v,isReferenceHidden:d,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":l})}}]}),$n="tippy-content",Yo="tippy-backdrop",Wn="tippy-arrow",Vn="tippy-svg-arrow",Tt={passive:!0,capture:!0},Bn=function(){return document.body};function De(e,t,n){if(Array.isArray(e)){var o=e[t];return o??(Array.isArray(n)?n[t]:n)}return e}function Xe(e,t){var n={}.toString.call(e);return n.indexOf("[object")===0&&n.indexOf(t+"]")>-1}function qn(e,t){return typeof e=="function"?e.apply(void 0,t):e}function hn(e,t){return t===0?e:function(o){clearTimeout(n),n=setTimeout(function(){e(o)},t)};var n}function Lt(e){return[].concat(e)}function mn(e,t){e.indexOf(t)===-1&&e.push(t)}function we(e){return[].slice.call(e)}function gn(e){return Object.keys(e).reduce(function(t,n){return e[n]!==void 0&&(t[n]=e[n]),t},{})}function Kt(){return document.createElement("div")}function _e(e){return["Element","Fragment"].some(function(t){return Xe(e,t)})}function Ko(e){return _e(e)?[e]:function(t){return Xe(t,"NodeList")}(e)?we(e):Array.isArray(e)?e:we(document.querySelectorAll(e))}function Le(e,t){e.forEach(function(n){n&&(n.style.transitionDuration=t+"ms")})}function yn(e,t){e.forEach(function(n){n&&n.setAttribute("data-state",t)})}function je(e,t,n){var o=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(s){e[o](s,n)})}function bn(e,t){for(var n=t;n;){var o;if(e.contains(n))return!0;n=n.getRootNode==null||(o=n.getRootNode())==null?void 0:o.host}return!1}var vt={isTouch:!1},xn=0;function Xo(){vt.isTouch||(vt.isTouch=!0,window.performance&&document.addEventListener("mousemove",In))}function In(){var e=performance.now();e-xn<20&&(vt.isTouch=!1,document.removeEventListener("mousemove",In)),xn=e}function Jo(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var Zo=typeof window<"u"&&typeof document<"u"&&!!window.msCrypto,ut=Object.assign({appendTo:Bn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Go=Object.keys(ut);function Un(e){var t=(e.plugins||[]).reduce(function(n,o){var s,i=o.name,a=o.defaultValue;return i&&(n[i]=e[i]!==void 0?e[i]:(s=ut[i])!=null?s:a),n},{});return Object.assign({},e,t)}function wn(e,t){var n=Object.assign({},t,{content:qn(t.content,[e])},t.ignoreAttributes?{}:function(o,s){return(s?Object.keys(Un(Object.assign({},ut,{plugins:s}))):Go).reduce(function(i,a){var c=(o.getAttribute("data-tippy-"+a)||"").trim();if(!c)return i;if(a==="content")i[a]=c;else try{i[a]=JSON.parse(c)}catch{i[a]=c}return i},{})}(e,t.plugins));return n.aria=Object.assign({},ut.aria,n.aria),n.aria={expanded:n.aria.expanded==="auto"?t.interactive:n.aria.expanded,content:n.aria.content==="auto"?t.interactive?null:"describedby":n.aria.content},n}var Qo=function(){return"innerHTML"};function Ve(e,t){e[Qo()]=t}function En(e){var t=Kt();return e===!0?t.className=Wn:(t.className=Vn,_e(e)?t.appendChild(e):Ve(t,e)),t}function On(e,t){_e(t.content)?(Ve(e,""),e.appendChild(t.content)):typeof t.content!="function"&&(t.allowHTML?Ve(e,t.content):e.textContent=t.content)}function Be(e){var t=e.firstElementChild,n=we(t.children);return{box:t,content:n.find(function(o){return o.classList.contains($n)}),arrow:n.find(function(o){return o.classList.contains(Wn)||o.classList.contains(Vn)}),backdrop:n.find(function(o){return o.classList.contains(Yo)})}}function Fn(e){var t=Kt(),n=Kt();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var o=Kt();function s(i,a){var c=Be(t),p=c.box,v=c.content,d=c.arrow;a.theme?p.setAttribute("data-theme",a.theme):p.removeAttribute("data-theme"),typeof a.animation=="string"?p.setAttribute("data-animation",a.animation):p.removeAttribute("data-animation"),a.inertia?p.setAttribute("data-inertia",""):p.removeAttribute("data-inertia"),p.style.maxWidth=typeof a.maxWidth=="number"?a.maxWidth+"px":a.maxWidth,a.role?p.setAttribute("role",a.role):p.removeAttribute("role"),i.content===a.content&&i.allowHTML===a.allowHTML||On(v,e.props),a.arrow?d?i.arrow!==a.arrow&&(p.removeChild(d),p.appendChild(En(a.arrow))):p.appendChild(En(a.arrow)):d&&p.removeChild(d)}return o.className=$n,o.setAttribute("data-state","hidden"),On(o,e.props),t.appendChild(n),n.appendChild(o),s(e.props,e.props),{popper:t,onUpdate:s}}Fn.$$tippy=!0;var tr=1,de=[],He=[];function er(e,t){var n,o,s,i,a,c,p,v,d=wn(e,Object.assign({},ut,Un(gn(t)))),l=!1,O=!1,b=!1,E=!1,x=[],g=hn(Dt,d.interactiveDebounce),C=tr++,h=(v=d.plugins).filter(function(u,y){return v.indexOf(u)===y}),r={id:C,reference:e,popper:Kt(),popperInstance:null,props:d,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:h,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(s)},setProps:function(u){if(!r.state.isDestroyed){N("onBeforeUpdate",[r,u]),ie();var y=r.props,T=wn(e,Object.assign({},y,gn(u),{ignoreAttributes:!0}));r.props=T,Ot(),y.interactiveDebounce!==T.interactiveDebounce&&(et(),g=hn(Dt,T.interactiveDebounce)),y.triggerTarget&&!T.triggerTarget?Lt(y.triggerTarget).forEach(function(A){A.removeAttribute("aria-expanded")}):T.triggerTarget&&e.removeAttribute("aria-expanded"),tt(),S(),w&&w(y,T),r.popperInstance&&(Vt(),qt().forEach(function(A){requestAnimationFrame(A._tippy.popperInstance.forceUpdate)})),N("onAfterUpdate",[r,u])}},setContent:function(u){r.setProps({content:u})},show:function(){var u=r.state.isVisible,y=r.state.isDestroyed,T=!r.state.isEnabled,A=vt.isTouch&&!r.props.touch,L=De(r.props.duration,0,ut.duration);if(!(u||y||T||A)&&!W().hasAttribute("disabled")&&(N("onShow",[r],!1),r.props.onShow(r)!==!1)){if(r.state.isVisible=!0,k()&&(f.style.visibility="visible"),S(),ft(),r.state.isMounted||(f.style.transition="none"),k()){var B=I();Le([B.box,B.content],0)}c=function(){var U;if(r.state.isVisible&&!E){if(E=!0,f.offsetHeight,f.style.transition=r.props.moveTransition,k()&&r.props.animation){var z=I(),q=z.box,yt=z.content;Le([q,yt],L),yn([q,yt],"visible")}Q(),tt(),mn(He,r),(U=r.popperInstance)==null||U.forceUpdate(),N("onMount",[r]),r.props.animation&&k()&&function(K,Z){$t(K,Z)}(L,function(){r.state.isShown=!0,N("onShown",[r])})}},function(){var U,z=r.props.appendTo,q=W();U=r.props.interactive&&z===Bn||z==="parent"?q.parentNode:qn(z,[q]),U.contains(f)||U.appendChild(f),r.state.isMounted=!0,Vt()}()}},hide:function(){var u=!r.state.isVisible,y=r.state.isDestroyed,T=!r.state.isEnabled,A=De(r.props.duration,1,ut.duration);if(!(u||y||T)&&(N("onHide",[r],!1),r.props.onHide(r)!==!1)){if(r.state.isVisible=!1,r.state.isShown=!1,E=!1,l=!1,k()&&(f.style.visibility="hidden"),et(),xt(),S(!0),k()){var L=I(),B=L.box,U=L.content;r.props.animation&&(Le([B,U],A),yn([B,U],"hidden"))}Q(),tt(),r.props.animation?k()&&function(z,q){$t(z,function(){!r.state.isVisible&&f.parentNode&&f.parentNode.contains(f)&&q()})}(A,r.unmount):r.unmount()}},hideWithInteractivity:function(u){$().addEventListener("mousemove",g),mn(de,g),g(u)},enable:function(){r.state.isEnabled=!0},disable:function(){r.hide(),r.state.isEnabled=!1},unmount:function(){r.state.isVisible&&r.hide(),r.state.isMounted&&(Bt(),qt().forEach(function(u){u._tippy.unmount()}),f.parentNode&&f.parentNode.removeChild(f),He=He.filter(function(u){return u!==r}),r.state.isMounted=!1,N("onHidden",[r]))},destroy:function(){r.state.isDestroyed||(r.clearDelayTimeouts(),r.unmount(),ie(),delete e._tippy,r.state.isDestroyed=!0,N("onDestroy",[r]))}};if(!d.render)return r;var m=d.render(r),f=m.popper,w=m.onUpdate;f.setAttribute("data-tippy-root",""),f.id="tippy-"+r.id,r.popper=f,e._tippy=r,f._tippy=r;var _=h.map(function(u){return u.fn(r)}),H=e.hasAttribute("aria-expanded");return Ot(),tt(),S(),N("onCreate",[r]),d.showOnCreate&&ue(),f.addEventListener("mouseenter",function(){r.props.interactive&&r.state.isVisible&&r.clearDelayTimeouts()}),f.addEventListener("mouseleave",function(){r.props.interactive&&r.props.trigger.indexOf("mouseenter")>=0&&$().addEventListener("mousemove",g)}),r;function j(){var u=r.props.touch;return Array.isArray(u)?u:[u,0]}function P(){return j()[0]==="hold"}function k(){var u;return!((u=r.props.render)==null||!u.$$tippy)}function W(){return p||e}function $(){var u,y,T=W().parentNode;return T&&(y=Lt(T)[0])!=null&&(u=y.ownerDocument)!=null&&u.body?y.ownerDocument:document}function I(){return Be(f)}function F(u){return r.state.isMounted&&!r.state.isVisible||vt.isTouch||i&&i.type==="focus"?0:De(r.props.delay,u?0:1,ut.delay)}function S(u){u===void 0&&(u=!1),f.style.pointerEvents=r.props.interactive&&!u?"":"none",f.style.zIndex=""+r.props.zIndex}function N(u,y,T){var A;T===void 0&&(T=!0),_.forEach(function(L){L[u]&&L[u].apply(L,y)}),T&&(A=r.props)[u].apply(A,y)}function Q(){var u=r.props.aria;if(u.content){var y="aria-"+u.content,T=f.id;Lt(r.props.triggerTarget||e).forEach(function(A){var L=A.getAttribute(y);if(r.state.isVisible)A.setAttribute(y,L?L+" "+T:T);else{var B=L&&L.replace(T,"").trim();B?A.setAttribute(y,B):A.removeAttribute(y)}})}}function tt(){!H&&r.props.aria.expanded&&Lt(r.props.triggerTarget||e).forEach(function(u){r.props.interactive?u.setAttribute("aria-expanded",r.state.isVisible&&u===W()?"true":"false"):u.removeAttribute("aria-expanded")})}function et(){$().removeEventListener("mousemove",g),de=de.filter(function(u){return u!==g})}function R(u){if(!vt.isTouch||!b&&u.type!=="mousedown"){var y=u.composedPath&&u.composedPath()[0]||u.target;if(!r.props.interactive||!bn(f,y)){if(Lt(r.props.triggerTarget||e).some(function(T){return bn(T,y)})){if(vt.isTouch||r.state.isVisible&&r.props.trigger.indexOf("click")>=0)return}else N("onClickOutside",[r,u]);r.props.hideOnClick===!0&&(r.clearDelayTimeouts(),r.hide(),O=!0,setTimeout(function(){O=!1}),r.state.isMounted||xt())}}}function V(){b=!0}function pt(){b=!1}function ft(){var u=$();u.addEventListener("mousedown",R,!0),u.addEventListener("touchend",R,Tt),u.addEventListener("touchstart",pt,Tt),u.addEventListener("touchmove",V,Tt)}function xt(){var u=$();u.removeEventListener("mousedown",R,!0),u.removeEventListener("touchend",R,Tt),u.removeEventListener("touchstart",pt,Tt),u.removeEventListener("touchmove",V,Tt)}function $t(u,y){var T=I().box;function A(L){L.target===T&&(je(T,"remove",A),y())}if(u===0)return y();je(T,"remove",a),je(T,"add",A),a=A}function gt(u,y,T){T===void 0&&(T=!1),Lt(r.props.triggerTarget||e).forEach(function(A){A.addEventListener(u,y,T),x.push({node:A,eventType:u,handler:y,options:T})})}function Ot(){var u;P()&&(gt("touchstart",ae,{passive:!0}),gt("touchend",se,{passive:!0})),(u=r.props.trigger,u.split(/\s+/).filter(Boolean)).forEach(function(y){if(y!=="manual")switch(gt(y,ae),y){case"mouseenter":gt("mouseleave",se);break;case"focus":gt(Zo?"focusout":"blur",Wt);break;case"focusin":gt("focusout",Wt)}})}function ie(){x.forEach(function(u){var y=u.node,T=u.eventType,A=u.handler,L=u.options;y.removeEventListener(T,A,L)}),x=[]}function ae(u){var y,T=!1;if(r.state.isEnabled&&!ce(u)&&!O){var A=((y=i)==null?void 0:y.type)==="focus";i=u,p=u.currentTarget,tt(),!r.state.isVisible&&Xe(u,"MouseEvent")&&de.forEach(function(L){return L(u)}),u.type==="click"&&(r.props.trigger.indexOf("mouseenter")<0||l)&&r.props.hideOnClick!==!1&&r.state.isVisible?T=!0:ue(u),u.type==="click"&&(l=!T),T&&!A&&nt(u)}}function Dt(u){var y=u.target,T=W().contains(y)||f.contains(y);u.type==="mousemove"&&T||function(A,L){var B=L.clientX,U=L.clientY;return A.every(function(z){var q=z.popperRect,yt=z.popperState,K=z.props.interactiveBorder,Z=yt.placement.split("-")[0],lt=yt.modifiersData.offset;if(!lt)return!0;var zn=Z==="bottom"?lt.top.y:0,Yn=Z==="top"?lt.bottom.y:0,Kn=Z==="right"?lt.left.x:0,Xn=Z==="left"?lt.right.x:0,Jn=q.top-U+zn>K,Zn=U-q.bottom-Yn>K,Gn=q.left-B+Kn>K,Qn=B-q.right-Xn>K;return Jn||Zn||Gn||Qn})}(qt().concat(f).map(function(A){var L,B=(L=A._tippy.popperInstance)==null?void 0:L.state;return B?{popperRect:A.getBoundingClientRect(),popperState:B,props:d}:null}).filter(Boolean),u)&&(et(),nt(u))}function se(u){ce(u)||r.props.trigger.indexOf("click")>=0&&l||(r.props.interactive?r.hideWithInteractivity(u):nt(u))}function Wt(u){r.props.trigger.indexOf("focusin")<0&&u.target!==W()||r.props.interactive&&u.relatedTarget&&f.contains(u.relatedTarget)||nt(u)}function ce(u){return!!vt.isTouch&&P()!==u.type.indexOf("touch")>=0}function Vt(){Bt();var u=r.props,y=u.popperOptions,T=u.placement,A=u.offset,L=u.getReferenceClientRect,B=u.moveTransition,U=k()?Be(f).arrow:null,z=L?{getBoundingClientRect:L,contextElement:L.contextElement||W()}:e,q=[{name:"offset",options:{offset:A}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!B}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(yt){var K=yt.state;if(k()){var Z=I().box;["placement","reference-hidden","escaped"].forEach(function(lt){lt==="placement"?Z.setAttribute("data-placement",K.placement):K.attributes.popper["data-popper-"+lt]?Z.setAttribute("data-"+lt,""):Z.removeAttribute("data-"+lt)}),K.attributes.popper={}}}}];k()&&U&&q.push({name:"arrow",options:{element:U,padding:3}}),q.push.apply(q,(y==null?void 0:y.modifiers)||[]),r.popperInstance=zo(z,f,Object.assign({},y,{placement:T,onFirstUpdate:c,modifiers:q}))}function Bt(){r.popperInstance&&(r.popperInstance.destroy(),r.popperInstance=null)}function qt(){return we(f.querySelectorAll("[data-tippy-root]"))}function ue(u){r.clearDelayTimeouts(),u&&N("onTrigger",[r,u]),ft();var y=F(!0),T=j(),A=T[0],L=T[1];vt.isTouch&&A==="hold"&&L&&(y=L),y?n=setTimeout(function(){r.show()},y):r.show()}function nt(u){if(r.clearDelayTimeouts(),N("onUntrigger",[r,u]),r.state.isVisible){if(!(r.props.trigger.indexOf("mouseenter")>=0&&r.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(u.type)>=0&&l)){var y=F(!1);y?o=setTimeout(function(){r.state.isVisible&&r.hide()},y):s=requestAnimationFrame(function(){r.hide()})}}else xt()}}function Ft(e,t){t===void 0&&(t={});var n=ut.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",Xo,Tt),window.addEventListener("blur",Jo);var o=Object.assign({},t,{plugins:n}),s=Ko(e).reduce(function(i,a){var c=a&&er(a,o);return c&&i.push(c),i},[]);return _e(e)?s[0]:s}Ft.defaultProps=ut,Ft.setDefaultProps=function(e){Object.keys(e).forEach(function(t){ut[t]=e[t]})},Ft.currentInput=vt,Object.assign({},Hn,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),Ft.setDefaultProps({render:Fn});var Pt=(e=>(e.Hover="hover",e.Click="click",e))(Pt||{});const Xt=class Xt extends Event{constructor(){super(Xt.eventType,{bubbles:!0})}static isEvent(t){return t.type===Xt.eventType}};M(Xt,"eventType","augment-ds-event__close-tooltip-request");let wt=Xt;const Oe=class Oe{constructor(t){M(this,"debouncedHoverStart");M(this,"debouncedHoverEnd");M(this,"handleMouseEnter",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});M(this,"handleMouseLeave",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.call(this)});M(this,"handleMouseMove",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});M(this,"cancelHovers",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()});this.debouncedHoverStart=rn(t.onHoverStart,t.hoverTriggerDuration),this.debouncedHoverEnd=rn(t.onHoverEnd,Oe.DEFAULT_HOVER_END_DEBOUNCE_MS)}destroy(){var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()}};M(Oe,"DEFAULT_HOVER_END_DEBOUNCE_MS",67);let Ee=Oe;function qe(e,t){return e.addEventListener("mouseenter",t.handleMouseEnter),e.addEventListener("mouseleave",t.handleMouseLeave),e.addEventListener("mousemove",t.handleMouseMove),{destroy(){e.removeEventListener("mouseenter",t.handleMouseEnter),e.removeEventListener("mouseleave",t.handleMouseLeave),e.removeEventListener("mousemove",t.handleMouseMove)}}}const nr=Symbol("hover-action-context");function br(e=100){const t=_n(!1);Tn(nr,t);const n=new Ee({onHoverStart(){t.set(!0)},onHoverEnd(){t.set(!1)},hoverTriggerDuration:e});return function(o){return qe(o,n)}}const Jt=class Jt{constructor(t){M(this,"_state");M(this,"_tippy");M(this,"_triggerElement");M(this,"_contentElement");M(this,"_contentProps");M(this,"_hoverContext");M(this,"_referenceClientRect");M(this,"_hasPointerEvents",!0);M(this,"_setOpen",t=>{var n,o;this._isOpen!==t&&(this._state.update(s=>({...s,open:t})),(o=(n=this._opts).onOpenChange)==null||o.call(n,t))});M(this,"openTooltip",()=>{this.internalControlSetOpen(!0)});M(this,"closeTooltip",()=>{this.internalControlSetOpen(!1)});M(this,"toggleTooltip",()=>{this.internalControlSetOpen(!this._isOpen)});M(this,"externalControlSetOpen",t=>{this._opts.open=t,t!==void 0&&this._setOpen(t)});M(this,"updateTippyTheme",t=>{this._opts.tippyTheme!==t&&(this._opts.tippyTheme=t,this._updateTippy())});M(this,"internalControlSetOpen",t=>{this._isExternallyControlled||this._setOpen(t)});M(this,"_updateTippy",()=>{var n;if(!this._triggerElement||!this._contentElement||!this._contentProps)return(n=this._tippy)==null||n.destroy(),void(this._tippy=void 0);const t={trigger:"manual",showOnCreate:this._isOpen,offset:this._opts.offset??[0,2],interactive:this._hasPointerEvents,content:this._contentElement,popperOptions:{strategy:"fixed",modifiers:[{name:"preventOverflow",options:{padding:this._opts.nested?12:0}}]},duration:0,delay:0,placement:or(this._contentProps),hideOnClick:!0,appendTo:this._opts.nested?this._triggerElement:document.body,theme:this._opts.tippyTheme};if(this._referenceClientRect!==void 0){const o=this._referenceClientRect;t.getReferenceClientRect=()=>o}if(this._tippy!==void 0)this._tippy.setProps(t);else{const o=this._state.subscribe(s=>{var i,a;s.open?(i=this._tippy)==null||i.show():(a=this._tippy)==null||a.hide()});this._tippy=Ft(this._triggerElement,{...t,onDestroy:o})}});M(this,"update",()=>{var t,n;(n=(t=this._tippy)==null?void 0:t.popperInstance)==null||n.update()});M(this,"registerTrigger",(t,n)=>{this._triggerElement=t,this._referenceClientRect=n;const o=this._hoverContext&&qe(this._triggerElement,this._hoverContext);return this._updateTippy(),{update:s=>{this._referenceClientRect=s,this._updateTippy()},destroy:()=>{o==null||o.destroy(),this._triggerElement=void 0,this._updateTippy()}}});M(this,"registerContents",(t,n)=>{t.remove(),this._contentElement=t,this._contentProps=n;const o=this._hoverContext&&qe(this._contentElement,this._hoverContext);this._updateTippy();const s=function(i,a){const c=new ResizeObserver(()=>a());return c.observe(i),()=>c.disconnect()}(t,this.update);return{destroy:()=>{o==null||o.destroy(),this._contentElement=void 0,this._updateTippy(),s()},update:i=>{n={...n,...i},this._contentProps=n,this._updateTippy()}}});M(this,"requestClose",()=>{var t;(t=this._contentElement)==null||t.dispatchEvent(new wt)});this._opts=t,this._state=_n({open:this._opts.open??this._opts.defaultOpen??!1}),this.supportsHover&&(this._hoverContext=new Ee({hoverTriggerDuration:this.delayDurationMs,onHoverStart:()=>{this.openTooltip(),this._opts.onHoverStart()},onHoverEnd:()=>{this.closeTooltip(),this._opts.onHoverEnd()}})),this._hasPointerEvents=this._opts.hasPointerEvents??!0}get supportsHover(){return this._opts.triggerOn.includes(Pt.Hover)}get supportsClick(){return this._opts.triggerOn.includes(Pt.Click)}get triggerElement(){return this._triggerElement}get contentElement(){return this._contentElement}get state(){return this._state}get delayDurationMs(){return this._opts.delayDurationMs??Jt.DEFAULT_DELAY_DURATION_MS}get _isExternallyControlled(){const{defaultOpen:t,open:n}=this._opts;return n!==void 0&&(t!==void 0&&console.warn("`defaultOpen` has no effect when `open` is provided"),!0)}get _isOpen(){return ro(this._state).open}};M(Jt,"CONTEXT_KEY","augment-tooltip-context"),M(Jt,"DEFAULT_DELAY_DURATION_MS",250);let Rt=Jt;function or(e){return e.align==="center"?e.side:`${e.side}-${e.align}`}function rr(e,t){te(t,!1);let n=D(t,"defaultOpen",24,()=>{}),o=D(t,"open",24,()=>{}),s=D(t,"onOpenChange",24,()=>{}),i=D(t,"delayDurationMs",24,()=>{}),a=D(t,"nested",8,!0),c=D(t,"hasPointerEvents",8,!0),p=D(t,"offset",24,()=>{}),v=D(t,"onHoverStart",8,()=>{}),d=D(t,"onHoverEnd",8,()=>{}),l=D(t,"triggerOn",24,()=>[Pt.Hover,Pt.Click]);const O=()=>x.openTooltip(),b=()=>x.closeTooltip();let E=D(t,"tippyTheme",24,()=>{});const x=new Rt({defaultOpen:n(),open:o(),onOpenChange:s(),delayDurationMs:i(),nested:a(),onHoverStart:v(),onHoverEnd:d(),triggerOn:l(),tippyTheme:E(),hasPointerEvents:c(),offset:p()});Tn(Rt.CONTEXT_KEY,x),jt(()=>dt(o()),()=>{x.externalControlSetOpen(o())}),jt(()=>dt(E()),()=>{x.updateTippyTheme(E())}),Ie(),ee();var g=me(),C=zt(g);return Ct(C,t,"default",{},null),ct(e,g),be(t,"requestOpen",O),be(t,"requestClose",b),ne({requestOpen:O,requestClose:b})}var ir=Nt('<div role="button" tabindex="-1"><!></div>');function ar(e,t){te(t,!1);let n=D(t,"referenceClientRect",24,()=>{}),o=D(t,"class",8,"");const s=Cn(Rt.CONTEXT_KEY),i=p=>{s.supportsClick&&(s.toggleTooltip(),p.stopPropagation())};ee();var a=ir(),c=Zt(a);Ct(c,t,"default",{},null),ge(()=>Y("click",a,i)),ge(()=>Y("keydown",a,function(p){ot.call(this,t,p)})),Dn(a,(p,v)=>{var d;return(d=s.registerTrigger)==null?void 0:d.call(s,p,v)},n),ye(()=>An(a,1,`l-tooltip-trigger ${o()}`)),ct(e,a),ne()}var sr=Nt('<div role="button" tabindex="-1"><!></div>');function cr(e,t){te(t,!1);const[n,o]=lo(),s=()=>Qe(O,"$state",n),i=()=>Qe(C,"$openState",n);let a=D(t,"onEscapeKeyDown",8,()=>{}),c=D(t,"onClickOutside",8,()=>{}),p=D(t,"onRequestClose",8,()=>{}),v=D(t,"side",8,"top"),d=D(t,"align",8,"center");const l=Cn(Rt.CONTEXT_KEY),O=l.state,b=f=>{f.target!==null&&f.target instanceof Node&&l.contentElement&&l.triggerElement&&s().open&&(f.composedPath().includes(l.contentElement)||f.composedPath().includes(l.triggerElement)||(l.closeTooltip(),c()(f)))},E=f=>{f.target!==null&&f.target instanceof Node&&l.contentElement&&s().open&&f.key==="Escape"&&(l.closeTooltip(),a()(f))},x=f=>{var w;if(wt.isEvent(f)&&f.target&&((w=l.contentElement)!=null&&w.contains(f.target)))return l.closeTooltip(),p()(f),void f.stopPropagation()},g=f=>{f.target===window&&l.requestClose()},C=io(O,f=>f.open);ao(()=>{var f;(f=l.contentElement)==null||f.removeEventListener(wt.eventType,x)}),jt(()=>(i(),wt),()=>{l.contentElement&&(i()?l.contentElement.addEventListener(wt.eventType,x):l.contentElement.removeEventListener(wt.eventType,x))}),Ie(),ee();var h=sr();let r;Y("click",Ce,function(...f){var w;(w=s().open?b:void 0)==null||w.apply(this,f)},!0),Y("keydown",Ce,function(...f){var w;(w=s().open?E:void 0)==null||w.apply(this,f)},!0),Y("blur",Ce,function(...f){var w;(w=s().open?g:void 0)==null||w.apply(this,f)},!0);var m=Zt(h);Ct(m,t,"default",{},null),Dn(h,(f,w)=>{var _;return(_=l.registerContents)==null?void 0:_.call(l,f,w)},()=>({side:v(),align:d()})),ge(()=>Y("click",h,en(function(f){ot.call(this,t,f)}))),ge(()=>Y("keydown",h,en(function(f){ot.call(this,t,f)}))),ye(f=>{r=An(h,1,"l-tooltip-contents svelte-1s7j18e",null,r,f),Je(h,"data-position-side",v()),Je(h,"data-position-align",d())},[()=>({"l-tooltip-contents--open":s().open})],Se),ct(e,h),ne(),o()}const Me={Root:rr,Trigger:ar,Content:cr};var ur=Nt('<div class="svelte-hdzv5n"><!></div>'),pr=Nt("<!> <!>",1);function xr(e,t){const n=vo(t);te(t,!1);let o=D(t,"content",24,()=>{}),s=D(t,"width",24,()=>{}),i=D(t,"minWidth",24,()=>{}),a=D(t,"maxWidth",8,"250px"),c=D(t,"delayDurationMs",24,()=>{}),p=D(t,"triggerOn",24,()=>[Pt.Hover]),v=D(t,"side",8,"top"),d=D(t,"nested",8,!1),l=D(t,"hasPointerEvents",24,()=>{}),O=D(t,"offset",24,()=>v()==="top"||v()==="bottom"?[0,5]:[5,0]),b=D(t,"open",24,()=>{}),E=D(t,"align",8,"center"),x=D(t,"class",8,""),g=D(t,"onOpenChange",24,()=>{}),C=D(t,"referenceClientRect",24,()=>{}),h=D(t,"theme",8,""),r=ve(void 0);const m=()=>{var _;return(_=st(r))==null?void 0:_.requestOpen()},f=()=>{var _;return(_=st(r))==null?void 0:_.requestClose()};ee();const w=Se(()=>h()||"");return ho(Me.Root(e,{get delayDurationMs(){return c()},get onOpenChange(){return g()},get triggerOn(){return p()},get nested(){return d()},get hasPointerEvents(){return l()},get offset(){return O()},get open(){return b()},get tippyTheme(){return`default text-tooltip-augment ${st(w)??""}`},children:(_,H)=>{var j=pr(),P=zt(j);Me.Trigger(P,{get referenceClientRect(){return C()},get class(){return x()},children:($,I)=>{var F=me(),S=zt(F);Ct(S,t,"default",{},null),ct($,F)},$$slots:{default:!0}});var k=so(P,2),W=$=>{Me.Content($,{get side(){return v()},get align(){return E()},children:(I,F)=>{var S=ur();let N;var Q=Zt(S),tt=R=>{var V=me(),pt=zt(V);Ct(pt,t,"content",{},null),ct(R,V)},et=R=>{mo(R,{size:1,class:"tooltip-text",children:(V,pt)=>{var ft=uo();ye(()=>po(ft,o())),ct(V,ft)},$$slots:{default:!0}})};ke(Q,R=>{Ze(()=>n.content)?R(tt):R(et,!1)}),ye(R=>N=co(S,"",N,R),[()=>({width:s(),"min-width":i(),"max-width":a()})],Se),ct(I,S)},$$slots:{default:!0}})};ke(k,$=>{dt(o()),Ze(()=>o()||n.content)&&$(W)}),ct(_,j)},$$slots:{default:!0},$$legacy:!0}),_=>he(r,_),()=>st(r)),be(t,"requestOpen",m),be(t,"requestClose",f),ne({requestOpen:m,requestClose:f})}var fr=Nt("<div><!></div>"),lr=Nt("<div><!></div>");function wr(e,t){const n=tn(t,["children","$$slots","$$events","$$legacy"]),o=tn(n,["size","insetContent","variant","interactive","includeBackground","borderless"]);te(t,!1);const s=ve(),i=ve(),a=ve();let c=D(t,"size",8,1),p=D(t,"insetContent",8,!1),v=D(t,"variant",8,"surface"),d=D(t,"interactive",8,!1),l=D(t,"includeBackground",8,!0),O=D(t,"borderless",8,!1);jt(()=>(st(s),dt(o)),()=>{he(s,o.class)}),jt(()=>(dt(c()),dt(v()),dt(p()),dt(d()),dt(l()),dt(O()),st(s)),()=>{he(i,["c-card",`c-card--size-${c()}`,`c-card--${v()}`,p()?"c-card--insetContent":"",d()?"c-card--interactive":"",l()?"c-card--with-background":"",O()?"c-card--borderless":"",st(s)])}),jt(()=>st(i),()=>{he(a,{...go("accent"),class:st(i).join(" ")})}),Ie(),ee();var b=me(),E=zt(b),x=C=>{var h=fr();Ge(h,()=>({...st(a),role:"button",tabindex:"0"}),void 0,"svelte-x444gv");var r=Zt(h);Ct(r,t,"default",{},null),Y("click",h,function(m){ot.call(this,t,m)}),Y("keyup",h,function(m){ot.call(this,t,m)}),Y("keydown",h,function(m){ot.call(this,t,m)}),Y("mousedown",h,function(m){ot.call(this,t,m)}),Y("mouseover",h,function(m){ot.call(this,t,m)}),Y("focus",h,function(m){ot.call(this,t,m)}),Y("mouseleave",h,function(m){ot.call(this,t,m)}),Y("blur",h,function(m){ot.call(this,t,m)}),Y("contextmenu",h,function(m){ot.call(this,t,m)}),ct(C,h)},g=C=>{var h=lr();Ge(h,()=>({...st(a)}),void 0,"svelte-x444gv");var r=Zt(h);Ct(r,t,"default",{},null),ct(C,h)};ke(E,C=>{d()?C(x):C(g,!1)}),ct(e,b),ne()}export{wr as C,Ee as H,rr as R,xr as T,Pt as a,be as b,wt as c,rn as d,Rt as e,cr as f,ar as g,br as h,qe as o,Ft as t};
