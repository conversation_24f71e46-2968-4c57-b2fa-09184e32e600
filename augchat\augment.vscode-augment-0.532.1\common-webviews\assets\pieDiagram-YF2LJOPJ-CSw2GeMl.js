import{p as E}from"./chunk-TMUBEWPD-BAgpz-xc.js";import{T as y,O,aF as L,_ as m,g as N,s as P,a as V,b as G,o as I,p as _,l as F,c as q,E as H,I as J,a4 as K,e as Q,x as U,G as X}from"./AugmentMessage-DECje7TL.js";import{p as Y}from"./gitGraph-YCYPL57B-Jv_l0-wt.js";import{d as B}from"./arc-DZWMAysg.js";import{o as Z}from"./ordinal-_rw2EY4v.js";import"./legacy-YP6Kq8lu.js";import"./index-DOexUbEr.js";import"./SpinnerAugment-Dpcl1cXc.js";import"./CalloutAugment-0Y9u1WCc.js";import"./CardAugment-YBzgmAzG.js";import"./IconButtonAugment-CbpcmeFk.js";import"./host-BNehKqab.js";import"./event-modifiers-Bz4QCcZc.js";import"./index-CKSGO-M1.js";import"./async-messaging-gS_K9w3p.js";import"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-BNqj-rl6.js";import"./input-C2nR_fsN.js";import"./BaseTextInput-Br9yLRnx.js";import"./lodash-DfmeyYaq.js";import"./svelte-component-Uytug4gU.js";import"./Filespan-Dfz0pJHr.js";import"./index-4vhrZf9p.js";import"./diff-operations-BqCUC_IY.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-hRm--fCg.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-_jaOgw08.js";import"./message-broker-DRrss2z_.js";import"./types-CGlLNakm.js";import"./file-type-utils-D6OEcQY2.js";import"./chat-model-context-DZ2DTs5O.js";import"./tool-types-Chbmg_E2.js";import"./await-NDiL5Mzl.js";import"./OpenFileButton-DBqHwR-Z.js";import"./index-B528snJk.js";import"./remote-agents-client-DbhVjGoZ.js";import"./ra-diff-ops-model-BNum2ZUy.js";import"./TextAreaAugment-DXi02sx3.js";import"./ButtonAugment-DkEdzEZO.js";import"./CollapseButtonAugment-DFL7wB0Y.js";import"./partner-mcp-utils-Bk5-h15i.js";import"./MaterialIcon-j5PxZ6X_.js";import"./CopyButton-Cl2sCTw_.js";import"./copy-ChvqXPeP.js";import"./ellipsis-CW5cyp36.js";import"./LanguageIcon-D5Xb9jVX.js";import"./augment-logo-D8bZBTPs.js";import"./_baseUniq-oWvNbgLk.js";import"./_basePickBy-DEgJFl3R.js";import"./clone-Bz17jbTq.js";import"./init-g68aIKmP.js";function tt(t,r){return r<t?-1:r>t?1:r>=t?0:NaN}function et(t){return t}var rt=X.pie,R={sections:new Map,showData:!1},M=R.sections,z=R.showData,at=structuredClone(rt),W={getConfig:m(()=>structuredClone(at),"getConfig"),clear:m(()=>{M=new Map,z=R.showData,U()},"clear"),setDiagramTitle:_,getDiagramTitle:I,setAccTitle:G,getAccTitle:V,setAccDescription:P,getAccDescription:N,addSection:m(({label:t,value:r})=>{M.has(t)||(M.set(t,r),F.debug(`added new section: ${t}, with value: ${r}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{z=t},"setShowData"),getShowData:m(()=>z,"getShowData")},it=m((t,r)=>{E(t,r),r.setShowData(t.showData),t.sections.map(r.addSection)},"populateDb"),nt={parse:m(async t=>{const r=await Y("pie",t);F.debug(r),it(r,W)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),pt=m(t=>{const r=[...t.entries()].map(p=>({label:p[0],value:p[1]})).sort((p,u)=>u.value-p.value);return function(){var p=et,u=tt,c=null,w=y(0),S=y(O),$=y(0);function a(e){var i,s,n,T,g,l=(e=L(e)).length,v=0,A=new Array(l),d=new Array(l),f=+w.apply(this,arguments),C=Math.min(O,Math.max(-O,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/l,$.apply(this,arguments)),b=h*(C<0?-1:1);for(i=0;i<l;++i)(g=d[A[i]=i]=+p(e[i],i,e))>0&&(v+=g);for(u!=null?A.sort(function(x,D){return u(d[x],d[D])}):c!=null&&A.sort(function(x,D){return c(e[x],e[D])}),i=0,n=v?(C-l*b)/v:0;i<l;++i,f=T)s=A[i],T=f+((g=d[s])>0?g*n:0)+b,d[s]={data:e[s],index:i,value:g,startAngle:f,endAngle:T,padAngle:h};return d}return a.value=function(e){return arguments.length?(p=typeof e=="function"?e:y(+e),a):p},a.sortValues=function(e){return arguments.length?(u=e,c=null,a):u},a.sort=function(e){return arguments.length?(c=e,u=null,a):c},a.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),a):w},a.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),a):S},a.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),a):$},a}().value(p=>p.value)(r)},"createPieArcs"),se={parser:nt,db:W,renderer:{draw:m((t,r,p,u)=>{F.debug(`rendering pie chart
`+t);const c=u.db,w=q(),S=H(c.getConfig(),w.pie),$=18,a=450,e=a,i=J(r),s=i.append("g");s.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[T]=K(n.pieOuterStrokeWidth);T??(T=2);const g=S.textPosition,l=Math.min(e,a)/2-40,v=B().innerRadius(0).outerRadius(l),A=B().innerRadius(l*g).outerRadius(l*g);s.append("circle").attr("cx",0).attr("cy",0).attr("r",l+T/2).attr("class","pieOuterCircle");const d=c.getSections(),f=pt(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=Z(C);s.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),s.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+A.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),s.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=s.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:j}=o.data;return c.getShowData()?`${k} [${j}]`:k});const D=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));i.attr("viewBox",`0 0 ${D} 450`),Q(i,a,D,S.useMaxWidth)},"draw")},styles:ot};export{se as diagram};
