#!/usr/bin/env node

/**
 * 分析数据库中的实际键顺序
 * 直接使用我们已知的工作区路径
 */

const fs = require('fs');
const path = require('path');
const level = require('level');

// 分析数据库中的键顺序
async function analyzeDatabaseOrder() {
    console.log('🔍 分析数据库中的键顺序...\n');
    
    // 使用已知的工作区路径
    const knownWorkspaces = [
        'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\219eaf1da08a5e8387de19f31f58d75e\\augmentcode.augment'
    ];
    
    const conversationId = 'b78bd351-4c2b-4891-9ba8-f1d7a869d74b';
    
    for (const workspacePath of knownWorkspaces) {
        console.log(`🔍 分析工作区: ${workspacePath}`);
        
        const dbPath = path.join(workspacePath, 'conversations');
        
        if (!fs.existsSync(dbPath)) {
            console.log('   ❌ 数据库路径不存在');
            continue;
        }
        
        try {
            const db = level(dbPath, { valueEncoding: 'json' });
            
            console.log('   📊 正在读取数据库...');
            
            // 收集所有相关的键和数据
            const allKeys = [];
            const conversationData = [];
            
            for await (const [key, value] of db.iterator()) {
                allKeys.push(key);
                
                if (key.startsWith(`exchange:${conversationId}:`)) {
                    const parts = key.split(':');
                    const exchangeId = parts[2];
                    
                    conversationData.push({
                        key: key,
                        exchangeId: exchangeId,
                        value: value,
                        keyIndex: allKeys.length - 1, // 在数据库中的位置
                        timestamp: value.timestamp || null,
                        request_message: (value.request_message || '').substring(0, 100),
                        response_text: (value.response_text || '').substring(0, 100)
                    });
                }
            }
            
            await db.close();
            
            console.log(`   📊 总键数: ${allKeys.length}`);
            console.log(`   📊 目标对话键数: ${conversationData.length}\n`);
            
            if (conversationData.length === 0) {
                console.log('   ❌ 未找到目标对话数据');
                continue;
            }
            
            // 分析数据库中的自然顺序
            console.log('🔍 数据库中的自然顺序分析:');
            console.log('================================');
            
            // 按在数据库中出现的顺序排序
            conversationData.sort((a, b) => a.keyIndex - b.keyIndex);
            
            console.log('前10条记录（数据库自然顺序）:');
            conversationData.slice(0, 10).forEach((item, index) => {
                console.log(`${index + 1}. exchangeId: ${item.exchangeId}`);
                console.log(`   键索引: ${item.keyIndex}`);
                console.log(`   请求: ${item.request_message}...`);
                console.log(`   响应: ${item.response_text}...`);
                console.log('');
            });
            
            console.log('最后5条记录（数据库自然顺序）:');
            conversationData.slice(-5).forEach((item, index) => {
                const actualIndex = conversationData.length - 5 + index;
                console.log(`${actualIndex + 1}. exchangeId: ${item.exchangeId}`);
                console.log(`   键索引: ${item.keyIndex}`);
                console.log(`   请求: ${item.request_message}...`);
                console.log(`   响应: ${item.response_text}...`);
                console.log('');
            });
            
            // 分析exchangeId的模式
            console.log('🔍 ExchangeId模式分析:');
            console.log('================================');
            
            const exchangeIds = conversationData.map(item => item.exchangeId);
            const patterns = analyzeExchangeIdPatterns(exchangeIds);
            
            console.log(`总数: ${exchangeIds.length}`);
            console.log(`纯数字: ${patterns.numeric.length} (${(patterns.numeric.length/exchangeIds.length*100).toFixed(1)}%)`);
            console.log(`UUID格式: ${patterns.uuid.length} (${(patterns.uuid.length/exchangeIds.length*100).toFixed(1)}%)`);
            console.log(`temp-fe格式: ${patterns.tempFe.length} (${(patterns.tempFe.length/exchangeIds.length*100).toFixed(1)}%)`);
            console.log(`其他格式: ${patterns.other.length} (${(patterns.other.length/exchangeIds.length*100).toFixed(1)}%)`);
            
            // 测试不同的排序方法
            console.log('\n🧪 测试不同排序方法的效果:');
            console.log('================================');
            
            await testSortingMethods(conversationData);
            
            // 推断最佳排序方法
            console.log('\n💡 推断最佳排序方法:');
            console.log('================================');
            
            const recommendation = inferBestSorting(conversationData, patterns);
            console.log(recommendation);
            
            return conversationData;
            
        } catch (error) {
            console.log(`   ❌ 分析失败: ${error.message}`);
        }
    }
    
    return null;
}

// 分析exchangeId模式
function analyzeExchangeIdPatterns(exchangeIds) {
    const patterns = {
        numeric: [],
        uuid: [],
        tempFe: [],
        other: []
    };
    
    for (const id of exchangeIds) {
        if (/^\d+$/.test(id)) {
            patterns.numeric.push(id);
        } else if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)) {
            patterns.uuid.push(id);
        } else if (/^temp-fe-/.test(id)) {
            patterns.tempFe.push(id);
        } else {
            patterns.other.push(id);
        }
    }
    
    return patterns;
}

// 测试不同的排序方法
async function testSortingMethods(conversationData) {
    const originalOrder = [...conversationData];
    
    // 1. 按exchangeId字符串排序
    const stringSort = [...conversationData].sort((a, b) => a.exchangeId.localeCompare(b.exchangeId));
    console.log('1. 字符串排序结果（前5个）:');
    stringSort.slice(0, 5).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.exchangeId} - ${item.request_message.substring(0, 50)}...`);
    });
    
    // 2. 按数字排序（如果可能）
    const numericItems = conversationData.filter(item => /^\d+$/.test(item.exchangeId));
    if (numericItems.length > 0) {
        const numericSort = numericItems.sort((a, b) => parseInt(a.exchangeId, 10) - parseInt(b.exchangeId, 10));
        console.log('\n2. 数字排序结果（前5个）:');
        numericSort.slice(0, 5).forEach((item, index) => {
            console.log(`   ${index + 1}. ${item.exchangeId} - ${item.request_message.substring(0, 50)}...`);
        });
    }
    
    // 3. 按UUID第一部分排序
    const uuidItems = conversationData.filter(item => /^[0-9a-f]{8}-/.test(item.exchangeId));
    if (uuidItems.length > 0) {
        const uuidSort = uuidItems.sort((a, b) => {
            const aHex = parseInt(a.exchangeId.split('-')[0], 16);
            const bHex = parseInt(b.exchangeId.split('-')[0], 16);
            return aHex - bHex;
        });
        console.log('\n3. UUID十六进制排序结果（前5个）:');
        uuidSort.slice(0, 5).forEach((item, index) => {
            const hexValue = parseInt(item.exchangeId.split('-')[0], 16);
            console.log(`   ${index + 1}. ${item.exchangeId} (${hexValue}) - ${item.request_message.substring(0, 50)}...`);
        });
    }
    
    // 4. 数据库自然顺序（已经是这个顺序）
    console.log('\n4. 数据库自然顺序（前5个）:');
    originalOrder.slice(0, 5).forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.exchangeId} (索引${item.keyIndex}) - ${item.request_message.substring(0, 50)}...`);
    });
}

// 推断最佳排序方法
function inferBestSorting(conversationData, patterns) {
    let recommendation = '';
    
    // 分析数据库顺序是否有逻辑
    const keyIndices = conversationData.map(item => item.keyIndex);
    const isSequential = keyIndices.every((index, i) => i === 0 || index > keyIndices[i-1]);
    
    if (isSequential) {
        recommendation += '✅ 数据库自然顺序是连续的，这很可能就是插件使用的顺序\n';
        recommendation += '   建议: 使用LevelDB iterator的自然顺序\n';
        recommendation += '   实现: 保持数据库迭代的顺序，不进行额外排序\n';
    } else {
        recommendation += '⚠️  数据库自然顺序不连续，可能需要其他排序方法\n';
    }
    
    // 分析exchangeId模式
    const totalIds = conversationData.length;
    if (patterns.numeric.length > totalIds * 0.8) {
        recommendation += '\n✅ 大部分exchangeId是数字，建议数字排序\n';
        recommendation += '   实现: parseInt(exchangeId, 10)\n';
    } else if (patterns.uuid.length > totalIds * 0.8) {
        recommendation += '\n✅ 大部分exchangeId是UUID，建议UUID排序\n';
        recommendation += '   实现: parseInt(exchangeId.split("-")[0], 16)\n';
    }
    
    // 检查是否有时间戳
    const withTimestamp = conversationData.filter(item => item.timestamp);
    if (withTimestamp.length > 0) {
        recommendation += `\n📊 ${withTimestamp.length}/${totalIds} 条记录有timestamp\n`;
        if (withTimestamp.length > totalIds * 0.5) {
            recommendation += '   建议: 优先使用timestamp排序\n';
        }
    }
    
    return recommendation;
}

// 主函数
async function main() {
    console.log('🔧 数据库键顺序分析工具');
    console.log('================================\n');
    
    try {
        const result = await analyzeDatabaseOrder();
        
        if (result) {
            console.log('\n🎯 关键发现:');
            console.log('================================');
            console.log('1. 分析了数据库中的实际键顺序');
            console.log('2. 测试了多种排序方法');
            console.log('3. 提供了最佳排序方法的建议');
            console.log('4. 这些信息可以用来修复我们的导出工具');
        } else {
            console.log('❌ 无法分析数据库，请检查路径和权限');
        }
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
    }
}

// 运行分析
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { analyzeDatabaseOrder };
