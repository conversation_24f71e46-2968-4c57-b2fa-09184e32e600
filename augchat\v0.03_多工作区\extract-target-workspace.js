#!/usr/bin/env node

/**
 * 专门提取目标工作区的完整聊天记录
 * 工作区: 219eaf1da08a5e8387de19f31f58d75e (包含当前对话)
 */

const fs = require('fs');
const path = require('path');

// 目标工作区路径
const TARGET_WORKSPACE = 'C:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\workspaceStorage\\219eaf1da08a5e8387de19f31f58d75e\\Augment.vscode-augment\\augment-kv-store';

// 配置
const CONFIG = {
    outputDir: path.join(__dirname, 'TargetWorkspaceConversations'),
    tempDir: path.join(__dirname, 'temp_target_workspace'),
    workspaceId: '219eaf1da08a5e8387de19f31f58d75e'
};

// Level库导入
let Level;
try {
    const levelModule = require('level');
    Level = levelModule.Level || levelModule.default || levelModule;
} catch (error) {
    console.error('❌ 无法加载level库:', error.message);
    process.exit(1);
}

// 工具函数
const utils = {
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`✅ 创建目录: ${dirPath}`);
        }
    },

    formatDate() {
        return new Date().toISOString().replace(/[:.]/g, '-').split('.')[0];
    },

    copyDatabase(sourcePath, targetPath) {
        try {
            if (fs.existsSync(sourcePath)) {
                const files = fs.readdirSync(sourcePath);
                utils.ensureDir(targetPath);
                
                for (const file of files) {
                    if (file !== 'LOCK') { // 跳过锁文件
                        const sourceFile = path.join(sourcePath, file);
                        const targetFile = path.join(targetPath, file);
                        fs.copyFileSync(sourceFile, targetFile);
                    }
                }
                return true;
            }
        } catch (error) {
            console.warn(`⚠️  复制数据库失败: ${error.message}`);
        }
        return false;
    }
};

// 使用strings方法提取文本内容
function extractWithStrings(dbPath) {
    console.log(`🔍 使用strings方法提取文本内容...`);
    
    try {
        const files = fs.readdirSync(dbPath);
        const conversations = [];
        
        for (const file of files) {
            if (file.endsWith('.log') || file.endsWith('.ldb')) {
                const filePath = path.join(dbPath, file);
                const fileStats = fs.statSync(filePath);
                
                console.log(`📄 处理文件: ${file} (${(fileStats.size / 1024 / 1024).toFixed(2)}MB)`);
                
                try {
                    // 读取文件内容
                    const content = fs.readFileSync(filePath, 'binary');
                    
                    // 提取可读字符串
                    const strings = [];
                    let currentString = '';
                    
                    for (let i = 0; i < content.length; i++) {
                        const char = content.charCodeAt(i);
                        
                        // 可打印ASCII字符
                        if (char >= 32 && char <= 126) {
                            currentString += String.fromCharCode(char);
                        } else {
                            if (currentString.length >= 4) { // 最少4个字符
                                strings.push(currentString);
                            }
                            currentString = '';
                        }
                    }
                    
                    // 处理最后一个字符串
                    if (currentString.length >= 4) {
                        strings.push(currentString);
                    }
                    
                    console.log(`📊 从 ${file} 提取了 ${strings.length} 个字符串`);
                    
                    // 查找JSON数据
                    for (let i = 0; i < strings.length; i++) {
                        const str = strings[i];
                        
                        // 查找包含对话相关内容的字符串
                        if (str.includes('conversationId') || 
                            str.includes('request_message') || 
                            str.includes('response_text') ||
                            str.includes('导出') ||
                            str.includes('markdown') ||
                            str.includes('预览')) {
                            
                            try {
                                // 尝试提取JSON
                                const jsonMatch = str.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/g);
                                if (jsonMatch) {
                                    for (const jsonStr of jsonMatch) {
                                        try {
                                            const parsed = JSON.parse(jsonStr);
                                            conversations.push({
                                                source: file,
                                                lineNumber: i + 1,
                                                data: parsed,
                                                rawString: str.substring(0, 500) // 保留前500字符
                                            });
                                        } catch {
                                            // JSON解析失败，继续
                                        }
                                    }
                                } else {
                                    // 不是JSON但包含关键词，也保存
                                    conversations.push({
                                        source: file,
                                        lineNumber: i + 1,
                                        data: { rawText: str },
                                        rawString: str.substring(0, 500)
                                    });
                                }
                            } catch (parseError) {
                                // 解析错误，跳过
                            }
                        }
                    }
                    
                } catch (error) {
                    console.warn(`⚠️  处理文件 ${file} 时出错: ${error.message}`);
                }
            }
        }
        
        console.log(`📊 Strings方法提取完成: ${conversations.length} 条记录`);
        return conversations;
        
    } catch (error) {
        console.error(`❌ Strings提取失败: ${error.message}`);
        return [];
    }
}

// 使用LevelDB方法提取数据
async function extractWithLevelDB(dbPath) {
    console.log(`🔍 使用LevelDB方法提取数据...`);
    
    try {
        // 创建临时数据库副本
        const tempDbPath = path.join(CONFIG.tempDir, 'target_db');
        if (!utils.copyDatabase(dbPath, tempDbPath)) {
            console.log(`⚠️  无法复制数据库，跳过LevelDB方法`);
            return [];
        }

        const conversations = [];
        
        try {
            const db = new Level(tempDbPath, { 
                createIfMissing: false,
                errorIfExists: false,
                valueEncoding: 'json'
            });
            
            console.log(`📊 开始扫描LevelDB数据库...`);
            
            const iterator = db.iterator();
            let count = 0;
            
            for await (const [key, value] of iterator) {
                count++;
                
                try {
                    const keyStr = key.toString();
                    
                    // 查找对话相关的键
                    if (keyStr.includes('exchange:') || 
                        keyStr.includes('conversation') ||
                        keyStr.includes('metadata:')) {
                        
                        conversations.push({
                            source: 'leveldb',
                            key: keyStr,
                            value: value,
                            timestamp: new Date().toISOString()
                        });
                        
                        console.log(`🎯 发现对话数据: ${keyStr.substring(0, 100)}...`);
                    }
                    
                    // 每1000条记录显示进度
                    if (count % 1000 === 0) {
                        console.log(`📊 已扫描 ${count} 条记录...`);
                    }
                    
                } catch (parseError) {
                    // 跳过解析错误的记录
                }
            }
            
            await db.close();
            console.log(`📊 LevelDB扫描完成: 总记录 ${count}, 对话记录 ${conversations.length}`);
            
        } catch (dbError) {
            console.warn(`⚠️  数据库操作失败: ${dbError.message}`);
        }
        
        // 清理临时文件
        try {
            fs.rmSync(tempDbPath, { recursive: true, force: true });
        } catch (cleanupError) {
            console.warn(`⚠️  清理临时文件失败: ${cleanupError.message}`);
        }
        
        return conversations;
        
    } catch (error) {
        console.error(`❌ LevelDB提取失败: ${error.message}`);
        return [];
    }
}

// 主函数
async function main() {
    console.log('🚀 开始提取目标工作区的完整聊天记录...');
    console.log(`📂 目标工作区: ${CONFIG.workspaceId}`);
    console.log(`📁 路径: ${TARGET_WORKSPACE}`);
    
    if (!fs.existsSync(TARGET_WORKSPACE)) {
        console.error(`❌ 目标工作区不存在: ${TARGET_WORKSPACE}`);
        return;
    }
    
    utils.ensureDir(CONFIG.outputDir);
    utils.ensureDir(CONFIG.tempDir);
    
    const results = {
        timestamp: new Date().toISOString(),
        workspaceId: CONFIG.workspaceId,
        workspacePath: TARGET_WORKSPACE,
        methods: {}
    };
    
    // 方法1: Strings提取
    console.log(`\n📝 方法1: Strings文本提取`);
    try {
        const stringsResults = extractWithStrings(TARGET_WORKSPACE);
        results.methods.strings = {
            success: true,
            results: stringsResults,
            count: stringsResults.length
        };
    } catch (error) {
        console.error(`❌ Strings方法失败: ${error.message}`);
        results.methods.strings = {
            success: false,
            error: error.message
        };
    }
    
    // 方法2: LevelDB提取
    console.log(`\n💾 方法2: LevelDB数据库提取`);
    try {
        const leveldbResults = await extractWithLevelDB(TARGET_WORKSPACE);
        results.methods.leveldb = {
            success: true,
            results: leveldbResults,
            count: leveldbResults.length
        };
    } catch (error) {
        console.error(`❌ LevelDB方法失败: ${error.message}`);
        results.methods.leveldb = {
            success: false,
            error: error.message
        };
    }
    
    // 保存结果
    const outputPath = path.join(CONFIG.outputDir, `target_workspace_${CONFIG.workspaceId}_${utils.formatDate()}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(results, null, 2), 'utf8');
    
    console.log(`\n🎉 提取完成！`);
    console.log(`📄 结果已保存到: ${outputPath}`);
    console.log(`\n📊 提取摘要:`);
    
    const stringsCount = results.methods.strings?.count || 0;
    const leveldbCount = results.methods.leveldb?.count || 0;
    const totalCount = stringsCount + leveldbCount;
    
    console.log(`   - Strings方法: ${stringsCount} 条记录`);
    console.log(`   - LevelDB方法: ${leveldbCount} 条记录`);
    console.log(`   - 总计: ${totalCount} 条记录`);
    
    if (totalCount > 0) {
        console.log(`\n🎯 成功找到目标工作区的聊天记录！`);
        console.log(`💡 下一步: 运行convert-to-markdown.js转换为可预览格式`);
    } else {
        console.log(`\n🤔 没有找到聊天记录，可能需要检查数据库状态`);
    }
    
    // 清理临时目录
    try {
        fs.rmSync(CONFIG.tempDir, { recursive: true, force: true });
        console.log(`\n🧹 清理临时文件完成`);
    } catch (cleanupError) {
        console.warn(`⚠️  清理临时文件失败: ${cleanupError.message}`);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { main };
