#!/usr/bin/env node

/**
 * Augment聊天记录导出工具 (Node.js版本)
 * 使用level库读取LevelDB数据库
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 尝试导入level库
let Level;
try {
    const levelModule = require('level');
    Level = levelModule.Level || levelModule.default || levelModule;
} catch (error) {
    console.error('❌ 错误: 需要安装level库');
    console.error('请运行: npm install level');
    process.exit(1);
}

// 配置
const CONFIG = {
    outputDir: path.join(process.cwd(), 'AugmentExport'),
    userDataPath: path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User'),
    maxRetries: 3,
    retryDelay: 1000
};

// 工具函数
const utils = {
    // 创建目录
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`✅ 创建目录: ${dirPath}`);
        }
    },

    // 格式化日期
    formatDate(date = new Date()) {
        return date.toISOString().replace(/[:.]/g, '-').slice(0, 19);
    },

    // 安全的JSON解析
    safeJsonParse(str) {
        try {
            return JSON.parse(str);
        } catch {
            return str;
        }
    },

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
};

// 检测键的类型
function detectKeyType(keyStr) {
    if (keyStr.includes('conversation')) return 'conversation';
    if (keyStr.includes('message')) return 'message';
    if (keyStr.includes('chat')) return 'chat';
    if (keyStr.includes('user')) return 'user';
    if (keyStr.includes('session')) return 'session';
    if (keyStr.includes('dialog')) return 'dialog';
    if (keyStr.includes('history')) return 'history';
    if (keyStr.includes('config')) return 'config';
    if (keyStr.includes('state')) return 'state';
    if (keyStr.includes('cache')) return 'cache';
    if (/^[0-9a-f-]{36}$/.test(keyStr)) return 'uuid';
    if (/^\d+$/.test(keyStr)) return 'numeric';
    return 'unknown';
}

// 检测值的类型
function detectValueType(valueStr, parsedValue) {
    if (typeof parsedValue === 'object' && parsedValue !== null) {
        if (Array.isArray(parsedValue)) return 'array';
        if (parsedValue.messages) return 'conversation_object';
        if (parsedValue.content) return 'message_object';
        if (parsedValue.timestamp) return 'timestamped_object';
        return 'object';
    }
    if (valueStr.includes('"messages"')) return 'json_with_messages';
    if (valueStr.includes('"content"')) return 'json_with_content';
    if (valueStr.includes('"user"') || valueStr.includes('"assistant"')) return 'json_with_roles';
    if (/^\d+$/.test(valueStr)) return 'numeric_string';
    if (valueStr.length > 1000) return 'large_text';
    return 'text';
}

// 生成统计信息
function generateStatistics(entries) {
    const stats = {
        totalEntries: entries.length,
        keyTypes: {},
        valueTypes: {},
        conversationRelated: 0,
        largeEntries: 0,
        jsonEntries: 0
    };

    entries.forEach(entry => {
        // 统计键类型
        const keyType = entry.metadata?.keyType || 'unknown';
        stats.keyTypes[keyType] = (stats.keyTypes[keyType] || 0) + 1;

        // 统计值类型
        const valueType = entry.metadata?.valueType || 'unknown';
        stats.valueTypes[valueType] = (stats.valueTypes[valueType] || 0) + 1;

        // 统计聊天相关条目
        if (entry.metadata?.isConversationData) {
            stats.conversationRelated++;
        }

        // 统计大型条目
        if (entry.metadata?.valueLength > 1000) {
            stats.largeEntries++;
        }

        // 统计JSON条目
        if (entry.metadata?.isJson) {
            stats.jsonEntries++;
        }
    });

    return stats;
}

// 查找Augment工作区
function findAugmentWorkspaces() {
    const workspaceStoragePath = path.join(CONFIG.userDataPath, 'workspaceStorage');
    const workspaces = [];

    if (!fs.existsSync(workspaceStoragePath)) {
        console.log('⚠️  工作区存储路径不存在');
        return workspaces;
    }

    const dirs = fs.readdirSync(workspaceStoragePath, { withFileTypes: true });
    
    for (const dir of dirs) {
        if (dir.isDirectory()) {
            const augmentPath = path.join(workspaceStoragePath, dir.name, 'Augment.vscode-augment');
            const kvStorePath = path.join(augmentPath, 'augment-kv-store');
            
            if (fs.existsSync(augmentPath)) {
                workspaces.push({
                    id: dir.name,
                    path: augmentPath,
                    kvStorePath: kvStorePath,
                    hasKvStore: fs.existsSync(kvStorePath)
                });
            }
        }
    }

    return workspaces;
}

// 导出LevelDB数据
async function exportLevelDB(dbPath, outputPath, workspaceId) {
    console.log(`💾 导出工作区 ${workspaceId} 的聊天记录...`);

    let db;
    let retries = 0;

    while (retries < CONFIG.maxRetries) {
        try {
            // 尝试打开数据库
            db = new Level(dbPath, {
                createIfMissing: false,
                errorIfExists: false,
                readOnly: true
            });

            const conversations = [];
            const rawEntries = [];
            let totalEntries = 0;

            console.log(`🔍 开始扫描数据库: ${dbPath}`);

            // 读取所有键值对
            for await (const [key, value] of db.iterator()) {
                totalEntries++;

                try {
                    const keyStr = key.toString('utf8');
                    const keyHex = key.toString('hex');

                    // 尝试多种解码方式
                    let valueStr, valueHex, valueBinary;
                    try {
                        valueStr = value.toString('utf8');
                        valueHex = value.toString('hex');
                        valueBinary = value.toString('binary');
                    } catch (decodeError) {
                        console.warn(`解码值时出错: ${decodeError.message}`);
                        valueStr = '[无法解码]';
                        valueHex = value.toString('hex');
                        valueBinary = '[二进制数据]';
                    }

                    // 尝试解析为JSON
                    const parsedValue = utils.safeJsonParse(valueStr);

                    // 检查是否包含聊天相关的关键词
                    const isConversationData = keyStr.includes('conversation') ||
                                             keyStr.includes('chat') ||
                                             keyStr.includes('message') ||
                                             keyStr.includes('dialog') ||
                                             valueStr.includes('conversation') ||
                                             valueStr.includes('message') ||
                                             valueStr.includes('user') ||
                                             valueStr.includes('assistant');

                    const entry = {
                        key: keyStr,
                        keyHex: keyHex,
                        value: parsedValue,
                        valueRaw: valueStr,
                        valueHex: valueHex,
                        valueBinary: valueBinary.length > 1000 ? '[大型二进制数据]' : valueBinary,
                        timestamp: new Date().toISOString(),
                        metadata: {
                            keyLength: key.length,
                            valueLength: value.length,
                            isJson: typeof parsedValue === 'object',
                            isConversationData: isConversationData,
                            keyType: detectKeyType(keyStr),
                            valueType: detectValueType(valueStr, parsedValue)
                        }
                    };

                    if (isConversationData) {
                        conversations.push(entry);
                        console.log(`🎯 发现可能的聊天数据: ${keyStr.substring(0, 50)}...`);
                    }

                    rawEntries.push(entry);

                } catch (error) {
                    console.warn(`⚠️  处理键值对时出错: ${error.message}`);
                    rawEntries.push({
                        key: '[解析错误]',
                        keyHex: key.toString('hex'),
                        value: '[解析错误]',
                        valueHex: value.toString('hex'),
                        error: error.message
                    });
                }
            }

            await db.close();

            console.log(`📊 数据库扫描完成:`);
            console.log(`   - 总条目数: ${totalEntries}`);
            console.log(`   - 可能的聊天记录: ${conversations.length}`);
            console.log(`   - 所有数据条目: ${rawEntries.length}`);

            // 保存详细的导出数据
            const exportData = {
                exportInfo: {
                    workspaceId: workspaceId,
                    exportDate: new Date().toISOString(),
                    totalEntries: totalEntries,
                    conversationEntries: conversations.length,
                    dbPath: dbPath,
                    version: '2.0.0'
                },
                conversations: conversations,
                allEntries: rawEntries,
                statistics: generateStatistics(rawEntries)
            };

            fs.writeFileSync(outputPath, JSON.stringify(exportData, null, 2), 'utf8');

            // 同时保存一个简化版本，只包含可能的聊天记录
            const conversationOnlyPath = outputPath.replace('.json', '_conversations_only.json');
            fs.writeFileSync(conversationOnlyPath, JSON.stringify({
                exportInfo: exportData.exportInfo,
                conversations: conversations
            }, null, 2), 'utf8');

            console.log(`✅ 成功导出 ${conversations.length} 条聊天记录到: ${outputPath}`);
            console.log(`📝 详细数据已保存到: ${outputPath}`);
            console.log(`💬 聊天记录已保存到: ${conversationOnlyPath}`);

            return { success: true, count: conversations.length, totalEntries: totalEntries };

        } catch (error) {
            retries++;
            console.warn(`⚠️  尝试 ${retries}/${CONFIG.maxRetries} 失败: ${error.message}`);

            if (db) {
                try {
                    await db.close();
                } catch (closeError) {
                    console.warn(`关闭数据库时出错: ${closeError.message}`);
                }
            }

            if (retries < CONFIG.maxRetries) {
                console.log(`等待 ${CONFIG.retryDelay}ms 后重试...`);
                await utils.delay(CONFIG.retryDelay);
            }
        }
    }

    console.error(`❌ 导出失败，已重试 ${CONFIG.maxRetries} 次`);
    return { success: false, error: '达到最大重试次数' };
}

// 复制文件夹结构
function copyFolderStructure(sourcePath, destPath) {
    try {
        if (!fs.existsSync(sourcePath)) {
            return { success: false, error: '源路径不存在' };
        }
        
        // 递归复制
        function copyRecursive(src, dest) {
            const stats = fs.statSync(src);
            
            if (stats.isDirectory()) {
                utils.ensureDir(dest);
                const items = fs.readdirSync(src);
                
                for (const item of items) {
                    copyRecursive(path.join(src, item), path.join(dest, item));
                }
            } else {
                fs.copyFileSync(src, dest);
            }
        }
        
        copyRecursive(sourcePath, destPath);
        console.log(`✅ 已复制文件夹: ${sourcePath} -> ${destPath}`);
        return { success: true };
        
    } catch (error) {
        console.error(`❌ 复制文件夹失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 生成导出报告
function generateReport(outputDir, workspaces, results) {
    const reportPath = path.join(outputDir, 'export_report.json');
    
    const report = {
        exportInfo: {
            date: new Date().toISOString(),
            outputPath: outputDir,
            nodeVersion: process.version,
            platform: process.platform
        },
        summary: {
            totalWorkspaces: workspaces.length,
            workspacesWithKvStore: workspaces.filter(w => w.hasKvStore).length,
            successfulExports: Object.values(results).filter(r => r.success).length
        },
        workspaces: workspaces,
        results: results
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
    console.log(`📊 导出报告已保存: ${reportPath}`);
}

// 主函数
async function main() {
    console.log('🚀 开始导出Augment聊天记录...');
    console.log(`📂 输出目录: ${CONFIG.outputDir}`);
    
    // 创建输出目录
    utils.ensureDir(CONFIG.outputDir);
    
    // 查找工作区
    const workspaces = findAugmentWorkspaces();
    console.log(`🔍 找到 ${workspaces.length} 个Augment工作区`);
    
    if (workspaces.length === 0) {
        console.log('❌ 未找到任何Augment工作区');
        return;
    }
    
    const results = {};
    
    // 导出每个工作区
    for (const workspace of workspaces) {
        console.log(`\n🔄 处理工作区: ${workspace.id}`);
        
        const workspaceOutputDir = path.join(CONFIG.outputDir, 'workspaces', workspace.id);
        utils.ensureDir(workspaceOutputDir);
        
        // 复制文件夹结构
        const copyResult = copyFolderStructure(workspace.path, workspaceOutputDir);
        
        // 导出聊天记录
        let exportResult = { success: false };
        if (workspace.hasKvStore) {
            const chatHistoryPath = path.join(workspaceOutputDir, 'chat_history.json');
            exportResult = await exportLevelDB(workspace.kvStorePath, chatHistoryPath, workspace.id);
        } else {
            console.log('⚠️  该工作区没有聊天记录数据库');
        }
        
        results[workspace.id] = {
            folderCopied: copyResult.success,
            chatExported: exportResult.success,
            hasKvStore: workspace.hasKvStore,
            chatCount: exportResult.count || 0,
            error: copyResult.error || exportResult.error
        };
    }
    
    // 生成报告
    generateReport(CONFIG.outputDir, workspaces, results);
    
    console.log('\n🎉 导出完成！');
    console.log(`📂 请查看导出目录: ${CONFIG.outputDir}`);
    
    // 显示摘要
    const successful = Object.values(results).filter(r => r.chatExported).length;
    const totalChats = Object.values(results).reduce((sum, r) => sum + (r.chatCount || 0), 0);
    
    console.log(`\n📊 导出摘要:`);
    console.log(`   - 成功导出: ${successful}/${workspaces.length} 个工作区`);
    console.log(`   - 总聊天记录: ${totalChats} 条`);
}

// 错误处理
process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error.message);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    process.exit(1);
});

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { main, findAugmentWorkspaces, exportLevelDB };
