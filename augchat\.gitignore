# Generated by Cargo
# will have compiled files and executables
debug/
target/
__pycache__/
hudet/
cv/
layout_app.py
api/flask_session

# Remove Cargo.lock from gitignore if creating an executable, leave it for libraries
# More information here https://doc.rust-lang.org/cargo/guide/cargo-toml-vs-cargo-lock.html
Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb
*.trie

.idea/
.vscode/

# Exclude Mac generated files
.DS_Store

# Exclude the log folder
docker/ragflow-logs/
/flask_session
/logs
rag/res/deepdoc

# Exclude sdk generated files
sdk/python/ragflow.egg-info/
sdk/python/build/
sdk/python/dist/
sdk/python/ragflow_sdk.egg-info/
huggingface.co/
nltk_data/

# Exclude hash-like temporary files like 9b5ad71b2ce5302211f9c61530b329a4922fc6a4
*[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]*
.lh/


.github/
.history/
.specstory
.venv/
web/node_modules/
web/src/assets/
sdk/


**/node_modules/
