@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   Augment聊天记录导出工具
echo ========================================
echo.

:MENU
echo 请选择导出方式:
echo.
echo [1] 使用PowerShell脚本 (推荐)
echo [2] 使用Node.js脚本
echo [3] 仅复制原始文件
echo [4] 查看帮助
echo [0] 退出
echo.
set /p choice="请输入选择 (0-4): "

if "%choice%"=="1" goto POWERSHELL
if "%choice%"=="2" goto NODEJS
if "%choice%"=="3" goto COPY_ONLY
if "%choice%"=="4" goto HELP
if "%choice%"=="0" goto EXIT
goto MENU

:POWERSHELL
echo.
echo 🚀 使用PowerShell脚本导出...
echo.
powershell -ExecutionPolicy Bypass -File "Export-AugmentChatHistory.ps1"
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ PowerShell导出完成！
) else (
    echo.
    echo ❌ PowerShell导出失败，错误代码: %ERRORLEVEL%
)
pause
goto MENU

:NODEJS
echo.
echo 🚀 使用Node.js脚本导出...
echo.
echo 检查Node.js环境...
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    goto MENU
)

echo 检查level库...
node -e "require('level')" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️  level库未安装，正在安装...
    npm install level
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ level库安装失败
        pause
        goto MENU
    )
)

node export-augment-chat.js
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Node.js导出完成！
) else (
    echo.
    echo ❌ Node.js导出失败，错误代码: %ERRORLEVEL%
)
pause
goto MENU

:COPY_ONLY
echo.
echo 📁 仅复制原始文件...
echo.
set "source=%APPDATA%\Code\User"
set "dest=%CD%\AugmentBackup_%date:~0,4%%date:~5,2%%date:~8,2%"

if not exist "%source%" (
    echo ❌ 错误: VSCode用户数据目录不存在
    echo 路径: %source%
    pause
    goto MENU
)

echo 创建备份目录: %dest%
mkdir "%dest%" 2>nul

echo 复制全局存储...
if exist "%source%\globalStorage\augment.vscode-augment" (
    xcopy "%source%\globalStorage\augment.vscode-augment" "%dest%\globalStorage\augment.vscode-augment" /E /I /H /Y
    echo ✅ 全局存储复制完成
) else (
    echo ⚠️  全局存储不存在
)

echo 复制工作区存储...
set count=0
for /d %%i in ("%source%\workspaceStorage\*") do (
    if exist "%%i\Augment.vscode-augment" (
        set /a count+=1
        echo 复制工作区: %%~ni
        xcopy "%%i\Augment.vscode-augment" "%dest%\workspaces\%%~ni\Augment.vscode-augment" /E /I /H /Y
    )
)

echo.
echo ✅ 文件复制完成！
echo 📂 备份位置: %dest%
echo 📊 复制了 %count% 个工作区
pause
goto MENU

:HELP
echo.
echo ========================================
echo   使用说明
echo ========================================
echo.
echo 1. PowerShell脚本 (推荐):
echo    - 功能最完整，支持LevelDB解析
echo    - 需要WSL和Python环境
echo    - 自动安装依赖
echo.
echo 2. Node.js脚本:
echo    - 纯JavaScript实现
echo    - 需要Node.js环境
echo    - 自动安装level库
echo.
echo 3. 仅复制文件:
echo    - 最简单的方式
echo    - 只复制原始文件，不解析数据库
echo    - 适合备份和迁移
echo.
echo 导出位置:
echo    - PowerShell: C:\AugmentExport
echo    - Node.js: 当前目录\AugmentExport
echo    - 复制文件: 当前目录\AugmentBackup_日期
echo.
echo 注意事项:
echo    - 请确保VSCode已关闭
echo    - 聊天记录存储在LevelDB数据库中
echo    - 每个工作区有独立的聊天记录
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 感谢使用Augment聊天记录导出工具！
echo.
exit /b 0
