#!/usr/bin/env node

/**
 * 对话数据调试脚本
 * 用于分析对话解析过程中的问题
 */

const fs = require('fs');
const path = require('path');

// 分析最新导出目录中的文件
function analyzeExportedFiles() {
    console.log('🔍 分析导出文件中的对话数据...\n');
    
    // 查找最新的导出目录
    const currentDir = __dirname;
    const files = fs.readdirSync(currentDir);
    
    let latestExportDir = null;
    for (const file of files) {
        if (file.startsWith('conversations_export_') && fs.statSync(path.join(currentDir, file)).isDirectory()) {
            if (!latestExportDir || file > latestExportDir) {
                latestExportDir = file;
            }
        }
    }
    
    if (!latestExportDir) {
        console.log('❌ 未找到导出目录');
        return;
    }
    
    console.log(`📂 分析目录: ${latestExportDir}`);
    
    // 分析所有.md文件
    const exportPath = path.join(currentDir, latestExportDir);
    const exportFiles = fs.readdirSync(exportPath).filter(f => f.endsWith('.md') && f !== 'README.md');
    
    console.log(`📄 发现 ${exportFiles.length} 个对话文件\n`);
    
    const fileAnalysis = [];
    
    for (const file of exportFiles) {
        console.log(`🔍 分析文件: ${file}`);
        
        const filePath = path.join(exportPath, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 提取对话ID
        const conversationIdPattern = /- \*\*对话ID\*\*: `([a-f0-9-]+)`/g;
        const conversationIds = [];
        let match;
        
        while ((match = conversationIdPattern.exec(content)) !== null) {
            conversationIds.push(match[1]);
        }
        
        // 提取消息序号
        const messagePattern = /### 消息 (\d+)/g;
        const messageNumbers = [];
        
        while ((match = messagePattern.exec(content)) !== null) {
            messageNumbers.push(parseInt(match[1], 10));
        }
        
        // 分析结果
        const uniqueConversationIds = [...new Set(conversationIds)];
        const analysis = {
            filename: file,
            totalConversationIds: conversationIds.length,
            uniqueConversationIds: uniqueConversationIds.length,
            conversationIds: uniqueConversationIds,
            totalMessages: messageNumbers.length,
            messageRange: messageNumbers.length > 0 ? {
                min: Math.min(...messageNumbers),
                max: Math.max(...messageNumbers)
            } : null,
            hasMultipleConversations: uniqueConversationIds.length > 1,
            messageSequenceIssues: 0
        };
        
        // 检查消息序号连续性
        for (let i = 1; i < messageNumbers.length; i++) {
            if (messageNumbers[i] !== messageNumbers[i-1] + 1) {
                analysis.messageSequenceIssues++;
            }
        }
        
        fileAnalysis.push(analysis);
        
        // 输出分析结果
        console.log(`   - 对话ID数量: ${analysis.totalConversationIds} (唯一: ${analysis.uniqueConversationIds})`);
        console.log(`   - 消息数量: ${analysis.totalMessages}`);
        if (analysis.messageRange) {
            console.log(`   - 消息范围: ${analysis.messageRange.min} - ${analysis.messageRange.max}`);
        }
        console.log(`   - 序号不连续: ${analysis.messageSequenceIssues}`);
        
        if (analysis.hasMultipleConversations) {
            console.log(`   ⚠️  包含多个对话: ${analysis.conversationIds.join(', ')}`);
        }
        
        if (analysis.messageSequenceIssues > 0) {
            console.log(`   ⚠️  消息序号不连续: ${analysis.messageSequenceIssues} 处`);
        }
        
        console.log('');
    }
    
    // 总结分析
    console.log('📊 总结分析:');
    console.log('================================');
    
    const filesWithMultipleConversations = fileAnalysis.filter(f => f.hasMultipleConversations);
    const filesWithSequenceIssues = fileAnalysis.filter(f => f.messageSequenceIssues > 0);
    
    console.log(`总文件数: ${fileAnalysis.length}`);
    console.log(`包含多个对话的文件: ${filesWithMultipleConversations.length}`);
    console.log(`有序号问题的文件: ${filesWithSequenceIssues.length}`);
    
    if (filesWithMultipleConversations.length > 0) {
        console.log('\n⚠️  包含多个对话的文件:');
        filesWithMultipleConversations.forEach(f => {
            console.log(`   - ${f.filename}: ${f.conversationIds.join(', ')}`);
        });
    }
    
    if (filesWithSequenceIssues.length > 0) {
        console.log('\n⚠️  有序号问题的文件:');
        filesWithSequenceIssues.forEach(f => {
            console.log(`   - ${f.filename}: ${f.messageSequenceIssues} 个不连续处`);
        });
    }
    
    // 检查是否有重复的对话ID出现在不同文件中
    const allConversationIds = [];
    fileAnalysis.forEach(f => {
        f.conversationIds.forEach(id => {
            allConversationIds.push({ id, filename: f.filename });
        });
    });
    
    const conversationIdCounts = new Map();
    allConversationIds.forEach(item => {
        if (!conversationIdCounts.has(item.id)) {
            conversationIdCounts.set(item.id, []);
        }
        conversationIdCounts.get(item.id).push(item.filename);
    });
    
    const duplicateConversationIds = [];
    for (const [id, files] of conversationIdCounts) {
        if (files.length > 1) {
            duplicateConversationIds.push({ id, files });
        }
    }
    
    if (duplicateConversationIds.length > 0) {
        console.log('\n⚠️  重复的对话ID:');
        duplicateConversationIds.forEach(item => {
            console.log(`   - ${item.id}: 出现在 ${item.files.length} 个文件中`);
            item.files.forEach(file => console.log(`     * ${file}`));
        });
    }
    
    return {
        totalFiles: fileAnalysis.length,
        filesWithMultipleConversations: filesWithMultipleConversations.length,
        filesWithSequenceIssues: filesWithSequenceIssues.length,
        duplicateConversationIds: duplicateConversationIds.length
    };
}

// 主函数
function main() {
    console.log('🔧 对话数据调试工具');
    console.log('================================\n');
    
    const analysis = analyzeExportedFiles();
    
    if (analysis) {
        console.log('\n💡 问题诊断:');
        console.log('================================');
        
        if (analysis.filesWithMultipleConversations > 0) {
            console.log('❌ 发现文件包含多个对话 - 这表明文件名生成或对话分组有问题');
        }
        
        if (analysis.filesWithSequenceIssues > 0) {
            console.log('❌ 发现消息序号不连续 - 这可能是由于多个对话合并导致的');
        }
        
        if (analysis.duplicateConversationIds > 0) {
            console.log('❌ 发现重复的对话ID - 这表明同一个对话被分散到多个文件中');
        }
        
        if (analysis.filesWithMultipleConversations === 0 && 
            analysis.filesWithSequenceIssues === 0 && 
            analysis.duplicateConversationIds === 0) {
            console.log('✅ 未发现明显问题');
        }
        
        console.log('\n🔧 建议修复方案:');
        if (analysis.filesWithMultipleConversations > 0) {
            console.log('1. 检查对话解析逻辑，确保每个对话ID只生成一个文件');
            console.log('2. 检查文件名生成逻辑，确保唯一性');
        }
        if (analysis.duplicateConversationIds > 0) {
            console.log('3. 检查对话合并逻辑，避免重复处理');
        }
    }
}

// 运行调试
if (require.main === module) {
    main();
}

module.exports = { analyzeExportedFiles };
