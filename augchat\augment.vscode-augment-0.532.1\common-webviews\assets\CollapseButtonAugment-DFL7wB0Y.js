import{i as we,aI as xe,aJ as ke,c as _e,p as Ce,l as Se,U as Le,f as V,a as X,o as c,b as u,S as ze,R as He,x as de,w as ce,aK as Ie,y as k,D as qe,F as pe,G as O,J,O as Fe,Q as Me,Y as G,C as Y,m as ie,Z as Te,_ as Z,a1 as Be,L as ve,K as re,B as T,z as j,I as D,u as Ue,M as ue}from"./legacy-YP6Kq8lu.js";import{h as W,a as Ae,I as Ee}from"./IconButtonAugment-CbpcmeFk.js";import{l as _,s as Ge,p as z,a as K,e as N,b as me,h as Je}from"./SpinnerAugment-Dpcl1cXc.js";import{b as Ke}from"./CardAugment-YBzgmAzG.js";function ts(l,e,n){var s,o=l,f=Le,h=we()?xe:ke;_e(()=>{h(f,f=e())&&(s&&Ce(s),s=Se(()=>n(o)))})}var Oe=V("<svg><!></svg>");function as(l,e){const n=_(e,["children","$$slots","$$events","$$legacy"]);var s=Oe();X(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...n}));var o=c(s);W(o,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',!0),u(l,s)}var Pe=V("<svg><!></svg>");function Qe(l,e){const n=_(e,["children","$$slots","$$events","$$legacy"]);var s=Pe();X(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...n}));var o=c(s);W(o,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 498.7c-8.8 7-21.2 7-30 0l-160-128c-10.4-8.3-12-23.4-3.7-33.7s23.4-12 33.7-3.8l145 116 145-116c10.3-8.3 25.5-6.6 33.7 3.8s6.6 25.5-3.7 33.7zm160-357.4c10.4 8.3 12 23.4 3.8 33.7s-23.4 12-33.7 3.7L224 62.7l-145 116c-10.4 8.3-25.5 6.6-33.7-3.7s-6.6-25.5 3.7-33.7l160-128c8.8-7 21.2-7 30 0z"/>',!0),u(l,s)}const fe=Symbol("collapsible");function Re(){return He(fe)}var Ye=O('<footer class="c-collapsible__footer svelte-13q5m27"><!></footer>'),Ze=O('<div class="c-collapsible__body svelte-13q5m27"><!></div> <!>',1),je=O('<div><header><div data-collapsible-header="button" tabindex="0"><!></div></header> <div><div class="c-collapsible__content-inner svelte-13q5m27"><!></div></div></div>');function os(l,e){const n=Ge(e),s=_(e,["children","$$slots","$$events","$$legacy"]),o=_(s,["toggle","collapsed","stickyHeader","expandable","isHeaderStuck","toggleHeader","stickyHeaderTop"]);de(e,!1);const[f,h]=me(),d=()=>N(m,"$collapsedStore",f),C=()=>N(i,"$expandableStore",f),H=ie();let g=z(e,"collapsed",12,!1),B=z(e,"stickyHeader",8,!1),p=z(e,"expandable",12,!0),I=z(e,"isHeaderStuck",12,!1),$=z(e,"toggleHeader",8,!1),q=z(e,"stickyHeaderTop",24,()=>-.5);const m=ce(g()),P=Ie(m,a=>a),i=ce(p());let Q,R=ie(!1);function ee(a){p()?m.set(a):m.set(!0)}const U=function(){ee(!d())};ze(fe,{collapsed:P,setCollapsed:ee,toggle:U,expandable:i}),k(()=>C(),()=>{p(C())}),k(()=>T(p()),()=>{i.set(p())}),k(()=>d(),()=>{g(d())}),k(()=>T(g()),()=>{m.set(g())}),k(()=>T(p()),()=>{p()||m.set(!0)}),k(()=>T(g()),()=>{g()?(clearTimeout(Q),Q=setTimeout(()=>{j(R,!1)},200)):(clearTimeout(Q),j(R,!0))}),k(()=>(Y(H),T(o)),()=>{j(H,o.class)}),qe(),pe();var A=je();let se,te;var E=c(A);let ae;var S=c(E);let oe;var he=c(S);K(he,e,"header",{},null),Ae(E,(a,v)=>function(y,b){const{onStuck:w,onUnstuck:F,offset:M=0}=b,t=document.createElement("div");t.style.position="absolute",t.style.top=M?`${M}px`:"0",t.style.height="1px",t.style.width="100%",t.style.pointerEvents="none",t.style.opacity="0",t.style.zIndex="-1";const r=y.parentNode;if(!r)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(r).position==="static"&&(r.style.position="relative"),r.insertBefore(t,y);const L=new IntersectionObserver(([x])=>{x.isIntersecting?F==null||F():w==null||w()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return L.observe(t),{update(x){b.onStuck=x.onStuck,b.onUnstuck=x.onUnstuck,x.offset!==void 0&&x.offset!==M&&(t.style.top=`${x.offset}px`)},destroy(){L.disconnect(),t.remove()}}}(a,v),()=>({offset:-q(),onStuck:()=>{I(!0)},onUnstuck:()=>{I(!1)}}));var le=re(E,2);let ne;var ge=c(le),$e=c(ge),ye=a=>{var v=Ze(),y=D(v),b=c(y),w=t=>{var r=ue(),L=D(r);K(L,e,"default",{},null),u(t,r)};J(b,t=>{Y(R)&&t(w)});var F=re(y,2),M=t=>{var r=Ye(),L=c(r);K(L,e,"footer",{},null),u(t,r)};J(F,t=>{Ue(()=>n.footer)&&t(M)}),u(a,v)};J($e,a=>{C()&&a(ye)}),Fe((a,v,y,b,w)=>{se=G(A,1,`c-collapsible ${Y(H)??""}`,"svelte-13q5m27",se,a),te=Te(A,"",te,v),ae=G(E,1,"c-collapsible__header svelte-13q5m27",null,ae,y),oe=G(S,1,"c-collapsible__header-inner svelte-13q5m27",null,oe,b),Z(S,"role",$()?"button":void 0),Z(S,"aria-expanded",$()?!d():void 0),Z(S,"aria-controls",$()?"collapsible-content":void 0),ne=G(le,1,"c-collapsible__content svelte-13q5m27",null,ne,w)},[()=>({"is-collapsed":d(),"is-expandable":C()}),()=>({"--sticky-header-top":`${q()}px`}),()=>({"is-sticky":B()}),()=>({"is-collapsed":d(),"is-header-stuck":I(),"has-header-padding":q()>0}),()=>({"is-collapsed":d()})],Me),Be("click",S,function(...a){var v;(v=$()?U:void 0)==null||v.apply(this,a)}),u(l,A),Ke(e,"toggle",U);var be=ve({toggle:U});return h(),be}var De=V("<svg><!></svg>");function Ne(l,e){const n=_(e,["children","$$slots","$$events","$$legacy"]);var s=De();X(s,()=>({xmlns:"http://www.w3.org/2000/svg",width:"8",height:"10","data-ds-icon":"fa",viewBox:"0 0 8 10",...n}));var o=c(s);W(o,()=>'<path d="M4.451 6.357a.42.42 0 0 0-.527 0L1.11 8.607a.42.42 0 0 0-.065.592.424.424 0 0 0 .593.067l2.548-2.04 2.55 2.04a.423.423 0 0 0 .527-.66zm2.813-4.965a.422.422 0 1 0-.526-.658l-2.55 2.04-2.55-2.04a.421.421 0 1 0-.527.658l2.813 2.25a.42.42 0 0 0 .527 0z"/>',!0),u(l,s)}var Ve=O('<span class="c-collapse-button-augment__icon svelte-hw7s17"><!></span>');function ls(l,e){const n=_(e,["children","$$slots","$$events","$$legacy"]),s=_(n,[]);de(e,!1);const[o,f]=me(),h=()=>N(d,"$collapsed",o),{collapsed:d,setCollapsed:C}=Re();pe(),Ee(l,Je({variant:"ghost-block",color:"neutral",size:1},()=>s,{$$events:{click:function(){C(!h())}},children:(H,g)=>{var B=Ve(),p=c(B);K(p,e,"default",{get collapsed(){return h()}},I=>{var $=ue(),q=D($),m=i=>{Qe(i,{})},P=i=>{Ne(i,{})};J(q,i=>{h()?i(m):i(P,!1)}),u(I,$)}),u(H,B)},$$slots:{default:!0}})),ve(),f()}export{Qe as A,os as C,as as T,ls as a,Ne as b,Re as g,ts as k};
