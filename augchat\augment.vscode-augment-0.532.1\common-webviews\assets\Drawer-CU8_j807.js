import{x as A,m as u,a8 as ee,C as e,z as a,y as B,B as w,D as ie,F as ae,G as Y,a1 as y,a9 as I,Z as J,o as p,K as L,J as te,O as se,Q as re,Y as K,b as Q,L as oe}from"./legacy-YP6Kq8lu.js";import{p as n,a as S,i as T}from"./SpinnerAugment-Dpcl1cXc.js";import{a as de,I as ne}from"./IconButtonAugment-CbpcmeFk.js";import{t as le,f as ce}from"./index-DOexUbEr.js";import{E as ve}from"./ellipsis-CW5cyp36.js";const O=(_,{onResize:t,options:g})=>{const v=new ResizeObserver(t);return v.observe(_,g),{destroy(){v.unobserve(_),v.disconnect()}}};var me=Y('<div class="c-drawer__hidden-indicator svelte-18f0m3o"><!></div>'),ue=Y('<div><div class="c-drawer__left svelte-18f0m3o"><div class="c-drawer__left-content svelte-18f0m3o"><!></div></div> <div aria-hidden="true"></div> <div class="c-drawer__right svelte-18f0m3o"><!></div> <!></div>');function ze(_,t){A(t,!1);let g,v,X=n(t,"initialWidth",8,300),z=n(t,"expandedMinWidth",8,50),C=n(t,"minimizedWidth",8,0),s=n(t,"minimized",12,!1),Z=n(t,"class",8,""),j=n(t,"showButton",8,!0),q=n(t,"deadzone",8,0),H=n(t,"columnLayoutThreshold",8,600),o=n(t,"layoutMode",28,()=>{}),x=u(),f=u(),l=u(!1),c=u(X()),k=u(X()),r=u(!1);function D(){s(!s())}function M(){if(e(f)){if(o()!==void 0)return a(r,o()==="column"),void(e(r)&&a(l,!1));a(r,e(f).clientWidth<H()),e(r)&&a(l,!1)}}ee(M),B(()=>(w(s()),w(o())),()=>{s()?(o("row"),a(r,!1)):o()!=="row"||s()||(o(void 0),M())}),B(()=>(w(o()),e(r)),()=>{o()!==void 0&&(a(r,o()==="column"),e(r)&&a(l,!1))}),B(()=>(w(s()),w(C()),e(c)),()=>{a(k,s()?C():e(c))}),ie(),ae();var h=ue();let E;y("mousemove",I,function(i){if(!e(l)||!e(x)||e(r))return;const d=i.clientX-g,b=e(f).clientWidth-200,m=v+d;m<z()?m<z()-q()?s(!0):(a(c,z()),s(!1)):m>b?(a(c,b),s(!1)):(a(c,m),s(!1))}),y("mouseup",I,function(){a(l,!1),a(c,Math.max(e(c),z()))});var W=p(h),R=p(W);J(R,"",{},{width:"var(--augment-drawer-width)","min-width":"var(--augment-drawer-width)","max-width":"var(--augment-drawer-width)"});var N=p(R);S(N,t,"left",{},null),T(W,i=>a(x,i),()=>e(x));var $=L(W,2);let F;var G=L($,2),P=p(G);S(P,t,"right",{},null);var U=L(G,2),V=i=>{var d=me(),b=p(d);ne(b,{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$events:{click:D},children:(m,fe)=>{ve(m,{})},$$slots:{default:!0}}),le(3,d,()=>ce,()=>({y:0,x:0,duration:200})),Q(i,d)};te(U,i=>{s()&&j()&&i(V)}),T(h,i=>a(f,i),()=>e(f)),de(h,(i,d)=>O==null?void 0:O(i,d),()=>({onResize:()=>o()===void 0&&M()})),se((i,d)=>{E=K(h,1,`c-drawer ${Z()??""}`,"svelte-18f0m3o",E,i),J(W,`--augment-drawer-width:${e(k)??""}px;`),R.inert=e(l),F=K($,1,"c-drawer__handle svelte-18f0m3o",null,F,d)},[()=>({"is-dragging":e(l),"is-hidden":!e(k),"is-column":e(r)}),()=>({"is-locked":e(r)})],re),y("mousedown",$,function(i){e(r)||(a(l,!0),g=i.clientX,v=e(x).offsetWidth,i.preventDefault())}),y("dblclick",$,D),Q(_,h),oe()}export{ze as D,O as r};
