var qi=Object.defineProperty;var Bi=(_,t,i)=>t in _?qi(_,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):_[t]=i;var d=(_,t,i)=>Bi(_,typeof t!="symbol"?t+"":t,i);import{w as Be,aO as li,ar as ke,aP as qt,x as Ot,F as Lt,G as ee,I as ne,K,J as we,o as pe,u as fe,b as V,O as St,Q as dt,C as u,m as j,Y as Tt,Z as vi,a1 as ze,L as $t,z as E,y as ge,D as Kt,B as Bt,P as bi,f as Hi,a as Zi,a8 as yi,T as Ci,M as Ht,N as Gi,a9 as Wt,X as Ki,aq as Ji,ap as Xi}from"./legacy-YP6Kq8lu.js";import{c as Mt,g as Qi,p as be,e as ue,b as It,f as Ze,S as hi,l as Yi,i as Et}from"./SpinnerAugment-Dpcl1cXc.js";import"./design-system-init-BkqeNcXX.js";import{h as at,W as he,D as Ut,e as en,i as tn}from"./host-BNehKqab.js";import{A as nn}from"./async-messaging-gS_K9w3p.js";import{d as fi,T as rn,a as gi}from"./CardAugment-YBzgmAzG.js";import{F as on,C as ki,S as wi,a as sn}from"./folder-opened-_jaOgw08.js";import{r as an,a as pi}from"./monaco-render-utils-DfwV7QLY.js";import{M as Mi}from"./message-broker-DRrss2z_.js";import{a as Ei,I as dn,h as cn}from"./IconButtonAugment-CbpcmeFk.js";import{K as He,R as _i,a as un,A as ln,P as hn}from"./Keybindings-DGDlpVHp.js";import{B as Re}from"./ButtonAugment-DkEdzEZO.js";import{s as fn}from"./chat-model-context-DZ2DTs5O.js";import{a as gn,M as pn,g as _n,C as mn}from"./index-hRm--fCg.js";import"./index-4vhrZf9p.js";import"./event-modifiers-Bz4QCcZc.js";import"./index-CKSGO-M1.js";import"./chat-types-BfwvR7Kn.js";import"./focusTrapStack-CaEmYw0i.js";import"./isObjectLike-BNqj-rl6.js";import"./input-C2nR_fsN.js";import"./BaseTextInput-Br9yLRnx.js";import"./types-CGlLNakm.js";import"./file-type-utils-D6OEcQY2.js";import"./tool-types-Chbmg_E2.js";import"./CalloutAugment-0Y9u1WCc.js";import"./exclamation-triangle-CZAYyoF0.js";import"./svelte-component-Uytug4gU.js";import"./Filespan-Dfz0pJHr.js";import"./MaterialIcon-j5PxZ6X_.js";import"./pen-to-square-C7XheTA_.js";import"./augment-logo-D8bZBTPs.js";var Zt={exports:{}};(function(_,t){var i="__lodash_hash_undefined__",a=1,r=2,s=9007199254740991,g="[object Arguments]",l="[object Array]",b="[object AsyncFunction]",m="[object Boolean]",h="[object Date]",v="[object Error]",k="[object Function]",S="[object GeneratorFunction]",N="[object Map]",p="[object Number]",O="[object Null]",w="[object Object]",J="[object Promise]",W="[object Proxy]",P="[object RegExp]",D="[object Set]",ie="[object String]",re="[object Symbol]",X="[object Undefined]",T="[object WeakMap]",oe="[object ArrayBuffer]",se="[object DataView]",Q=/^\[object .+?Constructor\]$/,me=/^(?:0|[1-9]\d*)$/,c={};c["[object Float32Array]"]=c["[object Float64Array]"]=c["[object Int8Array]"]=c["[object Int16Array]"]=c["[object Int32Array]"]=c["[object Uint8Array]"]=c["[object Uint8ClampedArray]"]=c["[object Uint16Array]"]=c["[object Uint32Array]"]=!0,c[g]=c[l]=c[oe]=c[m]=c[se]=c[h]=c[v]=c[k]=c[N]=c[p]=c[w]=c[P]=c[D]=c[ie]=c[T]=!1;var I=typeof Mt=="object"&&Mt&&Mt.Object===Object&&Mt,M=typeof self=="object"&&self&&self.Object===Object&&self,L=I||M||Function("return this")(),B=t&&!t.nodeType&&t,F=B&&_&&!_.nodeType&&_,H=F&&F.exports===B,Y=H&&I.process,y=function(){try{return Y&&Y.binding&&Y.binding("util")}catch{}}(),te=y&&y.isTypedArray;function Ne(e,n){for(var o=-1,f=e==null?0:e.length;++o<f;)if(n(e[o],o,e))return!0;return!1}function le(e){var n=-1,o=Array(e.size);return e.forEach(function(f,A){o[++n]=[A,f]}),o}function ae(e){var n=-1,o=Array(e.size);return e.forEach(function(f){o[++n]=f}),o}var _e,Ee,Ge,ct=Array.prototype,xt=Function.prototype,Ke=Object.prototype,et=L["__core-js_shared__"],R=xt.toString,U=Ke.hasOwnProperty,de=(_e=/[^.]+$/.exec(et&&et.keys&&et.keys.IE_PROTO||""))?"Symbol(src)_1."+_e:"",ye=Ke.toString,Je=RegExp("^"+R.call(U).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Se=H?L.Buffer:void 0,Ae=L.Symbol,ut=L.Uint8Array,Jt=Ke.propertyIsEnumerable,Oi=ct.splice,Pe=Ae?Ae.toStringTag:void 0,Xt=Object.getOwnPropertySymbols,Li=Se?Se.isBuffer:void 0,$i=(Ee=Object.keys,Ge=Object,function(e){return Ee(Ge(e))}),Nt=Xe(L,"DataView"),tt=Xe(L,"Map"),At=Xe(L,"Promise"),jt=Xe(L,"Set"),Vt=Xe(L,"WeakMap"),it=Xe(Object,"create"),Ii=Ue(Nt),xi=Ue(tt),Ni=Ue(At),Ai=Ue(jt),ji=Ue(Vt),Qt=Ae?Ae.prototype:void 0,Dt=Qt?Qt.valueOf:void 0;function Te(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var f=e[n];this.set(f[0],f[1])}}function Oe(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var f=e[n];this.set(f[0],f[1])}}function We(e){var n=-1,o=e==null?0:e.length;for(this.clear();++n<o;){var f=e[n];this.set(f[0],f[1])}}function lt(e){var n=-1,o=e==null?0:e.length;for(this.__data__=new We;++n<o;)this.add(e[n])}function je(e){var n=this.__data__=new Oe(e);this.size=n.size}function Vi(e,n){var o=gt(e),f=!o&&Pi(e),A=!o&&!f&&Ft(e),C=!o&&!f&&!A&&ai(e),z=o||f||A||C,q=z?function(G,Me){for(var Le=-1,ce=Array(G);++Le<G;)ce[Le]=Me(Le);return ce}(e.length,String):[],Ce=q.length;for(var Z in e)!U.call(e,Z)||z&&(Z=="length"||A&&(Z=="offset"||Z=="parent")||C&&(Z=="buffer"||Z=="byteLength"||Z=="byteOffset")||zi(Z,Ce))||q.push(Z);return q}function ht(e,n){for(var o=e.length;o--;)if(ni(e[o][0],n))return o;return-1}function nt(e){return e==null?e===void 0?X:O:Pe&&Pe in Object(e)?function(n){var o=U.call(n,Pe),f=n[Pe];try{n[Pe]=void 0;var A=!0}catch{}var C=ye.call(n);return A&&(o?n[Pe]=f:delete n[Pe]),C}(e):function(n){return ye.call(n)}(e)}function Yt(e){return rt(e)&&nt(e)==g}function ei(e,n,o,f,A){return e===n||(e==null||n==null||!rt(e)&&!rt(n)?e!=e&&n!=n:function(C,z,q,Ce,Z,G){var Me=gt(C),Le=gt(z),ce=Me?l:Ve(C),$e=Le?l:Ve(z),Qe=(ce=ce==g?w:ce)==w,pt=($e=$e==g?w:$e)==w,Ye=ce==$e;if(Ye&&Ft(C)){if(!Ft(z))return!1;Me=!0,Qe=!1}if(Ye&&!Qe)return G||(G=new je),Me||ai(C)?ti(C,z,q,Ce,Z,G):function(x,$,_t,De,Rt,ve,Ie){switch(_t){case se:if(x.byteLength!=$.byteLength||x.byteOffset!=$.byteOffset)return!1;x=x.buffer,$=$.buffer;case oe:return!(x.byteLength!=$.byteLength||!ve(new ut(x),new ut($)));case m:case h:case p:return ni(+x,+$);case v:return x.name==$.name&&x.message==$.message;case P:case ie:return x==$+"";case N:var Fe=le;case D:var st=De&a;if(Fe||(Fe=ae),x.size!=$.size&&!st)return!1;var mt=Ie.get(x);if(mt)return mt==$;De|=r,Ie.set(x,$);var zt=ti(Fe(x),Fe($),De,Rt,ve,Ie);return Ie.delete(x),zt;case re:if(Dt)return Dt.call(x)==Dt.call($)}return!1}(C,z,ce,q,Ce,Z,G);if(!(q&a)){var ot=Qe&&U.call(C,"__wrapped__"),di=pt&&U.call(z,"__wrapped__");if(ot||di){var Wi=ot?C.value():C,Ui=di?z.value():z;return G||(G=new je),Z(Wi,Ui,q,Ce,G)}}return Ye?(G||(G=new je),function(x,$,_t,De,Rt,ve){var Ie=_t&a,Fe=ii(x),st=Fe.length,mt=ii($),zt=mt.length;if(st!=zt&&!Ie)return!1;for(var vt=st;vt--;){var qe=Fe[vt];if(!(Ie?qe in $:U.call($,qe)))return!1}var ci=ve.get(x);if(ci&&ve.get($))return ci==$;var bt=!0;ve.set(x,$),ve.set($,x);for(var Pt=Ie;++vt<st;){var yt=x[qe=Fe[vt]],Ct=$[qe];if(De)var ui=Ie?De(Ct,yt,qe,$,x,ve):De(yt,Ct,qe,x,$,ve);if(!(ui===void 0?yt===Ct||Rt(yt,Ct,_t,De,ve):ui)){bt=!1;break}Pt||(Pt=qe=="constructor")}if(bt&&!Pt){var kt=x.constructor,wt=$.constructor;kt==wt||!("constructor"in x)||!("constructor"in $)||typeof kt=="function"&&kt instanceof kt&&typeof wt=="function"&&wt instanceof wt||(bt=!1)}return ve.delete(x),ve.delete($),bt}(C,z,q,Ce,Z,G)):!1}(e,n,o,f,ei,A))}function Di(e){return!(!si(e)||function(n){return!!de&&de in n}(e))&&(ri(e)?Je:Q).test(Ue(e))}function Fi(e){if(o=(n=e)&&n.constructor,f=typeof o=="function"&&o.prototype||Ke,n!==f)return $i(e);var n,o,f,A=[];for(var C in Object(e))U.call(e,C)&&C!="constructor"&&A.push(C);return A}function ti(e,n,o,f,A,C){var z=o&a,q=e.length,Ce=n.length;if(q!=Ce&&!(z&&Ce>q))return!1;var Z=C.get(e);if(Z&&C.get(n))return Z==n;var G=-1,Me=!0,Le=o&r?new lt:void 0;for(C.set(e,n),C.set(n,e);++G<q;){var ce=e[G],$e=n[G];if(f)var Qe=z?f($e,ce,G,n,e,C):f(ce,$e,G,e,n,C);if(Qe!==void 0){if(Qe)continue;Me=!1;break}if(Le){if(!Ne(n,function(pt,Ye){if(ot=Ye,!Le.has(ot)&&(ce===pt||A(ce,pt,o,f,C)))return Le.push(Ye);var ot})){Me=!1;break}}else if(ce!==$e&&!A(ce,$e,o,f,C)){Me=!1;break}}return C.delete(e),C.delete(n),Me}function ii(e){return function(n,o,f){var A=o(n);return gt(n)?A:function(C,z){for(var q=-1,Ce=z.length,Z=C.length;++q<Ce;)C[Z+q]=z[q];return C}(A,f(n))}(e,Ti,Ri)}function ft(e,n){var o,f,A=e.__data__;return((f=typeof(o=n))=="string"||f=="number"||f=="symbol"||f=="boolean"?o!=="__proto__":o===null)?A[typeof n=="string"?"string":"hash"]:A.map}function Xe(e,n){var o=function(f,A){return f==null?void 0:f[A]}(e,n);return Di(o)?o:void 0}Te.prototype.clear=function(){this.__data__=it?it(null):{},this.size=0},Te.prototype.delete=function(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n},Te.prototype.get=function(e){var n=this.__data__;if(it){var o=n[e];return o===i?void 0:o}return U.call(n,e)?n[e]:void 0},Te.prototype.has=function(e){var n=this.__data__;return it?n[e]!==void 0:U.call(n,e)},Te.prototype.set=function(e,n){var o=this.__data__;return this.size+=this.has(e)?0:1,o[e]=it&&n===void 0?i:n,this},Oe.prototype.clear=function(){this.__data__=[],this.size=0},Oe.prototype.delete=function(e){var n=this.__data__,o=ht(n,e);return!(o<0)&&(o==n.length-1?n.pop():Oi.call(n,o,1),--this.size,!0)},Oe.prototype.get=function(e){var n=this.__data__,o=ht(n,e);return o<0?void 0:n[o][1]},Oe.prototype.has=function(e){return ht(this.__data__,e)>-1},Oe.prototype.set=function(e,n){var o=this.__data__,f=ht(o,e);return f<0?(++this.size,o.push([e,n])):o[f][1]=n,this},We.prototype.clear=function(){this.size=0,this.__data__={hash:new Te,map:new(tt||Oe),string:new Te}},We.prototype.delete=function(e){var n=ft(this,e).delete(e);return this.size-=n?1:0,n},We.prototype.get=function(e){return ft(this,e).get(e)},We.prototype.has=function(e){return ft(this,e).has(e)},We.prototype.set=function(e,n){var o=ft(this,e),f=o.size;return o.set(e,n),this.size+=o.size==f?0:1,this},lt.prototype.add=lt.prototype.push=function(e){return this.__data__.set(e,i),this},lt.prototype.has=function(e){return this.__data__.has(e)},je.prototype.clear=function(){this.__data__=new Oe,this.size=0},je.prototype.delete=function(e){var n=this.__data__,o=n.delete(e);return this.size=n.size,o},je.prototype.get=function(e){return this.__data__.get(e)},je.prototype.has=function(e){return this.__data__.has(e)},je.prototype.set=function(e,n){var o=this.__data__;if(o instanceof Oe){var f=o.__data__;if(!tt||f.length<199)return f.push([e,n]),this.size=++o.size,this;o=this.__data__=new We(f)}return o.set(e,n),this.size=o.size,this};var Ri=Xt?function(e){return e==null?[]:(e=Object(e),function(n,o){for(var f=-1,A=n==null?0:n.length,C=0,z=[];++f<A;){var q=n[f];o(q,f,n)&&(z[C++]=q)}return z}(Xt(e),function(n){return Jt.call(e,n)}))}:function(){return[]},Ve=nt;function zi(e,n){return!!(n=n??s)&&(typeof e=="number"||me.test(e))&&e>-1&&e%1==0&&e<n}function Ue(e){if(e!=null){try{return R.call(e)}catch{}try{return e+""}catch{}}return""}function ni(e,n){return e===n||e!=e&&n!=n}(Nt&&Ve(new Nt(new ArrayBuffer(1)))!=se||tt&&Ve(new tt)!=N||At&&Ve(At.resolve())!=J||jt&&Ve(new jt)!=D||Vt&&Ve(new Vt)!=T)&&(Ve=function(e){var n=nt(e),o=n==w?e.constructor:void 0,f=o?Ue(o):"";if(f)switch(f){case Ii:return se;case xi:return N;case Ni:return J;case Ai:return D;case ji:return T}return n});var Pi=Yt(function(){return arguments}())?Yt:function(e){return rt(e)&&U.call(e,"callee")&&!Jt.call(e,"callee")},gt=Array.isArray,Ft=Li||function(){return!1};function ri(e){if(!si(e))return!1;var n=nt(e);return n==k||n==S||n==b||n==W}function oi(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=s}function si(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}function rt(e){return e!=null&&typeof e=="object"}var ai=te?function(e){return function(n){return e(n)}}(te):function(e){return rt(e)&&oi(e.length)&&!!c[nt(e)]};function Ti(e){return(n=e)!=null&&oi(n.length)&&!ri(n)?Vi(e):Fi(e);var n}_.exports=function(e,n){return ei(e,n)}})(Zt,Zt.exports);const vn=Qi(Zt.exports);function Si(_){return function(t){return"unitOfCodeWork"in t&&!function(i){return i.children.length>0&&"childIds"in i}(t)}(_)?[_]:_.children.flatMap(Si)}var xe=(_=>(_.edit="edit",_.instruction="instruction",_))(xe||{}),Gt=(_=>(_[_.instructionDrawer=0]="instructionDrawer",_[_.chunkActionPanel=1]="chunkActionPanel",_))(Gt||{});class bn{constructor(t,i,a){d(this,"_originalModel");d(this,"_modifiedModel");d(this,"_fullEdits",[]);d(this,"_currEdit");d(this,"_currOriginalEdit");d(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(i=>{this._modifiedModel.applyEdits([i])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});d(this,"finish",()=>this._completeCurrEdit());d(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);d(this,"_completeCurrEdit",t=>{const i={resetOriginal:[],original:[],modified:[]};if(!t)return i;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const a=this._nextModifiedInsertPosition(),r=t.stagedEndLine-t.stagedStartLine,s={range:new this._monaco.Range(a.lineNumber,0,a.lineNumber+r,0),text:""};i.modified.push(s),this._modifiedModel.applyEdits([s]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return i});d(this,"_startNewEdit",t=>{const i={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},i.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),i});d(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const i=this._nextModifiedInsertPosition(),a={...this._currEdit,text:t.newText,range:new this._monaco.Range(i.lineNumber,i.column,i.lineNumber,i.column)};return this._modifiedModel.applyEdits([a]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[a]:[]}});d(this,"_nextModifiedInsertPosition",()=>{var i;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((i=this._currEdit.text)==null?void 0:i.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=i,this._monaco=a,this._originalModel=this._monaco.editor.createModel(i),this._modifiedModel=this._monaco.editor.createModel(i)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class yn{constructor(t,i,a){d(this,"_asyncMsgSender");d(this,"_editor");d(this,"_chatModel");d(this,"_focusModel",new on);d(this,"_hasScrolledOnInit",!1);d(this,"_markHasScrolledOnInit",fi(()=>{this._hasScrolledOnInit=!0},200));d(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});d(this,"_subscribers",new Set);d(this,"_disposables",[]);d(this,"_rootChunk");d(this,"_keybindings",Be({}));d(this,"_requestId",Be(void 0));d(this,"requestId",this._requestId);d(this,"_disableResolution",Be(!1));d(this,"disableResolution",li(this._disableResolution));d(this,"_disableApply",Be(!1));d(this,"disableApply",li(this._disableApply));d(this,"_currStream");d(this,"_isLoadingDiffChunks",Be(!1));d(this,"_selectionLines",Be(void 0));d(this,"_mode",Be(xe.edit));d(this,"initializeEditor",t=>{var i,a,r,s,g,l,b,m,h,v,k,S;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new ki(new Mi(at),at,new wi),(a=(i=this._monaco.editor).registerCommand)==null||a.call(i,"acceptFocusedChunk",this.acceptFocusedChunk),(s=(r=this._monaco.editor).registerCommand)==null||s.call(r,"rejectFocusedChunk",this.rejectFocusedChunk),(l=(g=this._monaco.editor).registerCommand)==null||l.call(g,"acceptAllChunks",this.acceptAllChunks),(m=(b=this._monaco.editor).registerCommand)==null||m.call(b,"rejectAllChunks",this.rejectAllChunks),(v=(h=this._monaco.editor).registerCommand)==null||v.call(h,"focusNextChunk",this.focusNextChunk),(S=(k=this._monaco.editor).registerCommand)==null||S.call(k,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(N=>this.notifySubscribers())}),this.initialize()});d(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));d(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});d(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});d(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const i=this.leaves[0];this.revealChunk(i)}this.notifyDiffViewUpdated(),this.notifySubscribers()});d(this,"onMouseMoveModified",t=>{var r,s,g,l,b,m;if(((r=t.target.position)==null?void 0:r.lineNumber)===void 0||this.leaves===void 0)return;const i=this.editorOffset,a=(s=t.target.position)==null?void 0:s.lineNumber;for(let h=0;h<this.leaves.length;h++){const v=this.leaves[h],k=(g=v.unitOfCodeWork.lineChanges)==null?void 0:g.lineChanges[0].modifiedStart,S=(l=v.unitOfCodeWork.lineChanges)==null?void 0:l.lineChanges[0].modifiedEnd,N=(b=v.unitOfCodeWork.lineChanges)==null?void 0:b.lineChanges[0].originalStart,p=(m=v.unitOfCodeWork.lineChanges)==null?void 0:m.lineChanges[0].originalEnd;if(k!==void 0&&S!==void 0&&N!==void 0&&p!==void 0){if(k!==S||a!==k){if(k<=a&&a<S){this.setCurrFocusedChunkIdx(h,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const O=this._editor.getOriginalEditor(),w=O.getOption(this._monaco.editor.EditorOption.lineHeight),J=O.getScrolledVisiblePosition({lineNumber:N,column:0}),W=O.getScrolledVisiblePosition({lineNumber:p+1,column:0});if(J===null||W===null)continue;const P=J.top-w/2+i,D=W.top-w/2+i;if(t.event.posy>=P&&t.event.posy<=D){this.setCurrFocusedChunkIdx(h,!1);break}break}}}});d(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:he.diffViewWindowFocusChange,data:t})});d(this,"setCurrFocusedChunkIdx",(t,i=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),i&&this.revealCurrFocusedChunk(),this.notifySubscribers())});d(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});d(this,"revealChunk",t=>{var r;const i=(r=t.unitOfCodeWork.lineChanges)==null?void 0:r.lineChanges[0],a=i==null?void 0:i.modifiedStart;a!==void 0&&this._editor.revealLineNearTop(a-1)});d(this,"renderCentralOverlayWidget",t=>{const i=()=>({editor:this._editor,id:"central-overlay-widget"}),a=an(t,i(),{monaco:this._monaco});return{update:()=>{a.update(i())},destroy:a.destroy}});d(this,"renderInstructionsDrawerViewZone",(t,i)=>{let a=!1,r=i;const s=i.autoFocus??!0,g=h=>{s&&!a&&(this._editor.revealLineNearTop(h),a=!0)},l=h=>({...h,ordinal:Gt.instructionDrawer,editor:this._editor,afterLineNumber:h.line}),b=pi(t,l(i)),m=[];return s&&m.push(this._editor.onDidUpdateDiff(()=>{g(r.line)})),{update:h=>{const v={...r,...h};vn(v,r)||(b.update(l(v)),r=v,g(v.line))},destroy:()=>{b.destroy(),m.forEach(h=>h.dispose())}}});d(this,"renderActionsViewZone",(t,i)=>{const a=s=>{var l;let g;return g=s.chunk?(l=s.chunk.unitOfCodeWork.lineChanges)==null?void 0:l.lineChanges[0].modifiedStart:1,{...s,ordinal:Gt.chunkActionPanel,editor:this._editor,afterLineNumber:g?g-1:void 0}},r=pi(t,a(i));return{update:s=>{r.update(a(s))},destroy:r.destroy}});d(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});d(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});d(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});d(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});d(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});d(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});d(this,"initialize",async()=>{var b;const t=await this._asyncMsgSender.send({type:he.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:i,instruction:a,keybindings:r,editable:s}=t.data;this._editor.updateOptions({readOnly:!s});const g=ke(this._keybindings);this._keybindings.set(r??g);const l=a==null?void 0:a.selection;l&&(l.start.line===l.end.line&&l.start.character===l.end.character&&this._mode.set(xe.instruction),ke(this.selectionLines)===void 0&&this._selectionLines.set({start:l.start.line,end:l.end.line})),this.updateModels(i.originalCode??"",i.modifiedCode??"",{rootPath:i.repoRoot,relPath:i.pathName}),(b=this._currStream)==null||b.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});d(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:he.disposeDiffView})});d(this,"_tryFetchStream",async()=>{var i,a,r;const t=this._asyncMsgSender.stream({type:he.diffViewFetchPendingStream},15e3,6e4);for await(const s of t)switch(s.type){case he.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(s.data.requestId);const g=this._editor.getOriginalEditor().getValue();this._currStream=new bn(s.data.streamId,g,this._monaco),this._syncStreamToModels();break}case he.diffViewDiffStreamEnded:if(((i=this._currStream)==null?void 0:i.id)!==s.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case he.diffViewDiffStreamChunk:{if(((a=this._currStream)==null?void 0:a.id)!==s.data.streamId)return;const g=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!g)return this.setLoading(!1),void this._cleanupStream();const l=(r=this._currStream)==null?void 0:r.onReceiveChunk(s);l&&(this._applyDeltaDiff(l),ke(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});d(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case he.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case he.diffViewAcceptAllChunks:this.acceptAllChunks();break;case he.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case he.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case he.diffViewFocusPrevChunk:this.focusPrevChunk();break;case he.diffViewFocusNextChunk:this.focusNextChunk()}});d(this,"_applyDeltaDiff",t=>{const i=this._editor.getOriginalEditor().getModel(),a=this._editor.getModifiedEditor().getModel();i&&a&&(i.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(r=>{i.pushEditOperations([],[r],()=>[])}),t.modified.forEach(r=>{a.pushEditOperations([],[r],()=>[])}))});d(this,"_cleanupStream",()=>{var t;if(this._currStream){const i=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(i),this._currStream=void 0,this._resetScrollOnInit()}});d(this,"_syncStreamToModels",()=>{var a,r;const t=(a=this._currStream)==null?void 0:a.originalValue,i=(r=this._currStream)==null?void 0:r.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),i&&i!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(i)});d(this,"acceptChunk",async t=>{ke(this._disableApply)||this.acceptChunks([t])});d(this,"acceptChunks",async(t,i=!1)=>{ke(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,Ut.accept,i),await qt(),this.areModelsEqual()&&!ke(this.isLoading)&&this.disposeDiffViewPanel())});d(this,"areModelsEqual",()=>{var a,r;const t=(a=this._editor.getModel())==null?void 0:a.original,i=(r=this._editor.getModel())==null?void 0:r.modified;return(t==null?void 0:t.getValue())===(i==null?void 0:i.getValue())});d(this,"rejectChunk",async t=>{this.rejectChunks([t])});d(this,"rejectChunks",async(t,i=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,Ut.reject,i),await qt(),this.areModelsEqual()&&!ke(this.isLoading)&&this.disposeDiffViewPanel()});d(this,"notifyDiffViewUpdated",fi(()=>{this.notifyResolvedChunks([],Ut.accept)},1e3));d(this,"notifyResolvedChunks",async(t,i,a=!1)=>{var s;const r=(s=this._editor.getModel())==null?void 0:s.original.uri.path;r&&await this._asyncMsgSender.send({type:he.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:r,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(g=>g.unitOfCodeWork),resolveType:i,shouldApplyToAll:a}},2e3)});d(this,"executeDiffChunks",(t,i)=>{var h,v,k;if(ke(this._disableResolution)||i&&ke(this._disableApply))return;const a=(h=this._editor.getModel())==null?void 0:h.original,r=(v=this._editor.getModel())==null?void 0:v.modified;if(!a||!r||this._currStream!==void 0)return;const s=[],g=[];for(const S of t){const N=(k=S.unitOfCodeWork.lineChanges)==null?void 0:k.lineChanges[0];if(!N||S.unitOfCodeWork.originalCode===void 0||S.unitOfCodeWork.modifiedCode===void 0)continue;let p={startLineNumber:N.originalStart,startColumn:1,endLineNumber:N.originalEnd,endColumn:1},O={startLineNumber:N.modifiedStart,startColumn:1,endLineNumber:N.modifiedEnd,endColumn:1};const w=i?S.unitOfCodeWork.modifiedCode:S.unitOfCodeWork.originalCode;w!==void 0&&(s.push({range:p,text:w}),g.push({range:O,text:w}))}a.pushEditOperations([],s,()=>[]),r.pushEditOperations([],g,()=>[]);const l=this._focusModel.nextIdx({nowrap:!0});if(l===void 0)return;const b=l===this._focusModel.focusedItemIdx?l-1:l,m=this._focusModel.items[b];m&&this.revealChunk(m)});d(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});d(this,"handleInstructionSubmit",t=>{const i=this._editor.getModifiedEditor(),a=this.getSelectedCodeDetails(i);if(!a)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,a)});d(this,"updateModels",(t,i,a)=>{var g,l;const r=(l=(g=this._editor.getModel())==null?void 0:g.original)==null?void 0:l.uri,s=(a&&this._monaco.Uri.file(a.relPath))??r;if(s)if((r==null?void 0:r.fsPath)!==s.fsPath||(r==null?void 0:r.authority)!==s.authority){const b=s.with({fragment:crypto.randomUUID()}),m=s.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,b),modified:this._monaco.editor.createModel(i??"",void 0,m)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==i&&this.getModifiedEditor().setValue(i??"");else console.warn("No URI found for diff view. Not updating models.")});d(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=a,this._asyncMsgSender=new nn(r=>at.postMessage(r)),this.initializeEditor(i)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return Si(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var s,g;const t=[],i=this._editor.getLineChanges(),a=(s=this._editor.getModel())==null?void 0:s.original,r=(g=this._editor.getModel())==null?void 0:g.modified;if(i&&a&&r){for(const l of i){const b=mi({startLineNumber:l.originalStartLineNumber,endLineNumber:l.originalEndLineNumber}),m=mi({startLineNumber:l.modifiedStartLineNumber,endLineNumber:l.modifiedEndLineNumber}),h=Cn(this._editor,b,m);t.push(h)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(l=>l.id)}}}getSelectedCodeDetails(t){const i=t.getModel();if(!i)return null;const a=i.getLanguageId(),r=1,s=1,g={lineNumber:i.getLineCount(),column:i.getLineMaxColumn(i.getLineCount())},l=ke(this._selectionLines);if(!l)throw new Error("No selection lines found");const b=Math.min(l.end+1,g.lineNumber),m=new this._monaco.Range(l.start+1,1,b,i.getLineMaxColumn(b));let h=i.getValueInRange(m);b<i.getLineCount()&&(h+=i.getEOL());const v=new this._monaco.Range(r,s,m.startLineNumber,m.startColumn),k=Math.min(m.endLineNumber+1,g.lineNumber),S=new this._monaco.Range(k,1,g.lineNumber,g.column);return{selectedCode:h,prefix:i.getValueInRange(v),suffix:i.getValueInRange(S),path:i.uri.path,language:a,prefixBegin:v.startLineNumber-1,suffixEnd:S.endLineNumber-1}}}function Cn(_,t,i){var s,g;const a=(s=_.getModel())==null?void 0:s.original,r=(g=_.getModel())==null?void 0:g.modified;if(!a||!r)throw new Error("No models found");return function(l,b,m,h){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:l,modifiedCode:b,lineChanges:{lineChanges:[{originalStart:m.startLineNumber,originalEnd:m.endLineNumber,modifiedStart:h.startLineNumber,modifiedEnd:h.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(a.getValueInRange(t),r.getValueInRange(i),t,i)}function mi(_){return _.endLineNumber===0?{startLineNumber:_.startLineNumber+1,startColumn:1,endLineNumber:_.startLineNumber+1,endColumn:1}:{startLineNumber:_.startLineNumber,startColumn:1,endLineNumber:_.endLineNumber+1,endColumn:1}}var kn=ee("<!> Accept",1),wn=ee("<!> Reject",1),Mn=ee("<div></div>  <div><div><!> <!></div></div>",1);function En(_,t){Ot(t,!1);const[i,a]=It(),r=()=>ue(S,"$keybindingsStore",i);let s=be(t,"align",8,"right"),g=be(t,"isFocused",8),l=be(t,"heightInPx",8,1),b=be(t,"disableApply",8,!1),m=be(t,"onAccept",8),h=be(t,"onReject",8),v=be(t,"diffViewModel",8),k=be(t,"leaf",8);const S=v().keybindings;let N=j(0);const p=c=>{E(N,c)};let O,w=j(!1);function J(){O&&(clearTimeout(O),O=void 0),E(w,!1)}function W(c){c.target.closest(".c-button-container")?J():c.type==="mouseenter"||c.type==="mousemove"?(J(),O=setTimeout(()=>{E(w,!0)},400)):c.type==="mouseleave"&&J()}Lt();var P=Mn(),D=ne(P);let ie;Ei(D,(c,I)=>{var M,L;return(L=(M=v()).renderActionsViewZone)==null?void 0:L.call(M,c,I)},()=>({chunk:k(),heightInPx:l(),onDomNodeTop:p}));var re=K(D,2);let X;var T=pe(re);let oe;var se=pe(T),Q=c=>{Re(c,{size:1,variant:"ghost",color:"success",$$events:{click(...I){var M;(M=m())==null||M.apply(this,I)}},children:(I,M)=>{var L=kn(),B=ne(L);He(B,{get keybinding(){return r(),fe(()=>r().acceptFocusedChunk)}}),V(I,L)},$$slots:{default:!0}})};we(se,c=>{b()||c(Q)});var me=K(se,2);Re(me,{size:1,variant:"ghost",color:"error",$$events:{click(...c){var I;(I=h())==null||I.apply(this,c)}},children:(c,I)=>{var M=wn(),L=ne(M);He(L,{get keybinding(){return r(),fe(()=>r().rejectFocusedChunk)}}),V(c,M)},$$slots:{default:!0}}),St((c,I,M)=>{ie=Tt(D,1,"svelte-zm1705",null,ie,c),X=Tt(re,1,"c-chunk-action-panel-anchor svelte-zm1705",null,X,I),vi(re,`top: ${u(N)??""}px;`),oe=Tt(T,1,"c-button-container svelte-zm1705",null,oe,M)},[()=>({"c-chunk-diff-border--focused":!!k()&&g()}),()=>({"c-chunk-action-panel-anchor--left":s()==="left","c-chunk-action-panel-anchor--right":s()==="right","c-chunk-action-panel-anchor--focused":g()}),()=>({"c-button-container--focused":g(),"c-button-container--transparent":u(w)})],dt),ze("mouseenter",re,W),ze("mousemove",re,W),ze("mouseleave",re,W),V(_,P),$t(),a()}var Sn=ee('<span class="c-diff-page-counter svelte-1w94ymh">Generating changes <!></span>'),On=ee('<span class="c-diff-page-counter svelte-1w94ymh"> <!></span>'),Ln=ee('<span class="c-diff-page-counter svelte-1w94ymh">No changes</span>'),$n=ee("<!> Back",1),In=ee("<!> Next",1),xn=ee("<!> Accept All",1),Nn=ee("<!> Reject All",1),An=ee("<!> <!>",1),jn=ee("<!> <!> <!>",1),Vn=ee('<div class="c-top-action-panel-anchor svelte-1w94ymh"><div class="c-button-container svelte-1w94ymh"><!> <!> <!></div></div>');function Dn(_,t){Ot(t,!1);const[i,a]=It(),r=()=>ue(k(),"$diffViewModel",i),s=()=>ue(N,"$requestId",i),g=()=>ue(u(h),"$isLoadingStore",i),l=()=>ue(S,"$keybindingsStore",i),b=j(),m=j(),h=j(),v=j();let k=be(t,"diffViewModel",8);const S=k().keybindings,N=k().requestId;let p,O=j("x"),w=j("Copy request ID"),J=j(()=>{});function W(c){c||(clearTimeout(p),p=void 0,E(w,"Copy request ID"))}async function P(){s()&&(await navigator.clipboard.writeText(s()),E(w,"Copied!"),clearTimeout(p),p=setTimeout(u(J),1500))}ge(()=>r(),()=>{Ze(E(b,r().disableResolution),"$disableResolution",i)}),ge(()=>r(),()=>{Ze(E(m,r().disableApply),"$disableApply",i)}),ge(()=>r(),()=>{r().currFocusedChunkIdx!==void 0?E(O,(r().currFocusedChunkIdx+1).toString()):E(O,"x")}),ge(()=>r(),()=>{Ze(E(h,r().isLoading),"$isLoadingStore",i)}),ge(()=>r(),()=>{var c;E(v,!!((c=r().leaves)!=null&&c.length))}),Kt(),Lt();var D=Vn(),ie=pe(D),re=pe(ie),X=c=>{const I=dt(()=>(Bt(gi),fe(()=>[gi.Hover])));rn(c,{onOpenChange:W,get content(){return u(w)},get triggerOn(){return u(I)},get requestClose(){return u(J)},set requestClose(M){E(J,M)},children:(M,L)=>{dn(M,{variant:"ghost",color:"neutral",size:1,$$events:{click:P},children:(B,F)=>{sn(B)},$$slots:{default:!0}})},$$slots:{default:!0},$$legacy:!0})};we(re,c=>{s()&&c(X)});var T=K(re,2),oe=c=>{var I=Sn(),M=K(pe(I));hi(M,{size:1,get loading(){return g()}}),V(c,I)},se=(c,I)=>{var M=B=>{var F=On(),H=pe(F),Y=K(H);hi(Y,{size:1,get loading(){return g()}}),St(()=>bi(H,`${u(O)??""} of ${r(),fe(()=>{var y,te;return(te=(y=r())==null?void 0:y.leaves)==null?void 0:te.length})??""} `)),V(B,F)},L=B=>{var F=Ln();V(B,F)};we(c,B=>{u(v)?B(M):B(L,!1)},I)};we(T,c=>{!u(v)&&g()?c(oe):c(se,!1)});var Q=K(T,2),me=c=>{var I=jn(),M=ne(I);Re(M,{size:1,variant:"ghost",color:"neutral",$$events:{click(...H){var Y;(Y=k().focusPrevChunk)==null||Y.apply(this,H)}},children:(H,Y)=>{var y=$n(),te=ne(y);He(te,{get keybinding(){return l(),fe(()=>l().focusPrevChunk)}}),V(H,y)},$$slots:{default:!0}});var L=K(M,2);Re(L,{size:1,variant:"ghost",color:"neutral",$$events:{click(...H){var Y;(Y=k().focusNextChunk)==null||Y.apply(this,H)}},children:(H,Y)=>{var y=In(),te=ne(y);He(te,{get keybinding(){return l(),fe(()=>l().focusNextChunk)}}),V(H,y)},$$slots:{default:!0}});var B=K(L,2),F=H=>{var Y=An(),y=ne(Y),te=le=>{Re(le,{size:1,variant:"ghost",color:"success",$$events:{click(...ae){var _e;(_e=k().acceptAllChunks)==null||_e.apply(this,ae)}},children:(ae,_e)=>{var Ee=xn(),Ge=ne(Ee);He(Ge,{get keybinding(){return l(),fe(()=>l().acceptAllChunks)}}),V(ae,Ee)},$$slots:{default:!0}})};we(y,le=>{ue(u(m),"$disableApply",i)||le(te)});var Ne=K(y,2);Re(Ne,{size:1,variant:"ghost",color:"error",$$events:{click(...le){var ae;(ae=k().rejectAllChunks)==null||ae.apply(this,le)}},children:(le,ae)=>{var _e=Nn(),Ee=ne(_e);He(Ee,{get keybinding(){return l(),fe(()=>l().rejectAllChunks)}}),V(le,_e)},$$slots:{default:!0}}),V(H,Y)};we(B,H=>{ue(u(b),"$disableResolution",i)||H(F)}),V(c,I)};we(Q,c=>{u(v)&&c(me)}),V(_,D),$t(),a()}var Fn=Hi("<svg><!></svg>"),Rn=ee("<!> <!> <!> <!>",1),zn=ee("Close <!>",1),Pn=ee('<div></div> <div class="instruction-drawer-panel svelte-1cxscce"><div class="instruction-drawer-panel__contents svelte-1cxscce" tabindex="0" role="button"><div class="l-input-area__input svelte-1cxscce"><!></div> <div class="c-instruction-drawer-panel__btn-container svelte-1cxscce"><!> <!></div></div></div>',1);function Tn(_,t){Ot(t,!1);const[i,a]=It(),r=()=>ue(re,"$modeStore",i),s=()=>ue(X,"$selectionLinesStore",i),g=()=>ue(h(),"$diffViewModel",i),l=()=>ue(u(m),"$isLoadingStore",i),b=j(),m=j();let h=be(t,"diffViewModel",8),v=be(t,"initialConversation",24,()=>{}),k=be(t,"initialFlags",24,()=>{});const S=gn.getContext().monaco,N={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},p=new Mi(at);let O=new wi;p.registerConsumer(O);let w=new ki(p,at,O,{initialConversation:v(),initialFlags:k()});const J=w.currentConversationModel;let W;p.registerConsumer(w),fn(w);let P,D=j(),ie=j("");const re=h().mode,X=h().selectionLines;function T(){const y=h().getModifiedEditor(),te=ke(S);if(!y||!te||(P==null||P.clear(),!s()))return;const Ne=s().start,le=s().end,ae={range:new te.Range(Ne+1,1,le+1,1),options:N};P||(P=y.createDecorationsCollection()),P.set([ae])}function oe(){var y;return!!((y=u(ie))!=null&&y.trim())&&(h().handleInstructionSubmit(u(ie)),!0)}yi(async()=>{await qt(),M(),E(se,h().editorOffset)}),Ci(()=>{W==null||W.destroy(),P==null||P.clear()});let se=j(0),Q=j(57),me=j(void 0),c=j(void 0);const I=()=>{var y;return(y=u(me))==null?void 0:y.requestFocus()},M=()=>{var y;return(y=u(me))==null?void 0:y.forceFocus()},L=y=>{J.saveDraftMentions(y.current)};function B(y){E(ie,y.rawText)}ge(()=>(r(),xe),()=>{E(b,(r()===xe.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs")}),ge(()=>g(),()=>{Ze(E(m,g().isLoading),"$isLoadingStore",i)}),ge(()=>(u(D),s()),()=>{if(u(D)){if(s()==null)E(Q,0);else{const y=u(D).scrollHeight;E(Q,Math.min(40+y,108))}W==null||W.update({heightInPx:u(Q)}),T()}}),Kt(),Lt();var F=Ht(),H=ne(F),Y=y=>{var te=Pn(),Ne=ne(te);Ei(Ne,R=>function(U){if(U){const de=s()?s().start:1;W=h().renderInstructionsDrawerViewZone(U,{line:de,heightInPx:u(Q),onDomNodeTop:ye=>{E(se,h().editorOffset+ye)},autoFocus:!0}),T()}}(R));var le=K(Ne,2),ae=pe(le),_e=pe(ae),Ee=pe(_e);Et(_i.Root(Ee,{focusOnInit:!0,children:(R,U)=>{var de=Rn(),ye=ne(de);un(ye,{shortcuts:{Enter:()=>oe()}});var Je=K(ye,2);Et(ln(Je,{requestEditorFocus:I,onMentionItemsUpdated:L,$$legacy:!0}),ut=>E(c,ut),()=>u(c));var Se=K(Je,2);_i.Content(Se,{get content(){return u(ie)},onContentChanged:B});var Ae=K(Se,2);hn(Ae,{get placeholder(){return u(b)}}),V(R,de)},$$slots:{default:!0},$$legacy:!0}),R=>E(me,R),()=>u(me)),Et(_e,R=>E(D,R),()=>u(D));var Ge=K(_e,2),ct=pe(Ge);Re(ct,{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$events:{click(...R){var U;(U=h().disposeDiffViewPanel)==null||U.apply(this,R)}},children:(R,U)=>{var de=zn(),ye=K(ne(de));He(ye,{keybinding:"esc"}),V(R,de)},$$slots:{default:!0}});var xt=K(ct,2);const Ke=dt(()=>(r(),Bt(xe),fe(()=>r()===xe.instruction?"Instruct Augment":"Edit with Augment"))),et=dt(()=>(u(ie),l(),fe(()=>!u(ie).trim()||l())));Re(xt,{id:"send",size:1,variant:"solid",color:"accent",get title(){return u(Ke)},get disabled(){return u(et)},$$events:{click:oe},children:(R,U)=>{var de=Gi();St(()=>bi(de,(r(),Bt(xe),fe(()=>r()===xe.instruction?"Instruct":"Edit")))),V(R,de)},$$slots:{default:!0,iconRight:(R,U)=>{(function(de,ye){const Je=Yi(ye,["children","$$slots","$$events","$$legacy"]);var Se=Fn();Zi(Se,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...Je}));var Ae=pe(Se);cn(Ae,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M133.9 232 65.8 95.9 383.4 232zm0 48h249.5L65.8 416.1l68-136.1zM44.6 34.6C32.3 29.3 17.9 32.3 8.7 42S-2.6 66.3 3.4 78.3L92.2 256 3.4 433.7c-6 12-3.9 26.5 5.3 36.3s23.5 12.7 35.9 7.5l448-192c11.8-5 19.4-16.6 19.4-29.4s-7.6-24.4-19.4-29.4l-448-192z"/>',!0),V(de,Se)})(R,{slot:"iconRight"})}}}),St(()=>vi(le,`top: ${u(se)??""}px; height: ${u(Q)??""}px;`)),ze("click",ae,M),ze("keydown",ae,R=>{R.key==="Enter"&&(M(),R.stopPropagation(),R.preventDefault())}),V(y,te)};we(H,y=>{s()&&y(Y)}),V(_,F),$t(),a()}var Wn=ee('<div class="sticky-top svelte-453n6i"><!></div> <!>',1),Un=ee('<div class="diff-view-container svelte-453n6i"><!> <div class="editor-container svelte-453n6i"><div class="editor svelte-453n6i"></div> <!></div></div>');Xi(function(_,t){Ot(t,!1);const[i,a]=It(),r=()=>ue(Ji,"$themeStore",i),s=()=>ue(u(h),"$diffViewModel",i),g=()=>ue(u(b),"$disableResolution",i),l=j(),b=j(),m=j();let h=j(),v=j();function k(p){const O=Ki.dark;return _n((p==null?void 0:p.category)||O,p==null?void 0:p.intensity)??mn.get(O)}let S=j();yi(async()=>{E(S,await window.augmentDeps.monaco),u(S)||console.error("Monaco not loaded. Diff view cannot be initialized.")}),Ci(()=>{var p;(p=u(h))==null||p.dispose()});let N=j(!1);ge(()=>(u(S),u(v),u(h),r()),()=>{u(S)&&u(v)&&!u(h)&&Ze(E(h,new yn(u(v),k(r()),u(S))),"$diffViewModel",i)}),ge(()=>u(h),()=>{var p;Ze(E(l,(p=u(h))==null?void 0:p.disableApply),"$disableApply",i)}),ge(()=>u(h),()=>{var p;Ze(E(b,(p=u(h))==null?void 0:p.disableResolution),"$disableResolution",i)}),ge(()=>(r(),u(h)),()=>{var O;const p=r();u(h)&&((O=u(h))==null||O.updateTheme(k(p)))}),ge(()=>s(),()=>{var p;E(m,(p=s())==null?void 0:p.leaves)}),ge(()=>(u(h),u(N)),()=>{var p;(p=u(h))==null||p.updateIsWebviewFocused(u(N))}),Kt(),Lt(),ze("message",Wt,function(...p){var O,w;(w=(O=u(h))==null?void 0:O.handleMessageFromExtension)==null||w.apply(this,p)}),ze("focus",Wt,()=>E(N,!0)),ze("blur",Wt,()=>E(N,!1)),pn.Root(_,{children:(p,O)=>{var w=Un(),J=pe(w),W=X=>{var T=Wn(),oe=ne(T);Dn(pe(oe),{get diffViewModel(){return s()}}),Tn(K(oe,2),{get diffViewModel(){return s()}}),V(X,T)};we(J,X=>{s()&&X(W)});var P=K(J,2),D=pe(P);Et(D,X=>E(v,X),()=>u(v));var ie=K(D,2),re=X=>{var T=Ht(),oe=ne(T);en(oe,1,()=>u(m),tn,(se,Q,me)=>{var c=Ht(),I=ne(c),M=L=>{const B=dt(()=>(s(),fe(()=>{var F;return((F=s())==null?void 0:F.currFocusedChunkIdx)===me})));En(L,{get isFocused(){return u(B)},onAccept:()=>{var F;return(F=s())==null?void 0:F.acceptChunk(u(Q))},onReject:()=>{var F;return(F=s())==null?void 0:F.rejectChunk(u(Q))},get diffViewModel(){return s()},get leaf(){return u(Q)},align:"right",get disableApply(){return ue(u(l),"$disableApply",i)}})};we(I,L=>{u(Q),fe(()=>u(Q).unitOfCodeWork.modifiedCode!==u(Q).unitOfCodeWork.originalCode)&&L(M)}),V(se,c)}),V(X,T)};we(ie,X=>{s(),u(m),g(),fe(()=>{var T;return s()&&((T=u(m))==null?void 0:T.length)&&!g()})&&X(re)}),V(p,w)},$$slots:{default:!0}}),$t(),a()},{target:document.getElementById("app")});
