import{x as L,y as T,z,m as C,A as j,B as k,C as s,D as q,F as E,G as u,a as S,H as M,I as N,J as O,u as P,K as Q,o as e,b as n,L as R}from"./legacy-YP6Kq8lu.js";import{s as U,l as p,p as l,d as V,T as W,a as w}from"./SpinnerAugment-Dpcl1cXc.js";var X=u('<div class="c-callout-icon svelte-149tvwv"><!></div>'),Y=u('<!> <div class="c-callout-body svelte-149tvwv"><!></div>',1),Z=u("<div><!></div>");function aa(y,a){const b=U(a),x=p(a,["children","$$slots","$$events","$$legacy"]),t=p(x,["color","variant","size","highContrast"]);L(a,!1);const o=C(),c=C();let d=l(a,"color",8,"info"),A=l(a,"variant",8,"soft"),$=l(a,"size",8,2),B=l(a,"highContrast",8,!1);const F=$();T(()=>(s(o),s(c),k(t)),()=>{z(o,t.class),z(c,j(t,["class"]))}),q(),E();var i=Z();S(i,(v,h)=>({...v,class:`c-callout c-callout--${d()} c-callout--${A()} c-callout--size-${$()} ${s(o)}`,...s(c),[M]:h}),[()=>V(d()),()=>({"c-callout--highContrast":B()})],"svelte-149tvwv");var G=e(i);W(G,{get size(){return F},children:(v,h)=>{var f=Y(),g=N(f),H=r=>{var m=X(),K=e(m);w(K,a,"icon",{},null),n(r,m)};O(g,r=>{P(()=>b.icon)&&r(H)});var I=Q(g,2),J=e(I);w(J,a,"default",{},null),n(v,f)},$$slots:{default:!0}}),n(y,i),R()}export{aa as C};
