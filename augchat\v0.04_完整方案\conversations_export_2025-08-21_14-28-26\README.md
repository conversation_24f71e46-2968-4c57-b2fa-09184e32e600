# Augment聊天记录导出

## 📊 导出统计

- **导出时间**: 2025/8/21 14:28:26
- **工作区数量**: 19
- **总对话数**: 10
- **生成文件数**: 10
- **总消息数**: 1097

## 📁 按工作区分类

### 🏢 219eaf1da08a (4 个对话)

- [现在有新的问题，就是导出来的不适合预览啊 正常都是一个一个对话，一个对话是一个完整的文件，可以mar...](./219eaf1da08a_现在有新的问题，就是导出来的不适合预览啊_正常都是一个一个对话，一个对话是一个完整的文件，可以mar..._b78bd351.md) (313 条消息)
- [前端代码量如此巨大吗](./219eaf1da08a_前端代码量如此巨大吗_0510fdf2.md) (138 条消息)
- [tenant_id、beta 这两列的作用](./219eaf1da08a_tenant_id、beta_这两列的作用_9eede235.md) (12 条消息)
- [我在RAGFlow系统中尝试添加大语言模型时遇到连接错误。具体情况如下： **问题描述：** - 在...](./219eaf1da08a_我在RAGFlow系统中尝试添加大语言模型时遇到连接错误。具体情况如下：_问题描述：_-_在..._c01bae6e.md) (9 条消息)

### 🏢 be0818f388a0 (6 个对话)

- [有内容但是看不见](./be0818f388a0_有内容但是看不见_b286fd40.md) (277 条消息)
- [文件管理查看结果  弹出框   样式问题 看不见](./be0818f388a0_文件管理查看结果_弹出框_样式问题_看不见_257b15d9.md) (2 条消息)
- [当前项目的前端和后端代码库中，git status 显示有大量混乱的文件更改需要整理。具体问题包括：...](./be0818f388a0_当前项目的前端和后端代码库中，git_status_显示有大量混乱的文件更改需要整理。具体问题包括：..._7f183491.md) (14 条消息)
- [Request URL http://localhost:8000/api/iot/v1/docum...](./be0818f388a0_Request_URL_http_localhost_8000_api_iot_v1_docum..._c593db49.md) (39 条消息)
- [不修改ragflow代码  有解决方法澳门](./be0818f388a0_不修改ragflow代码_有解决方法澳门_93168904.md) (276 条消息)
- [文档分块内容下方的列表有内容但是显示有问题，导致看不见](./be0818f388a0_文档分块内容下方的列表有内容但是显示有问题，导致看不见_d84042f4.md) (17 条消息)

## 🕒 最近对话 (按时间排序)

- [现在有新的问题，就是导出来的不适合预览啊 正常都是一个一个对话，一个对话是一个完整的文件，可以mar...](./219eaf1da08a_现在有新的问题，就是导出来的不适合预览啊_正常都是一个一个对话，一个对话是一个完整的文件，可以mar..._b78bd351.md) - Invalid Date (313 条消息)
- [有内容但是看不见](./be0818f388a0_有内容但是看不见_b286fd40.md) - Invalid Date (277 条消息)
- [文件管理查看结果  弹出框   样式问题 看不见](./be0818f388a0_文件管理查看结果_弹出框_样式问题_看不见_257b15d9.md) - Invalid Date (2 条消息)
- [当前项目的前端和后端代码库中，git status 显示有大量混乱的文件更改需要整理。具体问题包括：...](./be0818f388a0_当前项目的前端和后端代码库中，git_status_显示有大量混乱的文件更改需要整理。具体问题包括：..._7f183491.md) - Invalid Date (14 条消息)
- [Request URL http://localhost:8000/api/iot/v1/docum...](./be0818f388a0_Request_URL_http_localhost_8000_api_iot_v1_docum..._c593db49.md) - Invalid Date (39 条消息)
- [不修改ragflow代码  有解决方法澳门](./be0818f388a0_不修改ragflow代码_有解决方法澳门_93168904.md) - Invalid Date (276 条消息)
- [前端代码量如此巨大吗](./219eaf1da08a_前端代码量如此巨大吗_0510fdf2.md) - 2025/8/21 14:28:27 (138 条消息)
- [tenant_id、beta 这两列的作用](./219eaf1da08a_tenant_id、beta_这两列的作用_9eede235.md) - 2025/8/21 14:28:27 (12 条消息)
- [我在RAGFlow系统中尝试添加大语言模型时遇到连接错误。具体情况如下： **问题描述：** - 在...](./219eaf1da08a_我在RAGFlow系统中尝试添加大语言模型时遇到连接错误。具体情况如下：_问题描述：_-_在..._c01bae6e.md) - 2025/8/21 14:28:27 (9 条消息)
- [文档分块内容下方的列表有内容但是显示有问题，导致看不见](./be0818f388a0_文档分块内容下方的列表有内容但是显示有问题，导致看不见_d84042f4.md) - 2025/8/21 14:28:27 (17 条消息)

---

*生成时间: 2025/8/21 14:28:27*
*工具版本: Augment聊天记录完整导出器 v3.0*
