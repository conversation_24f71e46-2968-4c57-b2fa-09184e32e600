import{x as X,T as Y,F as Z,G as w,I as ee,o,K as r,J as I,C as g,m as S,b as $,L as te,z as f,N as F,O as se,P as ae}from"./legacy-YP6Kq8lu.js";import{l as K,p as s,a as h,h as re,T as V}from"./SpinnerAugment-Dpcl1cXc.js";import"./IconButtonAugment-CbpcmeFk.js";import"./BaseTextInput-Br9yLRnx.js";import{T as ne}from"./TextAreaAugment-DXi02sx3.js";import{l as oe}from"./lodash-DfmeyYaq.js";var ce=w('<div class="l-markdown-editor svelte-1dcrmc3"><div class="c-markdown-editor__header svelte-1dcrmc3"><!></div> <!> <div class="c-markdown-editor__content svelte-1dcrmc3"><!> <!></div></div> <div class="c-markdown-editor__status svelte-1dcrmc3"><!> <!></div>',1);function pe(C,t){const D=K(t,["children","$$slots","$$events","$$legacy"]),G=K(D,["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"]);X(t,!1);let y,J=s(t,"variant",8,"surface"),L=s(t,"size",8,2),M=s(t,"color",24,()=>{}),N=s(t,"resize",8,"none"),a=s(t,"textInput",28,()=>{}),c=s(t,"value",12,""),k=s(t,"selectedText",12,""),l=s(t,"selectionStart",12,0),i=s(t,"selectionEnd",12,0),O=s(t,"saveFunction",8),P=s(t,"debounceValue",8,2500),d=S(!1),u=S();const v=async()=>{try{O()(),f(d,!0),clearTimeout(y),y=setTimeout(()=>{f(d,!1)},1500)}catch(e){f(u,e instanceof Error?e.message:String(e))}};function m(){a()&&(l(a().selectionStart),i(a().selectionEnd),l()!==i()?k(c().substring(l(),i())):k(""))}const j=oe.debounce(v,P());Y(()=>{v()}),Z();var z=ce(),x=ee(z),T=o(x),q=o(T);h(q,t,"header",{},null);var E=r(T,2);h(E,t,"default",{},null);var A=r(E,2),_=o(A);h(_,t,"title",{},null);var B=r(_,2);ne(B,re({get variant(){return J()},get size(){return L()},get color(){return M()},get resize(){return N()},placeholder:"Enter markdown content..."},()=>G,{get textInput(){return a()},set textInput(e){a(e)},get value(){return c()},set value(e){c(e)},$$events:{select:m,mouseup:m,keyup:()=>{m()},input:j,keydown:e=>{(e.key==="Escape"||(e.metaKey||e.ctrlKey)&&e.key==="s")&&(e.preventDefault(),v())}},$$legacy:!0}));var H=r(x,2),b=o(H),Q=e=>{V(e,{size:1,weight:"light",color:"error",children:(p,W)=>{var n=F();se(()=>ae(n,g(u))),$(p,n)},$$slots:{default:!0}})};I(b,e=>{g(u)&&e(Q)});var R=r(b,2),U=e=>{V(e,{size:1,weight:"light",color:"success",children:(p,W)=>{var n=F("Saved");$(p,n)},$$slots:{default:!0}})};I(R,e=>{g(d)&&e(U)}),$(C,z),te()}export{pe as M};
