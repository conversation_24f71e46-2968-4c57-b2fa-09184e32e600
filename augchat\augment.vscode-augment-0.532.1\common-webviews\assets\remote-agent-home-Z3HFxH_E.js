import{f as Ze,a as Ve,o as _,b as i,x as Se,m as T,T as Be,F as ke,G as h,N as se,O as U,P as le,K as p,I as Y,J as P,u as o,B as k,C as e,L as ye,z as A,y as J,D as Ce,Q as b,Y as Ge,_ as Xe,M as et,a1 as Ue,R as Ee,S as Ne,a8 as tt,a9 as st,ap as at}from"./legacy-YP6Kq8lu.js";import{l as nt,p as x,T as ae,e as Ye,b as Je,S as rt}from"./SpinnerAugment-Dpcl1cXc.js";import"./design-system-init-BkqeNcXX.js";/* empty css                                */import{e as ue,i as it,h as ot}from"./host-BNehKqab.js";import{M as lt}from"./message-broker-DRrss2z_.js";import{S as dt,T as ct,a as Qe,b as vt,c as Me,d as gt,v as ut,e as mt}from"./StatusIndicator-Bj45ZHB5.js";import{R as X,s as pt,a as Te}from"./index-B528snJk.js";import{T as Oe,a as oe,C as ft}from"./CardAugment-YBzgmAzG.js";import{C as wt}from"./CalloutAugment-0Y9u1WCc.js";import{h as ht,I as je}from"./IconButtonAugment-CbpcmeFk.js";import{E as _t}from"./exclamation-triangle-CZAYyoF0.js";import{d as $t,s as St,R as qe}from"./remote-agents-client-DbhVjGoZ.js";import{A as kt}from"./augment-logo-D8bZBTPs.js";import"./async-messaging-gS_K9w3p.js";import"./event-modifiers-Bz4QCcZc.js";import"./chat-types-BfwvR7Kn.js";import"./types-CGlLNakm.js";var yt=Ze("<svg><!></svg>"),xt=h(" <!>",1),bt=h('<div class="agent-card-footer svelte-1qwlkoj"><!> <div class="time-container"><!></div></div>'),At=h('<div class="task-text-container svelte-1tatwxk"><!></div>'),Pt=h('<div class="task-status-indicator svelte-1tatwxk"><!></div>'),Rt=h('<div class="task-item svelte-1tatwxk"><div></div> <!> <!></div>'),zt=h(' <button class="error-dismiss svelte-1bxdvw4" aria-label="Dismiss error">×</button>',1),It=h('<div class="deletion-error svelte-1bxdvw4"><!></div>'),Ft=h('<span class="setup-script-title svelte-1bxdvw4">Generate a setup script</span>'),Ht=h('<div class="setup-script-title-container svelte-1bxdvw4"><div class="setup-script-badge svelte-1bxdvw4"><!></div> <!></div>'),Tt=h('<div class="tasks-list svelte-1bxdvw4"></div>'),Ot=h('<div class="card-header svelte-1bxdvw4"><div class="session-summary-container svelte-1bxdvw4"><!></div> <div class="card-info"><!></div></div> <div class="card-content svelte-1bxdvw4"><!></div> <div class="card-actions svelte-1bxdvw4"><!> <!> <!></div> <!>',1),qt=h("<div><!> <!></div>");function Ct(de,S){Se(S,!1);const O=T(),I=T(),R=T(),j=T();let d=x(S,"agent",8),K=x(S,"selected",8,!1),F=x(S,"isPinned",8,!1),H=x(S,"onSelect",8),M=x(S,"onDelete",8),q=x(S,"deletionError",12,null),B=x(S,"isDeleting",8,!1),v=x(S,"onTogglePinned",24,()=>{}),E=x(S,"sshConfig",24,()=>{});function G(){q(null)}Be(()=>{G()}),J(()=>(e(O),e(I),k(E())),()=>{var $;$=E()||{onSSH:()=>Promise.resolve(!1),canSSH:!1},A(O,$.onSSH),A(I,$.canSSH)}),J(()=>k(d()),()=>{A(R,d().turn_summaries||[])}),J(()=>{},()=>{A(j,!0)}),Ce(),ke();var c=qt();let ce;var me=_(c),g=$=>{var ne=It(),y=_(ne);wt(y,{variant:"soft",color:"error",size:1,children:(s,l)=>{var N=zt(),ee=Y(N),D=p(ee);U(()=>le(ee,`${q()??""} `)),Ue("click",D,G),i(s,N)},$$slots:{default:!0,icon:(s,l)=>{_t(s,{slot:"icon"})}}}),i($,ne)};P(me,$=>{q()&&$(g)});var Z=p(me,2);ft(Z,{variant:"surface",size:2,interactive:!0,class:"agent-card",$$events:{click:()=>H()(d().remote_agent_id),keydown:$=>$.key==="Enter"&&H()(d().remote_agent_id)},children:($,ne)=>{var y=Ot(),s=Y(y),l=_(s),N=_(l),ee=a=>{var r=Ht(),w=_(r),n=_(w);Qe(n);var t=p(w,2);ae(t,{size:2,weight:"medium",children:(m,u)=>{var Q=Ft();i(m,Q)},$$slots:{default:!0}}),i(a,r)},D=a=>{ae(a,{size:2,weight:"medium",class:"session-text",children:(r,w)=>{var n=se();U(()=>le(n,(k(d()),o(()=>d().session_summary)))),i(r,n)},$$slots:{default:!0}})};P(N,a=>{k(d()),o(()=>d().is_setup_script_agent)?a(ee):a(D,!1)});var L=p(l,2),ve=_(L);dt(ve,{get status(){return k(d()),o(()=>d().status)},get workspaceStatus(){return k(d()),o(()=>d().workspace_status)},isExpanded:!0,get hasUpdates(){return k(d()),o(()=>d().has_updates)}});var re=p(s,2),xe=_(re),be=a=>{var r=Tt();ue(r,5,()=>(e(R),o(()=>e(R).slice(0,3))),it,(w,n)=>{(function(t,m){Se(m,!1);const u=T();let Q=x(m,"text",8),W=x(m,"status",8,"info");J(()=>k(W()),()=>{A(u,function(C){switch(C){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(W()))}),Ce(),ke();var pe=Rt(),fe=_(pe),z=p(fe,2);const Pe=b(()=>(k(oe),o(()=>[oe.Hover])));Oe(z,{get content(){return Q()},get triggerOn(){return e(Pe)},maxWidth:"400px",children:(C,ge)=>{var he=At(),V=_(he);ae(V,{size:1,color:"secondary",children:(ie,Ke)=>{var Re=se();U(()=>le(Re,Q())),i(ie,Re)},$$slots:{default:!0}}),i(C,he)},$$slots:{default:!0}});var te=p(z,2),we=C=>{var ge=Pt(),he=_(ge);const V=b(()=>W()==="error"?"error":"neutral");ae(he,{size:1,get color(){return e(V)},children:(ie,Ke)=>{var Re=se();U(()=>le(Re,W()==="error"?"!":W()==="warning"?"⚠":"")),i(ie,Re)},$$slots:{default:!0}}),i(C,ge)};P(te,C=>{W()!=="error"&&W()!=="warning"||C(we)}),U(()=>Ge(fe,1,`bullet-point ${e(u)??""}`,"svelte-1tatwxk")),i(t,pe),ye()})(w,{get text(){return e(n)},status:"success"})}),i(a,r)};P(xe,a=>{e(R),o(()=>e(R).length>0)&&a(be)});var Ae=p(re,2),ze=_(Ae),Ie=a=>{const r=b(()=>F()?"Unpin agent":"Pin agent"),w=b(()=>(k(oe),o(()=>[oe.Hover])));Oe(a,{get content(){return e(r)},get triggerOn(){return e(w)},side:"top",children:(n,t)=>{je(n,{variant:"ghost",color:"neutral",size:1,$$events:{click:m=>{m.stopPropagation(),v()()}},children:(m,u)=>{var Q=et(),W=Y(Q),pe=z=>{(function(Pe,te){const we=nt(te,["children","$$slots","$$events","$$legacy"]);var C=yt();Ve(C,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 640 512",...we}));var ge=_(C);ht(ge,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',!0),i(Pe,C)})(z,{})},fe=z=>{vt(z,{})};P(W,z=>{F()?z(pe):z(fe,!1)}),i(m,Q)},$$slots:{default:!0}})},$$slots:{default:!0}})};P(ze,a=>{v()&&a(Ie)});var Fe=p(ze,2),He=a=>{const r=b(()=>(k(oe),o(()=>[oe.Hover])));Oe(a,{content:"SSH to agent",get triggerOn(){return e(r)},side:"top",children:(w,n)=>{const t=b(()=>!e(I)),m=b(()=>e(I)?"SSH to agent":"SSH to agent (agent must be running or idle)");je(w,{get disabled(){return e(t)},variant:"ghost",color:"neutral",size:1,get title(){return e(m)},$$events:{click:u=>{u.stopPropagation(),e(O)()}},children:(u,Q)=>{Qe(u)},$$slots:{default:!0}})},$$slots:{default:!0}})};P(Fe,a=>{E()&&a(He)});var De=p(Fe,2);const Le=b(()=>(k(oe),o(()=>[oe.Hover])));Oe(De,{content:"Delete agent",get triggerOn(){return e(Le)},side:"top",children:(a,r)=>{const w=b(()=>B()?"Deleting agent...":"Delete agent");je(a,{variant:"ghost",color:"neutral",size:1,get disabled(){return B()},get title(){return e(w)},$$events:{click:n=>{n.stopPropagation(),M()(d().remote_agent_id)}},children:(n,t)=>{ct(n)},$$slots:{default:!0}})},$$slots:{default:!0}});var We=p(Ae,2);const f=b(()=>(k(d()),o(()=>d().updated_at||d().started_at)));(function(a,r){Se(r,!1);let w=x(r,"isRemote",8,!1),n=x(r,"status",8),t=x(r,"timestamp",8),m=T($t(t()));const u=St(t(),z=>{A(m,z)});Be(()=>{u()}),ke();var Q=bt(),W=_(Q);ae(W,{size:1,color:"secondary",class:"location-text",children:(z,Pe)=>{var te=se();U(()=>le(te,w()?"Running in the cloud":"Running locally")),i(z,te)},$$slots:{default:!0}});var pe=p(W,2),fe=_(pe);ae(fe,{size:1,color:"secondary",class:"time-text",children:(z,Pe)=>{var te=xt(),we=Y(te),C=p(we),ge=V=>{var ie=se();U(()=>le(ie,e(m))),i(V,ie)},he=V=>{var ie=se("Unknown time");i(V,ie)};P(C,V=>{t()?V(ge):V(he,!1)}),U(()=>le(we,`${k(n()),k(X),o(()=>n()===X.agentRunning?"Last updated":"Started")??""} `)),i(z,te)},$$slots:{default:!0}}),i(a,Q),ye()})(We,{get isRemote(){return e(j)},get status(){return k(d()),o(()=>d().status)},get timestamp(){return e(f)}}),U(()=>Xe(l,"title",(k(d()),o(()=>d().is_setup_script_agent?"Generate a setup script":d().session_summary)))),i($,y)},$$slots:{default:!0}}),U($=>ce=Ge(c,1,"card-wrapper svelte-1bxdvw4",null,ce,$),[()=>({"selected-card":K(),"setup-script-card":d().is_setup_script_agent,deleting:B()})],b),i(de,c),ye()}function _e(de,S){Se(S,!1);const[O,I]=Je(),R=()=>Ye(B,"$sharedWebviewStore",O),j=T(),d=T(),K=T();let F=x(S,"agent",8),H=x(S,"selected",8,!1),M=x(S,"onSelect",8);const q=Ee(qe.key),B=Ee(Me);let v=T(!1),E=T(null),G=null;function c(){A(E,null),G&&(clearTimeout(G),G=null)}async function ce(){return!!e(K)&&await(async g=>await q.sshToRemoteAgent(g.remote_agent_id))(F())}J(()=>R(),()=>{var g;A(j,((g=R().state)==null?void 0:g.pinnedAgents)||{})}),J(()=>(e(j),k(F())),()=>{var g;A(d,((g=e(j))==null?void 0:g[F().remote_agent_id])===!0)}),J(()=>(k(F()),X),()=>{A(K,F().status===X.agentRunning||F().status===X.agentIdle)}),Ce(),ke();const me=b(()=>({onSSH:ce,canSSH:e(K)}));Ct(de,{get agent(){return F()},get selected(){return H()},get isPinned(){return e(d)},get onSelect(){return M()},onDelete:()=>async function(g){var $,ne;c(),A(v,!0);const Z=(($=R().state)==null?void 0:$.agentOverviews)||[];try{if(!await q.deleteRemoteAgent(g))throw new Error("Failed to delete agent");if(B.update(y=>{if(y)return{...y,agentOverviews:y.agentOverviews.filter(s=>s.remote_agent_id!==g)}}),(((ne=R().state)==null?void 0:ne.pinnedAgents)||{})[g])try{await q.deletePinnedAgentFromStore(g);const y=await q.getPinnedAgentsFromStore();B.update(s=>{if(s)return{...s,pinnedAgents:y}})}catch(y){console.error("Failed to remove pinned status:",y)}}catch(y){console.error("Failed to delete agent:",y),B.update(s=>{if(s)return{...s,agentOverviews:Z}}),A(E,y instanceof Error?y.message:"Failed to delete agent"),G=setTimeout(()=>{c()},5e3)}finally{A(v,!1)}}(F().remote_agent_id),onTogglePinned:()=>async function(g){try{e(d)?await q.deletePinnedAgentFromStore(g):await q.savePinnedAgentToStore(g,!0);const Z=await q.getPinnedAgentsFromStore();B.update($=>{if($)return{...$,pinnedAgents:Z}})}catch(Z){console.error("Failed to toggle pinned status:",Z)}}(F().remote_agent_id),get sshConfig(){return e(me)},get deletionError(){return e(E)},set deletionError(g){A(E,g)},get isDeleting(){return e(v)},set isDeleting(g){A(v,g)},$$legacy:!0}),ye(),I()}var Et=h('<div class="section-header svelte-1tegnqi"><!></div>');function $e(de,S){let O=x(S,"title",8);var I=Et(),R=_(I);ae(R,{size:2,color:"secondary",children:(j,d)=>{var K=se();U(()=>le(K,O())),i(j,K)},$$slots:{default:!0}}),i(de,I)}var Dt=h('<div class="empty-state svelte-aiqmvp"><div class="l-loading-container svelte-aiqmvp"><!> <!></div></div>'),Lt=h('<div class="empty-state svelte-aiqmvp"><!></div>'),Wt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),jt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Mt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Bt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Gt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Nt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Qt=h("<!> <!> <!> <!> <!> <!>",1),Ut=h('<div class="agent-list svelte-aiqmvp"><!></div>'),Yt=h('<div class="l-main svelte-1941nw6"><h1 class="l-main__title svelte-1941nw6"><span class="l-main__title-logo svelte-1941nw6"><!></span> Remote Agents</h1> <!></div>');at(function(de,S){Se(S,!1);const O=new lt(ot),I=new gt(O,void 0,ut,mt);O.registerConsumer(I),Ne(Me,I);const R=new qe(O);Ne(qe.key,R),tt(()=>(I.fetchStateFromExtension().then(()=>{I.update(H=>{if(!H)return;const M=[...H.activeWebviews,"home"];return H.pinnedAgents?{...H,activeWebviews:M}:{...H,activeWebviews:M,pinnedAgents:{}}})}),()=>{O.dispose(),R.dispose()})),ke();var j=Yt();Ue("message",st,function(...H){var M;(M=O.onMessageFromExtension)==null||M.apply(this,H)});var d=_(j),K=_(d),F=_(K);kt(F),function(H,M){Se(M,!1);const[q,B]=Je(),v=()=>Ye(ce,"$sharedWebviewStore",q),E=T(),G=T(),c=T(),ce=Ee(Me),me=Ee(qe.key);function g(s){ce.update(l=>{if(l)return{...l,selectedAgentId:s}})}J(()=>v(),()=>{var s;A(E,pt(((s=v().state)==null?void 0:s.agentOverviews)||[]))}),J(()=>v(),()=>{var s;A(G,((s=v().state)==null?void 0:s.pinnedAgents)||{})}),J(()=>(e(E),e(G),Te),()=>{A(c,e(E).reduce((s,l)=>{var N;return((N=e(G))==null?void 0:N[l.remote_agent_id])===!0?s.pinned.push(l):l.status===X.agentIdle&&l.has_updates?s.readyToReview.push(l):l.status===X.agentRunning||l.status===X.agentStarting||l.workspace_status===Te.workspaceResuming?s.running.push(l):l.status===X.agentFailed?s.failed.push(l):l.status===X.agentIdle||l.workspace_status===Te.workspacePaused||l.workspace_status===Te.workspacePausing?s.idle.push(l):s.additional.push(l),s},{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]}))}),J(()=>v(),()=>{var s;(s=v().state)!=null&&s.agentOverviews||me.focusAugmentPanel()}),Ce(),ke();var Z=Ut(),$=_(Z),ne=s=>{var l=Dt(),N=_(l),ee=_(N);rt(ee,{});var D=p(ee,2);ae(D,{size:3,color:"secondary",children:(L,ve)=>{var re=se("Loading the Augment panel...");i(L,re)},$$slots:{default:!0}}),i(s,l)},y=(s,l)=>{var N=D=>{var L=Lt(),ve=_(L);ae(ve,{size:3,color:"secondary",children:(re,xe)=>{var be=se("No agents available");i(re,be)},$$slots:{default:!0}}),i(D,L)},ee=D=>{var L=Qt(),ve=Y(L),re=f=>{var a=Wt(),r=Y(a);$e(r,{title:"Pinned"});var w=p(r,2);ue(w,7,()=>(e(c),o(()=>e(c).pinned)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(f,a)};P(ve,f=>{e(c),o(()=>e(c).pinned.length>0)&&f(re)});var xe=p(ve,2),be=f=>{var a=jt(),r=Y(a);$e(r,{title:"Ready to review"});var w=p(r,2);ue(w,7,()=>(e(c),o(()=>e(c).readyToReview)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(f,a)};P(xe,f=>{e(c),o(()=>e(c).readyToReview.length>0)&&f(be)});var Ae=p(xe,2),ze=f=>{var a=Mt(),r=Y(a);$e(r,{title:"Running agents"});var w=p(r,2);ue(w,7,()=>(e(c),o(()=>e(c).running)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(f,a)};P(Ae,f=>{e(c),o(()=>e(c).running.length>0)&&f(ze)});var Ie=p(Ae,2),Fe=f=>{var a=Bt(),r=Y(a);$e(r,{title:"Idle agents"});var w=p(r,2);ue(w,7,()=>(e(c),o(()=>e(c).idle)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(f,a)};P(Ie,f=>{e(c),o(()=>e(c).idle.length>0)&&f(Fe)});var He=p(Ie,2),De=f=>{var a=Gt(),r=Y(a);$e(r,{title:"Failed agents"});var w=p(r,2);ue(w,7,()=>(e(c),o(()=>e(c).failed)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(f,a)};P(He,f=>{e(c),o(()=>e(c).failed.length>0)&&f(De)});var Le=p(He,2),We=f=>{var a=Nt(),r=Y(a);$e(r,{title:"Other agents"});var w=p(r,2);ue(w,7,()=>(e(c),o(()=>e(c).additional)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(f,a)};P(Le,f=>{e(c),o(()=>e(c).additional.length>0)&&f(We)}),i(D,L)};P(s,D=>{v(),o(()=>{var L;return((L=v().state)==null?void 0:L.agentOverviews.length)===0})?D(N):D(ee,!1)},l)};P($,s=>{v(),o(()=>{var l;return!((l=v().state)!=null&&l.agentOverviews)})?s(ne):s(y,!1)}),i(H,Z),ye(),B()}(p(d,2),{}),i(de,j),ye()},{target:document.getElementById("app")});
