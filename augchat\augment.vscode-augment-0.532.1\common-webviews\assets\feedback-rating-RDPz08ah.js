function a(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}const g=6048e5,l=6e4,b=36e5;function r(e){const t=a(e);return t.setHours(0,0,0,0),t}function s(e){const t=a(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function f(e,t){const n=r(e),o=r(t),c=+n-s(n),u=+o-s(o);return Math.round((c-u)/864e5)}var i=(e=>(e[e.unset=0]="unset",e[e.positive=1]="positive",e[e.negative=2]="negative",e))(i||{});export{i as F,b as a,l as b,f as d,g as m,a as t};
