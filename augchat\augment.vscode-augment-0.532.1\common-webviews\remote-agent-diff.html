<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agent Diff</title>
    <script nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <script type="module" crossorigin src="./assets/remote-agent-diff-4q6FEmf1.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw=="></script>
    <link rel="modulepreload" crossorigin href="./assets/legacy-YP6Kq8lu.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-Dpcl1cXc.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BkqeNcXX.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/host-BNehKqab.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-gS_K9w3p.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-DRrss2z_.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-BNum2ZUy.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-CbpcmeFk.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/event-modifiers-Bz4QCcZc.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-YBzgmAzG.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-DFL7wB0Y.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-DOexUbEr.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-Uytug4gU.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CaEmYw0i.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-Dfz0pJHr.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-4vhrZf9p.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-hRm--fCg.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-BqCUC_IY.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-DQWBC8l_.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-DkEdzEZO.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-j5PxZ6X_.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-BfwvR7Kn.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/file-type-utils-D6OEcQY2.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-D5Xb9jVX.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-CZAYyoF0.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-DiEnp4Ss.js" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-ClvxyORV.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-BIwUn_6v.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-MyOXHVsl.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-diff-Bzk3Sgw8.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-panel-base-CVwMZATI.css" nonce="nonce-30Sz0DCsPR61+e4td6fSrw==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
