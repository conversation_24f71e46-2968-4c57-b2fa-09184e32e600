#!/usr/bin/env node

/**
 * 消息排序测试脚本
 * 用于验证消息排序逻辑的修复效果
 */

const fs = require('fs');
const path = require('path');

// 测试排序逻辑
function testMessageSorting() {
    console.log('🧪 测试消息排序逻辑...\n');
    
    // 模拟不同类型的消息数据
    const testMessages = [
        {
            timestamp: '2025-08-21T06:30:00.000Z',
            request_message: '第三条消息（时间戳）',
            source: 'strings'
        },
        {
            exchangeId: '1',
            request_message: '第一条消息（exchangeId=1）',
            source: 'leveldb'
        },
        {
            timestamp: '2025-08-21T06:15:00.000Z',
            request_message: '第二条消息（时间戳）',
            source: 'strings'
        },
        {
            exchangeId: '10',
            request_message: '第五条消息（exchangeId=10）',
            source: 'leveldb'
        },
        {
            exchangeId: '2',
            request_message: '第四条消息（exchangeId=2）',
            source: 'leveldb'
        },
        {
            timestamp: '2025-08-21T06:45:00.000Z',
            request_message: '第六条消息（时间戳）',
            source: 'strings'
        }
    ];
    
    console.log('📋 原始消息顺序:');
    testMessages.forEach((msg, index) => {
        const timeInfo = msg.timestamp ? `时间戳: ${msg.timestamp}` : `exchangeId: ${msg.exchangeId}`;
        console.log(`  ${index + 1}. ${msg.request_message} (${timeInfo})`);
    });
    
    // 应用排序逻辑（与主脚本相同）
    const sortedMessages = testMessages.sort((a, b) => {
        // 获取排序键的函数
        const getSortKey = (message) => {
            // 优先使用timestamp（ISO格式）
            if (message.timestamp) {
                return new Date(message.timestamp).getTime();
            }
            // 其次使用exchangeId（通常是递增的数字）
            if (message.exchangeId) {
                // exchangeId通常是数字字符串，转换为数字进行排序
                const numericId = parseInt(message.exchangeId, 10);
                return isNaN(numericId) ? 0 : numericId;
            }
            // 最后使用uuid的时间戳部分（如果是时间相关的）
            if (message.uuid) {
                // 尝试从uuid中提取时间信息，如果失败则返回0
                try {
                    // 某些uuid可能包含时间戳信息
                    const uuidParts = message.uuid.split('-');
                    if (uuidParts.length > 0) {
                        const firstPart = parseInt(uuidParts[0], 16);
                        return isNaN(firstPart) ? 0 : firstPart;
                    }
                } catch (e) {
                    // 忽略解析错误
                }
            }
            return 0;
        };
        
        const timeA = getSortKey(a);
        const timeB = getSortKey(b);
        
        // 数字排序
        return timeA - timeB;
    });
    
    console.log('\n✅ 排序后的消息顺序:');
    sortedMessages.forEach((msg, index) => {
        const timeInfo = msg.timestamp ? 
            `时间戳: ${msg.timestamp} (${new Date(msg.timestamp).getTime()})` : 
            `exchangeId: ${msg.exchangeId} (${parseInt(msg.exchangeId, 10)})`;
        console.log(`  ${index + 1}. ${msg.request_message} (${timeInfo})`);
    });
    
    // 验证排序结果
    console.log('\n🔍 排序验证:');
    let isCorrect = true;
    const expectedOrder = [
        '第一条消息（exchangeId=1）',
        '第四条消息（exchangeId=2）',
        '第五条消息（exchangeId=10）',
        '第二条消息（时间戳）',
        '第三条消息（时间戳）',
        '第六条消息（时间戳）'
    ];
    
    for (let i = 0; i < expectedOrder.length; i++) {
        const expected = expectedOrder[i];
        const actual = sortedMessages[i].request_message;
        if (expected === actual) {
            console.log(`  ✅ 位置 ${i + 1}: ${actual}`);
        } else {
            console.log(`  ❌ 位置 ${i + 1}: 期望 "${expected}", 实际 "${actual}"`);
            isCorrect = false;
        }
    }
    
    console.log(`\n🎯 排序测试结果: ${isCorrect ? '✅ 通过' : '❌ 失败'}`);
    return isCorrect;
}

// 分析实际导出文件中的消息排序
function analyzeExportedFile() {
    console.log('\n📊 分析实际导出文件...\n');
    
    // 查找最新的导出目录
    const currentDir = __dirname;
    const files = fs.readdirSync(currentDir);
    
    let latestExportDir = null;
    for (const file of files) {
        if (file.startsWith('conversations_export_') && fs.statSync(path.join(currentDir, file)).isDirectory()) {
            if (!latestExportDir || file > latestExportDir) {
                latestExportDir = file;
            }
        }
    }
    
    if (!latestExportDir) {
        console.log('❌ 未找到导出目录');
        return;
    }
    
    console.log(`📂 分析目录: ${latestExportDir}`);
    
    // 查找目标对话文件
    const exportPath = path.join(currentDir, latestExportDir);
    const exportFiles = fs.readdirSync(exportPath);
    
    let targetFile = null;
    for (const file of exportFiles) {
        if (file.includes('b78bd351') && file.endsWith('.md')) {
            targetFile = file;
            break;
        }
    }
    
    if (!targetFile) {
        console.log('❌ 未找到目标对话文件');
        return;
    }
    
    console.log(`📄 分析文件: ${targetFile}`);
    
    // 读取文件内容并分析消息序号
    const filePath = path.join(exportPath, targetFile);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 提取消息序号
    const messagePattern = /### 消息 (\d+)/g;
    const messageNumbers = [];
    let match;
    
    while ((match = messagePattern.exec(content)) !== null) {
        messageNumbers.push(parseInt(match[1], 10));
    }
    
    console.log(`📊 发现 ${messageNumbers.length} 个消息序号`);
    console.log(`📈 序号范围: ${Math.min(...messageNumbers)} - ${Math.max(...messageNumbers)}`);
    
    // 检查序号连续性
    let discontinuities = 0;
    for (let i = 1; i < messageNumbers.length; i++) {
        if (messageNumbers[i] !== messageNumbers[i-1] + 1) {
            discontinuities++;
            console.log(`⚠️  序号不连续: ${messageNumbers[i-1]} → ${messageNumbers[i]} (位置 ${i})`);
        }
    }
    
    if (discontinuities === 0) {
        console.log('✅ 消息序号连续');
    } else {
        console.log(`❌ 发现 ${discontinuities} 个序号不连续的地方`);
    }
    
    // 检查是否有重复序号
    const uniqueNumbers = [...new Set(messageNumbers)];
    if (uniqueNumbers.length === messageNumbers.length) {
        console.log('✅ 无重复序号');
    } else {
        console.log(`❌ 发现重复序号: ${messageNumbers.length - uniqueNumbers.length} 个重复`);
    }
    
    return {
        totalMessages: messageNumbers.length,
        minNumber: Math.min(...messageNumbers),
        maxNumber: Math.max(...messageNumbers),
        discontinuities: discontinuities,
        duplicates: messageNumbers.length - uniqueNumbers.length
    };
}

// 主函数
function main() {
    console.log('🔧 消息排序修复验证工具');
    console.log('================================\n');
    
    // 测试排序逻辑
    const sortingTest = testMessageSorting();
    
    // 分析实际文件
    const fileAnalysis = analyzeExportedFile();
    
    console.log('\n📋 总结报告:');
    console.log('================================');
    console.log(`排序逻辑测试: ${sortingTest ? '✅ 通过' : '❌ 失败'}`);
    
    if (fileAnalysis) {
        console.log(`实际文件分析:`);
        console.log(`  - 总消息数: ${fileAnalysis.totalMessages}`);
        console.log(`  - 序号范围: ${fileAnalysis.minNumber} - ${fileAnalysis.maxNumber}`);
        console.log(`  - 不连续处: ${fileAnalysis.discontinuities}`);
        console.log(`  - 重复序号: ${fileAnalysis.duplicates}`);
        
        const fileTest = fileAnalysis.discontinuities === 0 && fileAnalysis.duplicates === 0;
        console.log(`  - 文件测试: ${fileTest ? '✅ 通过' : '❌ 失败'}`);
    }
    
    console.log('\n💡 建议:');
    if (sortingTest && fileAnalysis && fileAnalysis.discontinuities === 0) {
        console.log('✅ 排序修复成功！可以重新运行导出工具生成修复后的文件。');
    } else {
        console.log('⚠️  排序问题仍然存在，需要进一步调试。');
    }
}

// 运行测试
if (require.main === module) {
    main();
}

module.exports = { testMessageSorting, analyzeExportedFile };
