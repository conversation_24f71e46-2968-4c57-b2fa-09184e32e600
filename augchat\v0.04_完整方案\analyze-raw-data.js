#!/usr/bin/env node

/**
 * 原始数据分析工具
 * 深入分析VSCode数据库中的时间戳信息
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const level = require('level');

// 分析原始数据结构
async function analyzeRawData() {
    console.log('🔍 分析原始数据结构和时间戳信息...\n');
    
    // 查找VSCode工作区
    const workspaces = await findWorkspaces();
    console.log(`📂 发现 ${workspaces.length} 个工作区\n`);
    
    let totalAnalyzed = 0;
    const timeStampAnalysis = {
        withTimestamp: 0,
        withExchangeId: 0,
        withBoth: 0,
        withNeither: 0,
        timestampFormats: new Set(),
        exchangeIdFormats: new Set(),
        sampleData: []
    };
    
    for (const workspace of workspaces.slice(0, 3)) { // 分析前3个工作区
        console.log(`🔍 分析工作区: ${workspace.id.substring(0, 8)}...`);
        console.log(`   路径: ${workspace.path}`);
        
        try {
            // 分析LevelDB数据
            const leveldbData = await analyzeLevelDBData(workspace);
            console.log(`   LevelDB记录: ${leveldbData.length}`);
            
            // 分析字符串数据
            const stringsData = await analyzeStringsData(workspace);
            console.log(`   字符串记录: ${stringsData.length}`);
            
            // 合并分析
            const allData = [...leveldbData, ...stringsData];
            totalAnalyzed += allData.length;
            
            // 分析时间戳信息
            for (const item of allData) {
                const hasTimestamp = !!(item.timestamp);
                const hasExchangeId = !!(item.exchangeId);
                
                if (hasTimestamp && hasExchangeId) {
                    timeStampAnalysis.withBoth++;
                } else if (hasTimestamp) {
                    timeStampAnalysis.withTimestamp++;
                } else if (hasExchangeId) {
                    timeStampAnalysis.withExchangeId++;
                } else {
                    timeStampAnalysis.withNeither++;
                }
                
                if (hasTimestamp) {
                    timeStampAnalysis.timestampFormats.add(typeof item.timestamp);
                }
                if (hasExchangeId) {
                    timeStampAnalysis.exchangeIdFormats.add(typeof item.exchangeId);
                }
                
                // 收集样本数据
                if (timeStampAnalysis.sampleData.length < 10) {
                    timeStampAnalysis.sampleData.push({
                        source: item.source,
                        timestamp: item.timestamp,
                        exchangeId: item.exchangeId,
                        hasContent: !!(item.request_message || item.response_text)
                    });
                }
            }
            
        } catch (error) {
            console.log(`   ❌ 分析失败: ${error.message}`);
        }
        
        console.log('');
    }
    
    // 输出分析结果
    console.log('📊 时间戳分析结果:');
    console.log('================================');
    console.log(`总记录数: ${totalAnalyzed}`);
    console.log(`有timestamp: ${timeStampAnalysis.withTimestamp} (${(timeStampAnalysis.withTimestamp/totalAnalyzed*100).toFixed(1)}%)`);
    console.log(`有exchangeId: ${timeStampAnalysis.withExchangeId} (${(timeStampAnalysis.withExchangeId/totalAnalyzed*100).toFixed(1)}%)`);
    console.log(`两者都有: ${timeStampAnalysis.withBoth} (${(timeStampAnalysis.withBoth/totalAnalyzed*100).toFixed(1)}%)`);
    console.log(`两者都无: ${timeStampAnalysis.withNeither} (${(timeStampAnalysis.withNeither/totalAnalyzed*100).toFixed(1)}%)`);
    
    console.log('\n📋 数据格式:');
    console.log(`timestamp格式: ${Array.from(timeStampAnalysis.timestampFormats).join(', ')}`);
    console.log(`exchangeId格式: ${Array.from(timeStampAnalysis.exchangeIdFormats).join(', ')}`);
    
    console.log('\n🔍 样本数据:');
    timeStampAnalysis.sampleData.forEach((sample, index) => {
        console.log(`${index + 1}. 来源: ${sample.source}`);
        console.log(`   timestamp: ${sample.timestamp}`);
        console.log(`   exchangeId: ${sample.exchangeId}`);
        console.log(`   有内容: ${sample.hasContent}`);
        console.log('');
    });
    
    return timeStampAnalysis;
}

// 查找VSCode工作区
async function findWorkspaces() {
    const userDataPaths = [
        path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User', 'workspaceStorage'),
        path.join(os.homedir(), 'Library', 'Application Support', 'Code', 'User', 'workspaceStorage'),
        path.join(os.homedir(), '.config', 'Code', 'User', 'workspaceStorage')
    ];
    
    const workspaces = [];
    
    for (const basePath of userDataPaths) {
        if (fs.existsSync(basePath)) {
            const dirs = fs.readdirSync(basePath);
            for (const dir of dirs) {
                const workspacePath = path.join(basePath, dir);
                const augmentPath = path.join(workspacePath, 'augmentcode.augment');
                
                if (fs.existsSync(augmentPath)) {
                    workspaces.push({
                        id: dir,
                        path: workspacePath,
                        augmentPath: augmentPath
                    });
                }
            }
            break; // 找到第一个存在的路径就停止
        }
    }
    
    return workspaces;
}

// 分析LevelDB数据
async function analyzeLevelDBData(workspace) {
    const data = [];
    const dbPath = path.join(workspace.augmentPath, 'conversations');
    
    if (!fs.existsSync(dbPath)) {
        return data;
    }
    
    try {
        const db = level(dbPath, { valueEncoding: 'json' });
        
        for await (const [key, value] of db.iterator()) {
            if (key.startsWith('exchange:')) {
                const keyParts = key.split(':');
                if (keyParts.length >= 3) {
                    data.push({
                        source: 'leveldb',
                        conversationId: keyParts[1],
                        exchangeId: keyParts[2],
                        timestamp: value.timestamp || null,
                        request_message: value.request_message || '',
                        response_text: value.response_text || '',
                        rawValue: value
                    });
                }
            }
        }
        
        await db.close();
    } catch (error) {
        console.log(`   LevelDB错误: ${error.message}`);
    }
    
    return data;
}

// 分析字符串数据
async function analyzeStringsData(workspace) {
    const data = [];
    const logFiles = [];
    
    // 查找日志文件
    function findLogFiles(dir) {
        if (!fs.existsSync(dir)) return;
        
        const items = fs.readdirSync(dir);
        for (const item of items) {
            const itemPath = path.join(dir, item);
            const stat = fs.statSync(itemPath);
            
            if (stat.isDirectory()) {
                findLogFiles(itemPath);
            } else if (item.endsWith('.log')) {
                logFiles.push(itemPath);
            }
        }
    }
    
    findLogFiles(workspace.augmentPath);
    
    // 分析日志文件
    for (const logFile of logFiles.slice(0, 5)) { // 限制分析文件数量
        try {
            const content = fs.readFileSync(logFile, 'utf8');
            
            // 查找对话相关的JSON数据
            const jsonMatches = content.match(/\{[^{}]*"conversationId"[^{}]*\}/g) || [];
            
            for (const match of jsonMatches) {
                try {
                    const parsed = JSON.parse(match);
                    if (parsed.conversationId) {
                        data.push({
                            source: 'strings',
                            conversationId: parsed.conversationId,
                            exchangeId: parsed.exchangeId || null,
                            timestamp: parsed.timestamp || null,
                            request_message: parsed.request_message || '',
                            response_text: parsed.response_text || '',
                            rawData: parsed
                        });
                    }
                } catch (e) {
                    // 忽略JSON解析错误
                }
            }
        } catch (error) {
            console.log(`   字符串分析错误: ${error.message}`);
        }
    }
    
    return data;
}

// 分析特定对话的时间戳
async function analyzeConversationTimestamps(conversationId = 'b78bd351-4c2b-4891-9ba8-f1d7a869d74b') {
    console.log(`\n🎯 分析特定对话的时间戳: ${conversationId.substring(0, 8)}...`);
    
    const workspaces = await findWorkspaces();
    const conversationData = [];
    
    for (const workspace of workspaces) {
        try {
            // 从LevelDB获取数据
            const leveldbData = await analyzeLevelDBData(workspace);
            const convLevelDB = leveldbData.filter(item => item.conversationId === conversationId);
            
            // 从字符串获取数据
            const stringsData = await analyzeStringsData(workspace);
            const convStrings = stringsData.filter(item => item.conversationId === conversationId);
            
            conversationData.push(...convLevelDB, ...convStrings);
        } catch (error) {
            // 忽略错误
        }
    }
    
    console.log(`📊 找到 ${conversationData.length} 条相关记录`);
    
    // 分析时间戳分布
    const withTimestamp = conversationData.filter(item => item.timestamp);
    const withExchangeId = conversationData.filter(item => item.exchangeId);
    
    console.log(`   有timestamp: ${withTimestamp.length}`);
    console.log(`   有exchangeId: ${withExchangeId.length}`);
    
    // 显示时间戳样本
    console.log('\n🔍 时间戳样本:');
    const samples = conversationData.slice(0, 10);
    samples.forEach((item, index) => {
        console.log(`${index + 1}. ${item.source}: timestamp=${item.timestamp}, exchangeId=${item.exchangeId}`);
    });
    
    // 尝试排序
    console.log('\n📈 排序测试:');
    const sorted = conversationData.sort((a, b) => {
        const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
        const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
        
        if (timeA && timeB) return timeA - timeB;
        if (timeA) return -1;
        if (timeB) return 1;
        
        // 都没有timestamp，使用exchangeId
        const idA = a.exchangeId || '';
        const idB = b.exchangeId || '';
        return idA.localeCompare(idB);
    });
    
    console.log('排序后前5条:');
    sorted.slice(0, 5).forEach((item, index) => {
        console.log(`${index + 1}. ${item.timestamp || 'no-timestamp'} | ${item.exchangeId || 'no-exchangeId'}`);
    });
    
    console.log('排序后后5条:');
    sorted.slice(-5).forEach((item, index) => {
        console.log(`${sorted.length - 4 + index}. ${item.timestamp || 'no-timestamp'} | ${item.exchangeId || 'no-exchangeId'}`);
    });
    
    return conversationData;
}

// 主函数
async function main() {
    console.log('🔧 原始数据时间戳分析工具');
    console.log('================================\n');
    
    try {
        // 1. 分析整体数据结构
        const analysis = await analyzeRawData();
        
        // 2. 分析特定对话
        await analyzeConversationTimestamps();
        
        // 3. 提供诊断结论
        console.log('\n💡 诊断结论:');
        console.log('================================');
        
        const totalRecords = analysis.withTimestamp + analysis.withExchangeId + analysis.withBoth + analysis.withNeither;
        const timestampCoverage = (analysis.withTimestamp + analysis.withBoth) / totalRecords * 100;
        
        if (timestampCoverage < 50) {
            console.log('❌ 时间戳覆盖率低 - 大部分记录缺少timestamp');
            console.log('   建议: 改进数据提取逻辑，寻找其他时间信息源');
        } else {
            console.log('✅ 时间戳覆盖率良好');
            console.log('   问题可能在排序算法或数据合并逻辑');
        }
        
        if (analysis.withExchangeId > analysis.withTimestamp) {
            console.log('⚠️  exchangeId数量多于timestamp');
            console.log('   建议: 改进exchangeId解析逻辑');
        }
        
    } catch (error) {
        console.error('❌ 分析失败:', error.message);
    }
}

// 运行分析
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { analyzeRawData, analyzeConversationTimestamps };
