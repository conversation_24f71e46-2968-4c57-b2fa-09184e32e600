var n=Object.defineProperty;var i=(o,e,s)=>e in o?n(o,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):o[e]=s;var r=(o,e,s)=>i(o,typeof e!="symbol"?e+"":e,s);import{W as a}from"./host-BNehKqab.js";import{A as h}from"./async-messaging-gS_K9w3p.js";class g extends h{constructor(s){super(t=>{this._host.postMessage(t)});r(this,"_consumers",[]);this._host=s,this.onMessageFromExtension=this.onMessageFromExtension.bind(this)}dispose(){this._consumers=[]}postMessage(s){this._host.postMessage(s)}registerConsumer(s){this._consumers.push(s)}onMessageFromExtension(s){s.data.type!==a.asyncWrapper&&this._consumers.forEach(t=>{t.handleMessageFromExtension(s)})}}export{g as M};
