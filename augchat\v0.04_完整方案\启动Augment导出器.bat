@echo off
chcp 65001 >nul
title 🚀 Augment聊天记录完整导出器 v3.0

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🚀 Augment聊天记录完整导出器 v3.0                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎯 这是一个功能强大的Augment聊天记录导出工具
echo    能够自动发现所有VSCode工作区并生成易于阅读的Markdown文件
echo.
echo ✨ 核心特性:
echo    ✅ 自动发现所有VSCode工作区 (无需手动配置)
echo    ✅ 智能提取聊天记录 (双重技术保证完整性)
echo    ✅ 生成有意义的文件名 (基于对话内容)
echo    ✅ 支持增量更新 (只处理变化的对话)
echo    ✅ 统一输出管理 (集中存储，便于浏览)
echo.
echo 📊 当前状态检查:

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ Node.js 已安装
) else (
    echo    ❌ Node.js 未安装 - 请先安装Node.js
    echo    💡 下载地址: https://nodejs.org
    pause
    exit /b 1
)

REM 检查主脚本
if exist "augment-chat-exporter.js" (
    echo    ✅ 主脚本文件存在
) else (
    echo    ❌ 主脚本文件不存在
    pause
    exit /b 1
)

REM 检查level库
if exist "node_modules\level" (
    echo    ✅ level库已安装
) else (
    echo    📦 正在安装level库...
    npm install level >nul 2>&1
    if %errorlevel% equ 0 (
        echo    ✅ level库安装成功
    ) else (
        echo    ❌ level库安装失败
        pause
        exit /b 1
    )
)

REM 检查VSCode工作区
set workspace_found=0
if exist "%USERPROFILE%\AppData\Roaming\Code\User\workspaceStorage" (
    echo    ✅ 发现VSCode工作区目录
    set workspace_found=1
)

if %workspace_found% equ 0 (
    echo    ⚠️  未发现VSCode工作区目录
    echo    💡 请确保已安装VSCode并使用过Augment插件
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo.

if %workspace_found% equ 1 (
    echo 🎉 环境检查通过！准备开始导出...
    echo.
    echo 💡 建议: 为获得最佳效果，请先关闭所有VSCode窗口
    echo.
    set /p confirm="是否开始导出? (Y/N): "
    
    if /i "%confirm%"=="Y" (
        echo.
        echo 🚀 正在运行Augment聊天记录导出器...
        echo ═══════════════════════════════════════════════════════════════
        
        node augment-chat-exporter.js
        
        if %errorlevel% equ 0 (
            echo.
            echo ═══════════════════════════════════════════════════════════════
            echo 🎉 导出成功完成！
            echo.

            REM 查找最新的导出目录
            for /f "delims=" %%i in ('dir /b /ad conversations_export_* 2^>nul ^| sort /r') do (
                set "latest_dir=%%i"
                goto found_latest
            )

            :found_latest
            if defined latest_dir (
                echo 📂 输出目录: !latest_dir!
                echo 📋 索引文件: !latest_dir!\README.md
            ) else (
                echo ❌ 未找到导出目录
                goto END_EXPORT
            )
            echo.
            echo 🔍 查看结果:
            echo    [1] 打开输出目录
            echo    [2] 查看索引文件  
            echo    [3] 退出
            echo.
            set /p view_choice="请选择 (1-3): "
            
            if "!view_choice!"=="1" (
                echo 📂 正在打开输出目录...
                if defined latest_dir (
                    start "" "!latest_dir!"
                )
            ) else if "!view_choice!"=="2" (
                echo 📋 正在打开索引文件...
                if defined latest_dir (
                    start "" "!latest_dir!\README.md"
                )
            )
            
            echo.
            echo 🎯 导出完成总结:
            if defined latest_dir (
                if exist "!latest_dir!\README.md" (
                    echo    ✅ 成功生成索引文件
                )

                for /f %%i in ('dir /b "!latest_dir!\*.md" 2^>nul ^| find /c /v ""') do (
                    echo    ✅ 共生成 %%i 个文件
                )
            )
            
            echo    ✅ 所有对话已转换为Markdown格式
            echo    ✅ 支持在任何Markdown编辑器中查看
            echo.
            echo 💡 每次运行都会创建新的时间戳目录，保留历史导出记录

        ) else (
            echo.
            echo ❌ 导出过程中出现错误
            echo 💡 请检查错误信息并重试
            echo.
            echo 🛠️  常见解决方案:
            echo    - 确保关闭了所有VSCode窗口
            echo    - 检查磁盘空间是否充足
            echo    - 确保有足够的权限访问VSCode目录
        )

        :END_EXPORT
    ) else (
        echo.
        echo 👋 已取消导出操作
    )
) else (
    echo ❌ 环境检查未通过，无法继续
    echo.
    echo 🛠️  解决方案:
    echo    1. 安装VSCode: https://code.visualstudio.com
    echo    2. 安装Augment插件
    echo    3. 使用Augment进行一些对话
    echo    4. 重新运行此工具
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 感谢使用Augment聊天记录完整导出器！
echo 工具版本: v3.0 | 更新日期: 2025-08-21
echo ═══════════════════════════════════════════════════════════════
echo.
pause
