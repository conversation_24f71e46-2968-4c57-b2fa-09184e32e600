import{f as n,a as m,o as r,b as c}from"./legacy-YP6Kq8lu.js";import{h as i}from"./IconButtonAugment-CbpcmeFk.js";import{l as f}from"./SpinnerAugment-Dpcl1cXc.js";var p=n("<svg><!></svg>");function l(s,a){const e=f(a,["children","$$slots","$$events","$$legacy"]);var o=p();m(o,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...e}));var t=r(o);i(t,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M432 256a48 48 0 1 1-96 0 48 48 0 1 1 96 0m-160 0a48 48 0 1 1-96 0 48 48 0 1 1 96 0M64 304a48 48 0 1 1 0-96 48 48 0 1 1 0 96"/>',!0),c(s,o)}export{l as E};
