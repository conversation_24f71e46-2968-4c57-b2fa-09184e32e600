#!/usr/bin/env node

/**
 * 提取活跃对话记录
 * 专门处理被锁定的LevelDB数据库
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { execSync } = require('child_process');

// 尝试导入level库
let Level;
try {
    const levelModule = require('level');
    Level = levelModule.Level || levelModule.default || levelModule;
} catch (error) {
    console.error('❌ 错误: 需要安装level库');
    console.error('请运行: npm install level');
    process.exit(1);
}

// 配置
const CONFIG = {
    userDataPath: path.join(os.homedir(), 'AppData', 'Roaming', 'Code', 'User'),
    outputDir: path.join(process.cwd(), 'ActiveConversations'),
    tempDir: path.join(process.cwd(), 'temp_db_copies'),
    targetWorkspace: 'be0818f388a073cd7f2825038ea6bf6f' // 我们发现的活跃工作区
};

// 工具函数
const utils = {
    ensureDir(dirPath) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            console.log(`✅ 创建目录: ${dirPath}`);
        }
    },

    formatDate(date = new Date()) {
        return date.toISOString().replace(/[:.]/g, '-').slice(0, 19);
    },

    safeJsonParse(str) {
        try {
            return JSON.parse(str);
        } catch {
            return str;
        }
    },

    // 复制数据库文件到临时位置
    copyDatabaseFiles(sourcePath, destPath) {
        try {
            console.log(`📁 复制数据库文件从 ${sourcePath} 到 ${destPath}`);
            
            if (!fs.existsSync(sourcePath)) {
                throw new Error(`源路径不存在: ${sourcePath}`);
            }

            utils.ensureDir(destPath);

            // 复制所有数据库文件，但跳过LOCK文件
            const files = fs.readdirSync(sourcePath);
            let copiedFiles = 0;

            for (const file of files) {
                if (file === 'LOCK') {
                    console.log(`⏭️  跳过锁定文件: ${file}`);
                    continue;
                }

                const srcFile = path.join(sourcePath, file);
                const destFile = path.join(destPath, file);

                try {
                    fs.copyFileSync(srcFile, destFile);
                    copiedFiles++;
                    console.log(`✅ 复制文件: ${file}`);
                } catch (error) {
                    console.warn(`⚠️  复制文件失败 ${file}: ${error.message}`);
                }
            }

            console.log(`📊 总共复制了 ${copiedFiles} 个文件`);
            return copiedFiles > 0;

        } catch (error) {
            console.error(`❌ 复制数据库文件失败: ${error.message}`);
            return false;
        }
    }
};

// 使用strings命令提取文本内容
function extractWithStrings(dbPath) {
    console.log(`🔍 使用strings命令提取文本内容...`);
    
    try {
        const files = fs.readdirSync(dbPath);
        const conversations = [];
        
        for (const file of files) {
            if (file.endsWith('.log') || file.endsWith('.ldb')) {
                const filePath = path.join(dbPath, file);
                console.log(`📄 处理文件: ${file}`);
                
                try {
                    // 使用strings命令提取可读文本
                    const command = process.platform === 'win32' 
                        ? `wsl strings "${filePath.replace(/\\/g, '/').replace('C:', '/mnt/c')}"` 
                        : `strings "${filePath}"`;
                    
                    const output = execSync(command, { encoding: 'utf8', maxBuffer: 50 * 1024 * 1024 });
                    const lines = output.split('\n');
                    
                    // 查找包含对话数据的行
                    for (let i = 0; i < lines.length; i++) {
                        const line = lines[i];
                        
                        // 查找包含conversationId的行
                        if (line.includes('conversationId') || line.includes('conversation')) {
                            try {
                                // 尝试解析JSON
                                const jsonMatch = line.match(/\{.*\}/);
                                if (jsonMatch) {
                                    const parsed = JSON.parse(jsonMatch[0]);
                                    if (parsed.conversationId || parsed.uuid) {
                                        conversations.push({
                                            source: file,
                                            lineNumber: i + 1,
                                            data: parsed,
                                            rawLine: line.substring(0, 500) // 限制长度
                                        });
                                        console.log(`🎯 发现对话数据: ${parsed.conversationId || parsed.uuid}`);
                                    }
                                }
                            } catch (parseError) {
                                // 如果不是完整的JSON，保存原始行
                                if (line.length > 50 && (line.includes('message') || line.includes('content'))) {
                                    conversations.push({
                                        source: file,
                                        lineNumber: i + 1,
                                        data: line.substring(0, 1000),
                                        type: 'raw_text'
                                    });
                                }
                            }
                        }
                    }
                    
                } catch (error) {
                    console.warn(`⚠️  处理文件 ${file} 时出错: ${error.message}`);
                }
            }
        }
        
        return conversations;
        
    } catch (error) {
        console.error(`❌ strings提取失败: ${error.message}`);
        return [];
    }
}

// 尝试读取LevelDB数据库
async function readLevelDB(dbPath) {
    console.log(`💾 尝试读取LevelDB数据库: ${dbPath}`);
    
    let db;
    try {
        db = new Level(dbPath, { 
            createIfMissing: false,
            errorIfExists: false,
            readOnly: true
        });
        
        const conversations = [];
        let totalEntries = 0;
        
        for await (const [key, value] of db.iterator()) {
            totalEntries++;
            
            try {
                const keyStr = key.toString('utf8');
                const valueStr = value.toString('utf8');
                
                // 检查是否包含对话相关数据
                if (keyStr.includes('conversation') || valueStr.includes('conversationId') || 
                    valueStr.includes('message') || valueStr.includes('content')) {
                    
                    const parsedValue = utils.safeJsonParse(valueStr);
                    
                    conversations.push({
                        key: keyStr,
                        value: parsedValue,
                        valueRaw: valueStr.substring(0, 1000),
                        metadata: {
                            keyLength: key.length,
                            valueLength: value.length,
                            isJson: typeof parsedValue === 'object'
                        }
                    });
                    
                    console.log(`🎯 发现LevelDB对话数据: ${keyStr}`);
                }
            } catch (error) {
                console.warn(`⚠️  处理LevelDB条目时出错: ${error.message}`);
            }
        }
        
        await db.close();
        
        console.log(`✅ LevelDB读取完成: ${totalEntries} 总条目, ${conversations.length} 对话条目`);
        return conversations;
        
    } catch (error) {
        console.error(`❌ LevelDB读取失败: ${error.message}`);
        if (db) {
            try {
                await db.close();
            } catch (closeError) {
                console.warn(`关闭数据库时出错: ${closeError.message}`);
            }
        }
        return [];
    }
}

// 主函数
async function main() {
    console.log('🚀 开始提取活跃对话记录...');
    
    utils.ensureDir(CONFIG.outputDir);
    utils.ensureDir(CONFIG.tempDir);
    
    // 构建源数据库路径
    const sourceDbPath = path.join(
        CONFIG.userDataPath,
        'workspaceStorage',
        CONFIG.targetWorkspace,
        'Augment.vscode-augment',
        'augment-kv-store'
    );
    
    const tempDbPath = path.join(CONFIG.tempDir, 'copied_db');
    
    console.log(`📂 源数据库路径: ${sourceDbPath}`);
    console.log(`📂 临时数据库路径: ${tempDbPath}`);
    
    if (!fs.existsSync(sourceDbPath)) {
        console.error(`❌ 源数据库路径不存在: ${sourceDbPath}`);
        return;
    }
    
    // 方法1: 使用strings命令直接提取
    console.log('\n🔍 方法1: 使用strings命令提取...');
    const stringsResults = extractWithStrings(sourceDbPath);
    
    // 方法2: 复制数据库文件后读取
    console.log('\n📁 方法2: 复制数据库文件后读取...');
    let leveldbResults = [];
    
    if (utils.copyDatabaseFiles(sourceDbPath, tempDbPath)) {
        leveldbResults = await readLevelDB(tempDbPath);
    }
    
    // 合并结果
    const allResults = {
        extractionDate: new Date().toISOString(),
        sourceWorkspace: CONFIG.targetWorkspace,
        sourceDbPath: sourceDbPath,
        methods: {
            strings: {
                count: stringsResults.length,
                results: stringsResults
            },
            leveldb: {
                count: leveldbResults.length,
                results: leveldbResults
            }
        },
        summary: {
            totalConversations: stringsResults.length + leveldbResults.length,
            stringsMethod: stringsResults.length,
            leveldbMethod: leveldbResults.length
        }
    };
    
    // 保存结果
    const outputPath = path.join(CONFIG.outputDir, `active_conversations_${utils.formatDate()}.json`);
    fs.writeFileSync(outputPath, JSON.stringify(allResults, null, 2), 'utf8');
    
    console.log(`\n🎉 提取完成！`);
    console.log(`📄 结果已保存到: ${outputPath}`);
    console.log(`\n📊 提取摘要:`);
    console.log(`   - Strings方法: ${stringsResults.length} 条记录`);
    console.log(`   - LevelDB方法: ${leveldbResults.length} 条记录`);
    console.log(`   - 总计: ${allResults.summary.totalConversations} 条记录`);
    
    // 显示一些示例数据
    if (stringsResults.length > 0) {
        console.log(`\n🎯 Strings方法发现的对话示例:`);
        stringsResults.slice(0, 3).forEach((conv, index) => {
            console.log(`${index + 1}. 来源: ${conv.source}`);
            if (conv.data && typeof conv.data === 'object') {
                console.log(`   对话ID: ${conv.data.conversationId || conv.data.uuid || '未知'}`);
                console.log(`   时间戳: ${conv.data.timestamp || '未知'}`);
            }
            console.log(`   数据预览: ${JSON.stringify(conv.data).substring(0, 100)}...`);
            console.log('');
        });
    }
    
    if (leveldbResults.length > 0) {
        console.log(`\n💾 LevelDB方法发现的对话示例:`);
        leveldbResults.slice(0, 3).forEach((conv, index) => {
            console.log(`${index + 1}. 键: ${conv.key}`);
            console.log(`   值预览: ${conv.valueRaw.substring(0, 100)}...`);
            console.log('');
        });
    }
    
    // 清理临时文件
    try {
        if (fs.existsSync(tempDbPath)) {
            fs.rmSync(tempDbPath, { recursive: true, force: true });
            console.log(`🧹 已清理临时文件: ${tempDbPath}`);
        }
    } catch (error) {
        console.warn(`⚠️  清理临时文件失败: ${error.message}`);
    }
}

// 运行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 程序执行失败:', error.message);
        process.exit(1);
    });
}

module.exports = { main, extractWithStrings, readLevelDB };
