# RAGFlow项目编程规范大全

## 📋 概述

本文档基于RAGFlow项目代码库的深度分析，总结了项目中已遵循的编程规范和最佳实践，同时识别了需要改进的地方。文档涵盖Python后端代码（102个文件，29,698行）和TypeScript/React前端代码（642个文件，约25,000-30,000行）。

### 🎯 规范分级系统

本文档采用三级规范分级系统：

- 🔴 **MUST (必须)**：强制性规则，违反将导致代码审查不通过
- 🟡 **SHOULD (应该)**：强烈建议遵循，有特殊情况可例外但需在PR中说明理由
- 🟢 **MAY (可以)**：可选建议，团队可根据具体情况选择采用

### 📊 规范覆盖范围

- ✅ **代码风格规范**：命名、格式化、注释
- ✅ **架构设计规范**：模块划分、依赖管理、接口设计
- ✅ **质量保证规范**：测试、代码审查、CI/CD
- ✅ **安全规范**：输入验证、权限控制、数据保护
- ✅ **性能规范**：优化策略、监控指标、资源管理

## 🐍 Python编程规范

### 1. 命名约定

#### 🔴 MUST 强制性规则
```python
# 文件命名：使用下划线分隔的小写字母
chat_model.py
task_executor.py
redis_conn.py

# 类名：使用PascalCase
class Base(ABC):
class DocumentService:
class KnowledgebaseService:

# 函数名：使用snake_case
def chat_streamly(self, system, history, gen_conf):
def server_error_response(e):
def construct_json_result(code, message, data=None):

# 常量：使用大写字母和下划线
LENGTH_NOTIFICATION_CN = "······\n由于大模型的上下文窗口大小限制，回答已经被大模型截断。"
MAXIMUM_OF_UPLOADING_FILES = 256
BATCH_SIZE = 64
```

#### 🚫 MUST NOT 禁止事项
```python
# 避免使用缩写和不清晰的命名
# 不好的例子
def __clean(self, line):  # 应该是 clean_line 或 sanitize_line
pn = 0  # 应该是 page_number
tol = self.total_token_count(resp)  # 应该是 total_tokens

# 改进建议
def clean_line(self, line):
page_number = 0
total_tokens = self.total_token_count(resp)
```

### 2. 代码结构和组织

#### ✅ 良好实践
```python
# 标准的文件头部版权声明
#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  ...

# 清晰的导入分组
import re
import os
import json
from abc import ABC
from openai import OpenAI
from rag.nlp import is_chinese, is_english
from rag.utils import num_tokens_from_string

# 工厂模式的使用
FACTORY = {
    "general": naive,
    ParserType.NAIVE.value: naive,
    ParserType.PAPER.value: paper,
    ParserType.BOOK.value: book,
    # ...
}
```

#### ⚠️ 需要改进
```python
# 大文件问题：chat_model.py (1,587行) 应该拆分
# 建议拆分为：
# - base_chat_model.py (基础类)
# - openai_chat_model.py (OpenAI相关)
# - azure_chat_model.py (Azure相关)
# - anthropic_chat_model.py (Anthropic相关)
# - local_chat_model.py (本地模型)

# 过长的函数应该拆分
def very_long_function():  # 超过50行的函数
    # 应该拆分为多个小函数
    pass
```

### 3. 错误处理规范

#### ✅ 良好实践
```python
# 统一的错误处理模式
def server_error_response(e):
    logging.exception(e)
    try:
        if e.code == 401:
            return get_json_result(code=401, message=repr(e))
    except BaseException:
        pass
    return get_json_result(code=settings.RetCode.EXCEPTION_ERROR, message=repr(e))

# API错误处理
try:
    response = self.client.chat.completions.create(
        model=self.model_name,
        messages=history,
        **gen_conf)
    return ans, self.total_token_count(response)
except openai.APIError as e:
    return "**ERROR**: " + str(e), 0
```

#### ⚠️ 需要改进
```python
# 避免裸露的except子句
try:
    # some code
except:  # 不好：捕获所有异常
    pass

# 改进：具体化异常类型
try:
    # some code
except (ValueError, TypeError) as e:
    logging.error(f"Specific error occurred: {e}")
    raise
```

### 4. 日志记录规范

#### ✅ 良好实践
```python
# 统一的日志初始化
def initRootLogger(logfile_basename: str, log_format: str = "%(asctime)-15s %(levelname)-8s %(process)d %(message)s"):
    logger = logging.getLogger()
    log_path = os.path.abspath(os.path.join(get_project_base_directory(), "logs", f"{logfile_basename}.log"))
    
    formatter = logging.Formatter(log_format)
    handler1 = RotatingFileHandler(log_path, maxBytes=10*1024*1024, backupCount=5)
    handler1.setFormatter(formatter)
    logger.addHandler(handler1)

# 异常日志记录
logging.exception(e)  # 自动包含堆栈跟踪
```

### 5. 类型注解规范

#### ✅ 良好实践
```python
# 使用Pydantic进行数据验证
class Chunk(BaseModel):
    id: str = ""
    content: str = ""
    document_id: str = ""
    important_keywords: list = Field(default_factory=list)
    positions: list[list[int]] = Field(default_factory=list)
    
    @validator('positions')
    def validate_positions(cls, value):
        for sublist in value:
            if len(sublist) != 5:
                raise ValueError("Each sublist in positions must have a length of 5")
        return value

# 函数类型注解
ErrorHandlerFn = Callable[[BaseException | None, str | None, dict | None], None]
```

#### ⚠️ 需要改进
```python
# 缺少类型注解的函数
def chat(self, system, history, gen_conf):  # 应该添加类型注解
    pass

# 改进建议
def chat(self, system: str, history: List[Dict[str, str]], gen_conf: Dict[str, Any]) -> Tuple[str, int]:
    pass
```

## 🌐 API设计规范

### 1. RESTful API设计原则

#### 🔴 MUST 强制性规则

```python
# URL命名：必须使用复数名词，小写字母，连字符分隔
# ✅ 正确示例
GET /api/v1/datasets
GET /api/v1/datasets/{dataset_id}/documents
POST /api/v1/knowledge-bases

# ❌ 错误示例
GET /api/v1/dataset          # 应该用复数
GET /api/v1/Dataset          # 不应该用大写
GET /api/v1/knowledge_bases  # 应该用连字符而非下划线

# HTTP方法：必须按语义正确使用
GET    /api/v1/datasets           # 获取列表
GET    /api/v1/datasets/{id}      # 获取单个资源
POST   /api/v1/datasets           # 创建新资源
PUT    /api/v1/datasets/{id}      # 完整更新资源
PATCH  /api/v1/datasets/{id}      # 部分更新资源
DELETE /api/v1/datasets/{id}      # 删除资源

# 状态码：必须使用标准HTTP状态码
200 OK                    # 成功获取/更新
201 Created              # 成功创建
204 No Content           # 成功删除
400 Bad Request          # 客户端错误
401 Unauthorized         # 未认证
403 Forbidden           # 无权限
404 Not Found           # 资源不存在
409 Conflict            # 资源冲突
422 Unprocessable Entity # 验证失败
500 Internal Server Error # 服务器错误
```

#### 🟡 SHOULD 强烈建议

```python
# API版本控制：应该在URL中包含版本号
@app.route('/api/v1/datasets', methods=['GET'])
def get_datasets():
    pass

# 响应格式：应该使用统一的响应结构
class APIResponse(BaseModel):
    code: int = 200
    message: str = "Success"
    data: Optional[Any] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))

def success_response(data: Any = None, message: str = "Success") -> dict:
    """标准成功响应"""
    return APIResponse(data=data, message=message).dict()

def error_response(code: int, message: str, data: Any = None) -> dict:
    """标准错误响应"""
    return APIResponse(code=code, message=message, data=data).dict()

# 分页：应该使用标准分页参数
@app.route('/api/v1/datasets')
def get_datasets():
    page = request.args.get('page', 1, type=int)
    page_size = request.args.get('page_size', 20, type=int)

    # 限制页面大小
    page_size = min(page_size, 100)

    # 返回分页信息
    return {
        'data': datasets,
        'pagination': {
            'page': page,
            'page_size': page_size,
            'total': total_count,
            'total_pages': math.ceil(total_count / page_size)
        }
    }
```

### 2. API文档规范

#### 🔴 MUST 强制性规则

```python
# 所有API端点必须包含完整的文档字符串
@app.route('/api/v1/datasets/<dataset_id>/documents', methods=['POST'])
@token_required
def upload_documents(dataset_id: str, tenant_id: str):
    """
    Upload documents to a dataset.

    Args:
        dataset_id (str): The unique identifier of the dataset
        tenant_id (str): The tenant identifier (from token)

    Request Body:
        - file (file): Document file to upload (required)
        - parser_config (json): Parser configuration (optional)

    Returns:
        201: Document uploaded successfully
            {
                "code": 201,
                "message": "Document uploaded successfully",
                "data": {
                    "document_id": "doc_123",
                    "filename": "example.pdf",
                    "status": "processing"
                }
            }
        400: Invalid request
        401: Unauthorized
        404: Dataset not found
        413: File too large

    Raises:
        ValidationError: When file validation fails
        StorageError: When file storage fails
    """
    pass

# API参数验证：必须使用Pydantic模型
class DocumentUploadRequest(BaseModel):
    parser_config: Optional[Dict[str, Any]] = None
    chunk_method: str = Field(default="naive", regex="^(naive|manual|qa)$")

    class Config:
        schema_extra = {
            "example": {
                "parser_config": {"layout_recognize": True},
                "chunk_method": "naive"
            }
        }

@app.route('/api/v1/datasets/<dataset_id>/documents', methods=['POST'])
def upload_documents(dataset_id: str):
    # 验证请求数据
    try:
        request_data = DocumentUploadRequest(**request.json)
    except ValidationError as e:
        return error_response(400, "Validation failed", e.errors())
```

### 3. 错误处理规范

#### 🔴 MUST 强制性规则

```python
# 统一的错误响应格式
class APIError(Exception):
    """API错误基类"""
    def __init__(self, code: int, message: str, details: Any = None):
        self.code = code
        self.message = message
        self.details = details
        super().__init__(self.message)

class ValidationError(APIError):
    """验证错误"""
    def __init__(self, message: str, details: Any = None):
        super().__init__(400, message, details)

class NotFoundError(APIError):
    """资源不存在错误"""
    def __init__(self, resource: str, resource_id: str):
        super().__init__(404, f"{resource} with id '{resource_id}' not found")

class PermissionError(APIError):
    """权限错误"""
    def __init__(self, message: str = "Permission denied"):
        super().__init__(403, message)

# 全局错误处理器
@app.errorhandler(APIError)
def handle_api_error(error: APIError):
    """处理API错误"""
    logger.error(f"API Error: {error.code} - {error.message}",
                extra={'details': error.details})

    return jsonify(error_response(
        code=error.code,
        message=error.message,
        data=error.details
    )), error.code

@app.errorhandler(ValidationError)
def handle_validation_error(error: ValidationError):
    """处理验证错误"""
    return jsonify(error_response(
        code=400,
        message="Validation failed",
        data=error.errors()
    )), 400
```

## 🗄️ 数据库操作规范

### 1. ORM使用规范

#### 🔴 MUST 强制性规则

```python
# 查询优化：必须避免N+1查询问题
# ❌ 错误示例：N+1查询
documents = Document.select()
for doc in documents:
    print(doc.knowledge_base.name)  # 每次循环都会查询数据库

# ✅ 正确示例：使用join预加载
documents = Document.select().join(KnowledgeBase)
for doc in documents:
    print(doc.knowledge_base.name)  # 只查询一次数据库

# 事务管理：必须正确使用事务
from peewee import database

def create_document_with_chunks(doc_data: dict, chunks_data: List[dict]):
    """创建文档和分块，必须在事务中执行"""
    with database.atomic():  # 确保原子性
        try:
            # 创建文档
            document = Document.create(**doc_data)

            # 批量创建分块
            chunks = []
            for chunk_data in chunks_data:
                chunk_data['document_id'] = document.id
                chunks.append(Chunk(**chunk_data))

            Chunk.bulk_create(chunks, batch_size=100)

            return document
        except Exception as e:
            # 事务会自动回滚
            logger.error(f"Failed to create document: {e}")
            raise

# 批量操作：必须使用批量方法提高性能
# ❌ 错误示例：逐个插入
for chunk_data in chunks_data:
    Chunk.create(**chunk_data)  # 每次都是一个数据库操作

# ✅ 正确示例：批量插入
chunks = [Chunk(**data) for data in chunks_data]
Chunk.bulk_create(chunks, batch_size=100)  # 批量操作，提高性能
```

#### 🟡 SHOULD 强烈建议

```python
# 查询分页：应该限制查询结果数量
def get_documents(page: int = 1, page_size: int = 20) -> Tuple[List[Document], int]:
    """获取分页文档列表"""
    # 限制最大页面大小
    page_size = min(page_size, 100)

    # 计算偏移量
    offset = (page - 1) * page_size

    # 分页查询
    documents = (Document
                .select()
                .order_by(Document.create_time.desc())
                .offset(offset)
                .limit(page_size))

    # 获取总数（考虑性能，可以缓存）
    total = Document.select().count()

    return list(documents), total

# 索引优化：应该为常用查询字段添加索引
class Document(BaseModel):
    id = CharField(primary_key=True)
    knowledge_base_id = CharField(index=True)  # 外键索引
    name = CharField(index=True)               # 查询字段索引
    status = CharField(index=True)             # 状态字段索引
    create_time = DateTimeField(index=True)    # 排序字段索引

    class Meta:
        # 复合索引
        indexes = (
            (('knowledge_base_id', 'status'), False),
            (('knowledge_base_id', 'create_time'), False),
        )
```

### 2. 数据库连接管理

#### 🔴 MUST 强制性规则

```python
# 连接池配置：必须使用连接池
from playhouse.pool import PooledPostgresqlDatabase

database = PooledPostgresqlDatabase(
    'ragflow',
    max_connections=20,      # 最大连接数
    stale_timeout=300,       # 连接超时时间（秒）
    timeout=30,              # 获取连接超时时间
    user='ragflow_user',
    password='password',
    host='localhost',
    port=5432
)

# 连接生命周期管理：必须正确关闭连接
@app.before_request
def before_request():
    """请求前连接数据库"""
    if database.is_closed():
        database.connect()

@app.after_request
def after_request(response):
    """请求后关闭数据库连接"""
    if not database.is_closed():
        database.close()
    return response

# 数据库迁移：必须使用版本化迁移
# migrations/001_initial.py
def migrate_up():
    """创建初始表结构"""
    database.create_tables([User, KnowledgeBase, Document, Chunk])

def migrate_down():
    """回滚迁移"""
    database.drop_tables([Chunk, Document, KnowledgeBase, User])

# 迁移版本管理
class Migration(BaseModel):
    version = CharField(primary_key=True)
    applied_at = DateTimeField(default=datetime.now)
```

## ⚛️ TypeScript/React编程规范

### 1. 组件命名和文件组织

#### ✅ 良好实践
```typescript
// 文件命名：kebab-case
file-upload-modal/
message-item/
pdf-previewer/

// 组件命名：PascalCase
export const ChatDrawer: React.FC = () => {};
export const FormSheet: React.FC = () => {};
export const RagNode: React.FC = () => {};

// Hook命名：use前缀 + camelCase
export const useSelectCanvasData = () => {};
export const useFetchFlow = () => {};
export const useHandleDrop = () => {};
```

#### ⚠️ 需要改进
```typescript
// 避免过深的目录嵌套
// 当前：web/src/pages/agent/canvas/node/categorize-node/
// 建议：web/src/pages/agent/nodes/CategorizeNode/

// 统一文件扩展名
// 混用 .ts 和 .tsx，建议：
// - 纯逻辑文件使用 .ts
// - 包含JSX的文件使用 .tsx
```

### 2. TypeScript类型定义

#### ✅ 良好实践
```typescript
// 接口定义清晰
export interface IFlow {
  avatar?: null | string;
  canvas_type: null;
  create_date: string;
  create_time: number;
  description: null;
  dsl: DSL;
  id: string;
  title: string;
  update_date: string;
  update_time: number;
  user_id: string;
}

// 泛型使用
declare global {
  type Nullable<T> = T | null;
}

// 严格的类型检查
@validator('positions')
def validate_positions(cls, value):
    for sublist in value:
        if len(sublist) != 5:
            raise ValueError("Each sublist in positions must have a length of 5")
    return value
```

### 3. React Hooks使用规范

#### ✅ 良好实践
```typescript
// 自定义Hook的良好实践
export const useFetchFlow = (): {
  data: IFlow;
  loading: boolean;
  refetch: () => void;
} => {
  const { id } = useParams();
  const { sharedId } = useGetSharedChatSearchParams();

  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery({
    queryKey: ['flowDetail'],
    initialData: {} as IFlow,
    refetchOnReconnect: false,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    gcTime: 0,
    queryFn: async () => {
      const { data } = await flowService.getCanvas({}, sharedId || id);
      return data?.data ?? {};
    },
  });

  return { data, loading, refetch };
};

// 回调引用优化
function useCallbackRef<T extends (...args: never[]) => unknown>(
  callback: T | undefined,
): T {
  const callbackRef = React.useRef(callback);

  React.useEffect(() => {
    callbackRef.current = callback;
  });

  return React.useMemo(
    () => ((...args) => callbackRef.current?.(...args)) as T,
    [],
  );
}
```

### 4. 状态管理规范

#### ✅ 良好实践
```typescript
// Zustand store的使用
const selector = (state: RFState) => ({
  nodes: state.nodes,
  edges: state.edges,
  onNodesChange: state.onNodesChange,
  onEdgesChange: state.onEdgesChange,
  onConnect: state.onConnect,
  setNodes: state.setNodes,
  onSelectionChange: state.onSelectionChange,
});

export const useSelectCanvasData = () => {
  return useGraphStore(selector);
};
```

### 5. 错误处理和请求管理

#### ✅ 良好实践
```typescript
// 统一的错误处理
const errorHandler = (error: {
  response: Response;
  message: string;
}): Response => {
  const { response } = error;
  if (error.message === FAILED_TO_FETCH) {
    notification.error({
      description: i18n.t('message.networkAnomalyDescription'),
      message: i18n.t('message.networkAnomaly'),
    });
  } else {
    if (response && response.status) {
      const errorText =
        RetcodeMessage[response.status as ResultCode] || response.statusText;
      const { status, url } = response;
      notification.error({
        message: `${i18n.t('message.requestError')} ${status}: ${url}`,
        description: errorText,
      });
    }
  }
  return response ?? { data: { code: 1999 } };
};

// 响应拦截器
request.interceptors.response.use(async (response: Response, options) => {
  if (response?.status === 413 || response?.status === 504) {
    message.error(RetcodeMessage[response?.status as ResultCode]);
  }

  const data: ResponseType = await response?.clone()?.json();
  if (data?.code === 100) {
    message.error(data?.message);
  } else if (data?.code === 401) {
    notification.error({
      message: data?.message,
      description: data?.message,
      duration: 3,
    });
    authorizationUtil.removeAll();
    redirectToLogin();
  }
  return response;
});
```

## � 并发编程规范

### 1. 异步编程规范

#### 🔴 MUST 强制性规则

```python
# 异步函数命名：必须使用async/await语法
# ✅ 正确示例
async def process_document_async(document_id: str) -> ProcessResult:
    """异步处理文档"""
    async with aiohttp.ClientSession() as session:
        result = await call_external_api(session, document_id)
        return result

# ❌ 错误示例：混用同步和异步
def process_document(document_id: str):  # 同步函数名
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(async_process(document_id))  # 不推荐

# 异步上下文管理：必须正确管理资源
async def batch_process_documents(document_ids: List[str]):
    """批量处理文档"""
    semaphore = asyncio.Semaphore(10)  # 限制并发数

    async def process_with_semaphore(doc_id: str):
        async with semaphore:  # 必须使用上下文管理器
            return await process_document_async(doc_id)

    tasks = [process_with_semaphore(doc_id) for doc_id in document_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 处理异常结果
    successful_results = []
    failed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            failed_results.append((document_ids[i], result))
        else:
            successful_results.append(result)

    return successful_results, failed_results

# 异步错误处理：必须正确处理异步异常
async def safe_async_operation(operation_func, *args, **kwargs):
    """安全的异步操作包装器"""
    try:
        return await operation_func(*args, **kwargs)
    except asyncio.TimeoutError:
        logger.error(f"Operation timeout: {operation_func.__name__}")
        raise
    except asyncio.CancelledError:
        logger.info(f"Operation cancelled: {operation_func.__name__}")
        raise
    except Exception as e:
        logger.exception(f"Async operation failed: {operation_func.__name__}")
        raise
```

#### 🟡 SHOULD 强烈建议

```python
# 异步任务队列：应该使用专业的任务队列
import asyncio
from typing import Callable, Any
import redis.asyncio as redis

class AsyncTaskQueue:
    """异步任务队列"""

    def __init__(self, redis_url: str, max_workers: int = 10):
        self.redis = redis.from_url(redis_url)
        self.semaphore = asyncio.Semaphore(max_workers)
        self.running = False

    async def enqueue(self, task_name: str, task_data: dict):
        """入队任务"""
        task = {
            'name': task_name,
            'data': task_data,
            'created_at': datetime.utcnow().isoformat(),
            'id': str(uuid.uuid4())
        }
        await self.redis.lpush('task_queue', json.dumps(task))

    async def worker(self, task_handlers: Dict[str, Callable]):
        """工作进程"""
        while self.running:
            try:
                # 阻塞式获取任务
                task_data = await self.redis.brpop('task_queue', timeout=1)
                if not task_data:
                    continue

                task = json.loads(task_data[1])
                handler = task_handlers.get(task['name'])

                if handler:
                    async with self.semaphore:
                        await handler(task['data'])
                else:
                    logger.warning(f"No handler for task: {task['name']}")

            except Exception as e:
                logger.exception(f"Worker error: {e}")
                await asyncio.sleep(1)  # 避免快速失败循环

# 异步缓存：应该使用异步缓存机制
class AsyncCache:
    """异步缓存"""

    def __init__(self, redis_client):
        self.redis = redis_client

    async def get_or_set(self, key: str, factory: Callable, ttl: int = 3600):
        """获取或设置缓存"""
        # 尝试从缓存获取
        cached = await self.redis.get(key)
        if cached:
            return json.loads(cached)

        # 缓存未命中，调用工厂函数
        value = await factory() if asyncio.iscoroutinefunction(factory) else factory()

        # 设置缓存
        await self.redis.setex(key, ttl, json.dumps(value, default=str))
        return value
```

### 2. 线程安全规范

#### 🔴 MUST 强制性规则

```python
# 共享状态保护：必须使用锁保护共享状态
import threading
from threading import Lock, RLock
from concurrent.futures import ThreadPoolExecutor

class ThreadSafeCounter:
    """线程安全计数器"""

    def __init__(self):
        self._value = 0
        self._lock = Lock()  # 必须使用锁

    def increment(self):
        with self._lock:  # 必须在锁保护下修改共享状态
            self._value += 1

    def get_value(self):
        with self._lock:  # 读取也需要锁保护
            return self._value

# 线程池使用：必须正确管理线程池生命周期
class DocumentProcessor:
    """文档处理器"""

    def __init__(self, max_workers: int = 4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.shutdown_event = threading.Event()

    def process_documents(self, documents: List[Document]):
        """并行处理文档"""
        futures = []

        for doc in documents:
            if self.shutdown_event.is_set():
                break
            future = self.executor.submit(self._process_single_document, doc)
            futures.append(future)

        # 等待所有任务完成
        results = []
        for future in futures:
            try:
                result = future.result(timeout=30)  # 设置超时
                results.append(result)
            except Exception as e:
                logger.exception(f"Document processing failed: {e}")
                results.append(None)

        return results

    def shutdown(self):
        """优雅关闭"""
        self.shutdown_event.set()
        self.executor.shutdown(wait=True)  # 等待所有任务完成

# 死锁避免：必须按固定顺序获取多个锁
class ResourceManager:
    """资源管理器"""

    def __init__(self):
        self.lock_a = Lock()
        self.lock_b = Lock()

    def operation_1(self):
        # 总是按照 lock_a -> lock_b 的顺序获取锁
        with self.lock_a:
            with self.lock_b:
                # 执行需要两个锁的操作
                pass

    def operation_2(self):
        # 必须保持相同的锁获取顺序
        with self.lock_a:
            with self.lock_b:
                # 执行另一个需要两个锁的操作
                pass
```

## 📝 版本控制规范

### 1. Git提交规范

#### 🔴 MUST 强制性规则

```bash
# 提交信息格式：必须遵循约定式提交规范
# 格式：<type>(<scope>): <description>
#
# <body>
#
# <footer>

# 类型（type）必须是以下之一：
feat:     # 新功能
fix:      # 修复bug
docs:     # 文档更新
style:    # 代码格式化（不影响功能）
refactor: # 重构（既不是新功能也不是修复bug）
perf:     # 性能优化
test:     # 添加或修改测试
chore:    # 构建过程或辅助工具的变动
ci:       # CI/CD相关变更
revert:   # 回滚之前的提交

# ✅ 正确示例
feat(api): add document upload endpoint

Add new REST API endpoint for uploading documents to datasets.
Supports multiple file formats including PDF, DOCX, and TXT.

Closes #123

fix(chat): resolve memory leak in chat model

The chat model was not properly releasing resources after each
conversation, causing memory usage to grow over time.

- Add proper cleanup in chat completion
- Implement context manager for resource management
- Add memory usage monitoring

Fixes #456

docs(readme): update installation instructions

- Add Docker installation steps
- Update Python version requirements
- Fix broken links to documentation

# ❌ 错误示例
"fix bug"                    # 太简单，没有说明修复了什么
"Update code"               # 没有说明更新了什么
"WIP"                       # 工作进行中的提交不应该推送到主分支
"fix typo in chat_model.py" # 应该使用 fix(chat): correct typo in model initialization
```

#### 🟡 SHOULD 强烈建议

```bash
# 提交频率：应该保持小而频繁的提交
# ✅ 推荐：每个逻辑变更一个提交
git add api/apps/document_app.py
git commit -m "feat(api): add document validation logic"

git add tests/test_document_validation.py
git commit -m "test(api): add tests for document validation"

git add docs/api/document.md
git commit -m "docs(api): document new validation rules"

# ❌ 不推荐：一个大提交包含多个不相关的变更
git add .
git commit -m "feat: add document validation, fix chat bug, update docs"

# 分支命名：应该使用描述性名称
# 格式：<type>/<issue-number>-<short-description>
feature/123-document-upload
bugfix/456-memory-leak-fix
hotfix/789-security-patch
docs/update-api-documentation
refactor/chat-model-cleanup

# 合并策略：应该使用squash merge保持历史清洁
# 对于feature分支，使用squash merge
git checkout main
git merge --squash feature/123-document-upload
git commit -m "feat(api): add document upload functionality

- Implement file upload endpoint
- Add validation for supported formats
- Include progress tracking
- Add comprehensive tests

Closes #123"
```

### 2. 分支管理规范

#### 🔴 MUST 强制性规则

```bash
# 分支保护：主分支必须受到保护
# GitHub分支保护规则配置：
# - Require pull request reviews before merging
# - Require status checks to pass before merging
# - Require branches to be up to date before merging
# - Include administrators in restrictions

# 分支策略：必须遵循Git Flow或GitHub Flow
# 主要分支：
main        # 生产环境代码，只能通过PR合并
develop     # 开发环境代码，集成分支
release/*   # 发布准备分支
hotfix/*    # 紧急修复分支

# 工作流程：
# 1. 从develop创建feature分支
git checkout develop
git pull origin develop
git checkout -b feature/123-new-feature

# 2. 开发完成后创建PR到develop
git push origin feature/123-new-feature
# 在GitHub上创建PR: feature/123-new-feature -> develop

# 3. 发布时从develop创建release分支
git checkout develop
git checkout -b release/v1.2.0

# 4. 发布完成后合并到main和develop
git checkout main
git merge release/v1.2.0
git tag v1.2.0
git checkout develop
git merge release/v1.2.0

# 代码审查：所有代码必须经过审查
# PR检查清单：
# - [ ] 代码符合编程规范
# - [ ] 包含适当的测试
# - [ ] 文档已更新
# - [ ] CI/CD检查通过
# - [ ] 至少一个团队成员审查通过
```

### 3. 版本发布规范

#### 🔴 MUST 强制性规则

```bash
# 语义化版本：必须遵循SemVer规范
# 格式：MAJOR.MINOR.PATCH
#
# MAJOR: 不兼容的API变更
# MINOR: 向后兼容的功能新增
# PATCH: 向后兼容的问题修复

# 版本标签：必须为每个发布创建标签
git tag -a v1.2.3 -m "Release version 1.2.3

Features:
- Add document batch upload API
- Implement real-time chat streaming
- Add support for new file formats

Bug Fixes:
- Fix memory leak in chat model
- Resolve database connection timeout
- Correct file validation logic

Breaking Changes:
- Remove deprecated /api/v0 endpoints
- Change response format for /api/v1/documents
"

git push origin v1.2.3

# 发布说明：必须为每个版本编写发布说明
# CHANGELOG.md 格式
## [1.2.3] - 2024-12-20

### Added
- Document batch upload API endpoint
- Real-time chat streaming support
- Support for EPUB and MOBI file formats

### Changed
- Improved error messages for file validation
- Updated API response format for consistency

### Fixed
- Memory leak in chat model initialization
- Database connection timeout issues
- File validation edge cases

### Removed
- Deprecated /api/v0 endpoints (breaking change)

### Security
- Updated dependencies to fix security vulnerabilities
- Improved input validation for file uploads
```

## �📁 项目结构规范

### 1. 目录组织原则

#### ✅ 良好实践
```
# 按功能模块组织
rag/
├── app/          # 文档解析器（按文档类型）
├── llm/          # 大语言模型（按模型类型）
├── nlp/          # 自然语言处理
├── utils/        # 工具模块（按连接类型）
└── svr/          # 服务器模块

api/
├── apps/         # 应用模块（按业务功能）
├── db/           # 数据库相关
├── utils/        # 工具函数
└── sdk/          # SDK模块

web/src/
├── pages/        # 页面组件（按功能模块）
├── components/   # 通用组件
├── hooks/        # 自定义Hooks
├── services/     # 服务层
├── utils/        # 工具函数
└── interfaces/   # 类型定义
```

### 2. 文件命名规范

#### ✅ 良好实践
```python
# Python文件：snake_case
task_executor.py
redis_conn.py
chat_model.py

# TypeScript文件：kebab-case
flow-hooks.ts
chat-service.ts
common-util.ts

# React组件：PascalCase目录 + index.tsx
components/
├── MessageItem/
│   └── index.tsx
├── FileUploadModal/
│   └── index.tsx
```

## 📊 代码质量标准

### 1. 文件大小限制

#### 🚨 当前问题
- `chat_model.py`: 1,587行 ❌ (建议 < 500行)
- `doc.py`: 1,407行 ❌ (建议 < 500行)
- `db_models.py`: 1,124行 ❌ (建议 < 500行)

#### ✅ 改进建议
```python
# 拆分大文件的策略
# chat_model.py -> 
#   ├── base_chat_model.py
#   ├── openai_models.py
#   ├── azure_models.py
#   ├── anthropic_models.py
#   └── local_models.py

# doc.py ->
#   ├── document_upload.py
#   ├── document_processing.py
#   ├── document_retrieval.py
#   └── document_management.py
```

### 2. 函数复杂度控制

#### ✅ 标准
- 单个函数不超过50行
- 圈复杂度不超过10
- 嵌套层级不超过4层

#### ⚠️ 重构建议
```python
# 过长函数的重构示例
def process_document(self, doc):  # 原函数100+行
    # 拆分为：
    def validate_document(self, doc):
        pass
    
    def extract_content(self, doc):
        pass
    
    def process_chunks(self, chunks):
        pass
    
    def save_results(self, results):
        pass
```

### 3. 注释和文档规范

#### ✅ 良好实践
```python
def upload(dataset_id, tenant_id):
    """
    Upload documents to a dataset.
    ---
    tags:
      - Documents
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: dataset_id
        type: string
        required: true
        description: ID of the dataset.
    """
```

#### ⚠️ 需要改进
```python
# 缺少文档字符串的函数
def chat(self, system, history, gen_conf):  # 需要添加docstring
    pass

# 改进建议
def chat(self, system: str, history: List[Dict], gen_conf: Dict) -> Tuple[str, int]:
    """
    Generate chat response using the language model.
    
    Args:
        system: System prompt for the conversation
        history: List of previous messages
        gen_conf: Generation configuration parameters
        
    Returns:
        Tuple of (response_text, token_count)
        
    Raises:
        openai.APIError: When API call fails
    """
    pass
```

## ✅ 规范检查清单

### Python代码检查
- [ ] 文件大小 < 500行
- [ ] 函数长度 < 50行
- [ ] 使用类型注解
- [ ] 包含docstring
- [ ] 统一错误处理
- [ ] 日志记录规范
- [ ] 导入语句分组
- [ ] 命名符合snake_case

### TypeScript代码检查
- [ ] 组件文件 < 300行
- [ ] 使用严格类型定义
- [ ] Hook命名以use开头
- [ ] 组件使用PascalCase
- [ ] 文件使用kebab-case
- [ ] 错误边界处理
- [ ] 性能优化（useMemo, useCallback）

### 项目结构检查
- [ ] 目录按功能组织
- [ ] 文件命名一致性
- [ ] 依赖关系清晰
- [ ] 配置文件集中管理
- [ ] 测试文件对应
- [ ] 文档完整性

## 🔧 工具和自动化

### 推荐工具
```bash
# Python
black          # 代码格式化
flake8         # 代码检查
mypy           # 类型检查
pytest         # 测试框架

# TypeScript/React
prettier       # 代码格式化
eslint         # 代码检查
typescript     # 类型检查
jest           # 测试框架
```

### 配置示例
```json
// .eslintrc.js
{
  "extends": [
    "@umijs/eslint-config-umi",
    "plugin:@typescript-eslint/recommended"
  ],
  "rules": {
    "max-lines": ["error", 300],
    "max-lines-per-function": ["error", 50],
    "complexity": ["error", 10]
  }
}
```

## 🎯 具体改进建议

### 1. 大文件重构计划

#### chat_model.py (1,587行) 重构方案
```python
# 当前结构问题
class Base(ABC):           # 基础类
class OpenAI(Base):        # OpenAI实现
class AzureOpenAI(Base):   # Azure实现
class Anthropic(Base):     # Anthropic实现
# ... 15个不同的模型类在同一文件

# 建议重构为模块化结构
rag/llm/
├── base/
│   ├── __init__.py
│   ├── chat_model_base.py      # 抽象基类
│   └── model_factory.py        # 工厂模式
├── openai/
│   ├── __init__.py
│   ├── openai_chat.py          # OpenAI实现
│   └── azure_openai.py         # Azure OpenAI实现
├── anthropic/
│   ├── __init__.py
│   └── claude_chat.py          # Claude实现
├── local/
│   ├── __init__.py
│   ├── ollama_chat.py          # Ollama实现
│   └── xinference_chat.py      # Xinference实现
└── __init__.py                 # 统一导出接口
```

#### doc.py (1,407行) 重构方案
```python
# 当前问题：单文件包含所有文档操作
# 建议拆分为：
api/apps/sdk/document/
├── __init__.py
├── upload.py              # 文档上传逻辑
├── processing.py          # 文档处理逻辑
├── retrieval.py           # 文档检索逻辑
├── management.py          # 文档管理逻辑
├── chunking.py            # 分块处理逻辑
└── models.py              # 数据模型定义
```

### 2. 代码质量改进示例

#### 错误处理标准化
```python
# 当前不一致的错误处理
# 文件A：
try:
    result = some_operation()
except Exception as e:
    return "**ERROR**: " + str(e), 0

# 文件B：
try:
    result = some_operation()
except BaseException:
    pass

# 建议统一的错误处理模式
class RAGFlowError(Exception):
    """RAGFlow基础异常类"""
    def __init__(self, message: str, error_code: int = 500):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class ModelError(RAGFlowError):
    """模型相关错误"""
    pass

class DocumentError(RAGFlowError):
    """文档处理错误"""
    pass

# 统一的错误处理装饰器
def handle_errors(error_type: Type[RAGFlowError] = RAGFlowError):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"Error in {func.__name__}: {e}")
                raise error_type(f"Operation failed: {str(e)}")
        return wrapper
    return decorator

# 使用示例
@handle_errors(ModelError)
def chat(self, system: str, history: List[Dict], gen_conf: Dict) -> Tuple[str, int]:
    """Generate chat response with proper error handling."""
    pass
```

#### 配置管理标准化
```python
# 当前问题：配置散落在各处
timeout = int(os.environ.get('LM_TIMEOUT_SECONDS', 600))
BATCH_SIZE = 64
MAXIMUM_OF_UPLOADING_FILES = 256

# 建议：集中配置管理
# config/settings.py
from pydantic import BaseSettings
from typing import Optional

class RAGFlowSettings(BaseSettings):
    # 模型配置
    llm_timeout_seconds: int = 600
    max_tokens: int = 4096
    temperature: float = 0.1

    # 文档处理配置
    max_upload_files: int = 256
    chunk_size: int = 512
    chunk_overlap: int = 50

    # 数据库配置
    database_url: str
    redis_url: str

    # 存储配置
    storage_type: str = "minio"
    minio_endpoint: Optional[str] = None

    class Config:
        env_file = ".env"
        case_sensitive = False

# 使用配置
settings = RAGFlowSettings()
```

### 3. 前端代码优化建议

#### 组件拆分示例
```typescript
// 当前问题：大组件文件
// 建议拆分策略

// 原始大组件 (300+ 行)
const FlowCanvas: React.FC = () => {
  // 大量逻辑混在一起
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  // ... 大量状态和逻辑

  return (
    <div>
      {/* 复杂的JSX结构 */}
    </div>
  );
};

// 重构后的模块化结构
// components/FlowCanvas/
├── index.tsx              # 主组件
├── hooks/
│   ├── useFlowData.ts     # 数据管理Hook
│   ├── useFlowEvents.ts   # 事件处理Hook
│   └── useFlowValidation.ts # 验证逻辑Hook
├── components/
│   ├── NodePanel.tsx      # 节点面板
│   ├── EdgePanel.tsx      # 连线面板
│   └── ToolBar.tsx        # 工具栏
└── types.ts               # 类型定义

// 主组件变得简洁
const FlowCanvas: React.FC = () => {
  const { nodes, edges, loading } = useFlowData();
  const { onNodesChange, onEdgesChange } = useFlowEvents();
  const { validateFlow } = useFlowValidation();

  return (
    <div className="flow-canvas">
      <ToolBar />
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
      />
      <NodePanel />
      <EdgePanel />
    </div>
  );
};
```

#### 类型安全改进
```typescript
// 当前问题：类型定义不够严格
interface IFlow {
  avatar?: null | string;  // 应该更具体
  canvas_type: null;       // 应该是联合类型
  dsl: DSL;               // DSL类型需要更详细定义
}

// 改进建议：更严格的类型定义
type AvatarUrl = string;
type CanvasType = 'workflow' | 'chat' | 'agent' | null;

interface FlowNode {
  id: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
}

interface FlowEdge {
  id: string;
  source: string;
  target: string;
  type?: EdgeType;
}

interface DSL {
  nodes: FlowNode[];
  edges: FlowEdge[];
  viewport: { x: number; y: number; zoom: number };
}

interface IFlow {
  id: string;
  title: string;
  description: string | null;
  avatar: AvatarUrl | null;
  canvas_type: CanvasType;
  dsl: DSL;
  create_date: string;
  update_date: string;
  create_time: number;
  update_time: number;
  user_id: string;
}

// 运行时类型验证
import { z } from 'zod';

const FlowSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1).max(100),
  description: z.string().nullable(),
  avatar: z.string().url().nullable(),
  canvas_type: z.enum(['workflow', 'chat', 'agent']).nullable(),
  dsl: z.object({
    nodes: z.array(z.object({
      id: z.string(),
      type: z.string(),
      position: z.object({ x: z.number(), y: z.number() }),
      data: z.record(z.any())
    })),
    edges: z.array(z.object({
      id: z.string(),
      source: z.string(),
      target: z.string()
    }))
  })
});

type Flow = z.infer<typeof FlowSchema>;
```

### 4. 性能优化建议

#### Python后端优化
```python
# 当前问题：同步处理大量文档
def process_documents(documents):
    results = []
    for doc in documents:
        result = process_single_document(doc)  # 同步处理
        results.append(result)
    return results

# 改进：异步批处理
import asyncio
from concurrent.futures import ThreadPoolExecutor

async def process_documents_async(documents: List[Document]) -> List[ProcessResult]:
    """异步批处理文档"""
    semaphore = asyncio.Semaphore(10)  # 限制并发数

    async def process_with_semaphore(doc: Document) -> ProcessResult:
        async with semaphore:
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                return await loop.run_in_executor(
                    executor, process_single_document, doc
                )

    tasks = [process_with_semaphore(doc) for doc in documents]
    return await asyncio.gather(*tasks, return_exceptions=True)

# 缓存优化
from functools import lru_cache
import redis

class CacheManager:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client

    def cached_embedding(self, text: str, model: str) -> List[float]:
        """缓存嵌入向量"""
        cache_key = f"embedding:{model}:{hash(text)}"
        cached = self.redis.get(cache_key)

        if cached:
            return json.loads(cached)

        embedding = generate_embedding(text, model)
        self.redis.setex(cache_key, 3600, json.dumps(embedding))
        return embedding
```

#### 前端性能优化
```typescript
// 当前问题：不必要的重渲染
const FlowNode: React.FC<Props> = ({ node, onUpdate }) => {
  // 每次父组件更新都会重新创建函数
  const handleClick = () => {
    onUpdate(node.id, { selected: true });
  };

  return <div onClick={handleClick}>{node.title}</div>;
};

// 改进：使用React.memo和useCallback
const FlowNode: React.FC<Props> = React.memo(({ node, onUpdate }) => {
  const handleClick = useCallback(() => {
    onUpdate(node.id, { selected: true });
  }, [node.id, onUpdate]);

  return <div onClick={handleClick}>{node.title}</div>;
});

// 虚拟化长列表
import { FixedSizeList as List } from 'react-window';

const VirtualizedNodeList: React.FC<{ nodes: FlowNode[] }> = ({ nodes }) => {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <FlowNode node={nodes[index]} />
    </div>
  );

  return (
    <List
      height={600}
      itemCount={nodes.length}
      itemSize={50}
      width="100%"
    >
      {Row}
    </List>
  );
};

// 状态管理优化
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

interface FlowState {
  nodes: FlowNode[];
  edges: FlowEdge[];
  selectedNodeId: string | null;
  // 分离不同关注点的状态
  ui: {
    sidebarOpen: boolean;
    loading: boolean;
  };
}

const useFlowStore = create<FlowState>()(
  subscribeWithSelector((set, get) => ({
    nodes: [],
    edges: [],
    selectedNodeId: null,
    ui: {
      sidebarOpen: true,
      loading: false,
    },
    // 只更新需要的部分
    updateNode: (id: string, updates: Partial<FlowNode>) =>
      set((state) => ({
        nodes: state.nodes.map((node) =>
          node.id === id ? { ...node, ...updates } : node
        ),
      })),
  }))
);

// 选择器优化，避免不必要的重渲染
const useSelectedNode = () => useFlowStore(
  (state) => state.nodes.find(n => n.id === state.selectedNodeId),
  shallow
);
```

### 5. 测试规范

#### Python测试示例
```python
# tests/test_chat_model.py
import pytest
from unittest.mock import Mock, patch
from rag.llm.chat_model import OpenAI

class TestChatModel:
    @pytest.fixture
    def chat_model(self):
        return OpenAI(
            key="test-key",
            model_name="gpt-3.5-turbo",
            base_url="https://api.openai.com/v1"
        )

    @patch('openai.OpenAI')
    def test_chat_success(self, mock_openai, chat_model):
        # 模拟成功响应
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Test response"
        mock_response.choices[0].finish_reason = "stop"

        mock_openai.return_value.chat.completions.create.return_value = mock_response

        result, tokens = chat_model.chat(
            system="You are a helpful assistant",
            history=[{"role": "user", "content": "Hello"}],
            gen_conf={"temperature": 0.7}
        )

        assert result == "Test response"
        assert tokens >= 0

    @patch('openai.OpenAI')
    def test_chat_api_error(self, mock_openai, chat_model):
        # 模拟API错误
        mock_openai.return_value.chat.completions.create.side_effect = \
            openai.APIError("API Error")

        result, tokens = chat_model.chat(
            system="",
            history=[{"role": "user", "content": "Hello"}],
            gen_conf={}
        )

        assert result.startswith("**ERROR**:")
        assert tokens == 0
```

#### TypeScript测试示例
```typescript
// __tests__/FlowCanvas.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import FlowCanvas from '../FlowCanvas';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('FlowCanvas', () => {
  it('renders flow canvas with nodes', async () => {
    render(<FlowCanvas />, { wrapper: createWrapper() });

    expect(screen.getByTestId('flow-canvas')).toBeInTheDocument();

    // 等待数据加载
    await screen.findByTestId('flow-nodes');

    expect(screen.getByTestId('flow-nodes')).toBeInTheDocument();
  });

  it('handles node selection', async () => {
    const { user } = render(<FlowCanvas />, { wrapper: createWrapper() });

    const node = await screen.findByTestId('flow-node-1');
    await user.click(node);

    expect(node).toHaveClass('selected');
  });
});

// Hook测试
import { renderHook, act } from '@testing-library/react';
import { useFlowData } from '../hooks/useFlowData';

describe('useFlowData', () => {
  it('loads flow data correctly', async () => {
    const { result } = renderHook(() => useFlowData('flow-id'));

    expect(result.current.loading).toBe(true);

    await act(async () => {
      // 等待数据加载完成
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.nodes).toHaveLength(2);
  });
});
```

## 🚀 CI/CD和部署规范

### 1. 代码质量检查流水线

#### GitHub Actions配置示例
```yaml
# .github/workflows/code-quality.yml
name: Code Quality Check

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main ]

jobs:
  python-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        pip install black flake8 mypy pytest
        pip install -r requirements.txt

    - name: Code formatting check
      run: black --check --diff rag/ api/

    - name: Lint check
      run: flake8 rag/ api/ --max-line-length=100 --max-complexity=10

    - name: Type check
      run: mypy rag/ api/ --ignore-missing-imports

    - name: Run tests
      run: pytest tests/ -v --cov=rag --cov=api --cov-report=xml

    - name: Upload coverage
      uses: codecov/codecov-action@v3

  frontend-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: web/package-lock.json

    - name: Install dependencies
      run: cd web && npm ci

    - name: Lint check
      run: cd web && npm run lint

    - name: Type check
      run: cd web && npm run type-check

    - name: Run tests
      run: cd web && npm run test:coverage

    - name: Build check
      run: cd web && npm run build
```

### 2. 预提交钩子配置

#### pre-commit配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.9
        files: ^(rag|api)/.*\.py$

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=100, --max-complexity=10]
        files: ^(rag|api)/.*\.py$

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        files: ^(rag|api)/.*\.py$
        args: [--ignore-missing-imports]

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        files: ^web/src/.*\.(ts|tsx|js|jsx|json|css|md)$

  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        files: ^web/src/.*\.(ts|tsx|js|jsx)$
        args: [--fix]
```

### 3. Docker化部署规范

#### 多阶段构建Dockerfile
```dockerfile
# Dockerfile.backend
FROM python:3.9-slim as base

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 开发阶段
FROM base as development
COPY requirements-dev.txt .
RUN pip install --no-cache-dir -r requirements-dev.txt
COPY . .
CMD ["python", "api/ragflow_server.py"]

# 生产阶段
FROM base as production
COPY rag/ ./rag/
COPY api/ ./api/
COPY conf/ ./conf/

# 创建非root用户
RUN useradd --create-home --shell /bin/bash ragflow
USER ragflow

EXPOSE 9380
CMD ["python", "api/ragflow_server.py"]

# 前端Dockerfile
FROM node:18-alpine as frontend-base

WORKDIR /app
COPY web/package*.json ./
RUN npm ci --only=production

FROM frontend-base as frontend-build
COPY web/ .
RUN npm run build

FROM nginx:alpine as frontend-production
COPY --from=frontend-build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  ragflow-server:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    ports:
      - "9380:9380"
    environment:
      - DATABASE_URL=************************************/ragflow
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  task-executor:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: production
    command: ["python", "rag/svr/task_executor.py", "0"]
    environment:
      - DATABASE_URL=************************************/ragflow
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    deploy:
      replicas: 2

  web-server:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: frontend-production
    ports:
      - "3000:80"
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ragflow
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 4. 监控和日志规范

#### 结构化日志配置
```python
# utils/logging_config.py
import logging
import json
from datetime import datetime
from typing import Dict, Any

class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""

    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }

        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)

        # 添加自定义字段
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id

        return json.dumps(log_entry, ensure_ascii=False)

def setup_logging(service_name: str, log_level: str = "INFO"):
    """设置结构化日志"""
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(StructuredFormatter())
    logger.addHandler(console_handler)

    # 文件处理器
    file_handler = logging.FileHandler(f'logs/{service_name}.log')
    file_handler.setFormatter(StructuredFormatter())
    logger.addHandler(file_handler)

    return logger

# 使用示例
logger = setup_logging('ragflow-server')

def process_document(doc_id: str, user_id: str):
    logger.info(
        "Processing document",
        extra={'user_id': user_id, 'doc_id': doc_id}
    )
```

#### 性能监控
```python
# utils/metrics.py
import time
import functools
from typing import Callable, Any
from prometheus_client import Counter, Histogram, Gauge

# 定义指标
REQUEST_COUNT = Counter(
    'ragflow_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'ragflow_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint']
)

ACTIVE_CONNECTIONS = Gauge(
    'ragflow_active_connections',
    'Number of active connections'
)

def monitor_performance(func: Callable) -> Callable:
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()

        try:
            result = func(*args, **kwargs)
            REQUEST_COUNT.labels(
                method=func.__name__,
                endpoint=func.__module__,
                status='success'
            ).inc()
            return result
        except Exception as e:
            REQUEST_COUNT.labels(
                method=func.__name__,
                endpoint=func.__module__,
                status='error'
            ).inc()
            raise
        finally:
            duration = time.time() - start_time
            REQUEST_DURATION.labels(
                method=func.__name__,
                endpoint=func.__module__
            ).observe(duration)

    return wrapper

# 使用示例
@monitor_performance
def chat(self, system: str, history: List[Dict], gen_conf: Dict):
    """带性能监控的聊天函数"""
    pass
```

### 5. 安全规范

#### 安全配置检查清单
```python
# security/security_config.py
import os
from typing import List, Dict

class SecurityConfig:
    """安全配置管理"""

    @staticmethod
    def validate_environment() -> List[str]:
        """验证环境变量安全性"""
        issues = []

        # 检查必需的安全环境变量
        required_vars = [
            'SECRET_KEY',
            'DATABASE_URL',
            'REDIS_URL',
            'JWT_SECRET'
        ]

        for var in required_vars:
            if not os.getenv(var):
                issues.append(f"Missing required environment variable: {var}")

        # 检查密钥强度
        secret_key = os.getenv('SECRET_KEY', '')
        if len(secret_key) < 32:
            issues.append("SECRET_KEY should be at least 32 characters long")

        return issues

    @staticmethod
    def get_cors_config() -> Dict[str, Any]:
        """CORS配置"""
        return {
            'origins': os.getenv('ALLOWED_ORIGINS', 'http://localhost:3000').split(','),
            'methods': ['GET', 'POST', 'PUT', 'DELETE'],
            'allow_headers': ['Content-Type', 'Authorization'],
            'expose_headers': ['X-Total-Count'],
            'supports_credentials': True,
            'max_age': 86400
        }

# 输入验证
from pydantic import BaseModel, validator
import re

class SecureDocumentUpload(BaseModel):
    filename: str
    content_type: str
    size: int

    @validator('filename')
    def validate_filename(cls, v):
        # 防止路径遍历攻击
        if '..' in v or '/' in v or '\\' in v:
            raise ValueError("Invalid filename")

        # 检查文件扩展名
        allowed_extensions = {'.pdf', '.txt', '.docx', '.md'}
        if not any(v.lower().endswith(ext) for ext in allowed_extensions):
            raise ValueError("File type not allowed")

        return v

    @validator('size')
    def validate_size(cls, v):
        max_size = 100 * 1024 * 1024  # 100MB
        if v > max_size:
            raise ValueError("File too large")
        return v

# API限流
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["1000 per hour", "100 per minute"]
)

@app.route('/api/upload')
@limiter.limit("10 per minute")
def upload_document():
    """限流的文档上传接口"""
    pass
```

## 📈 代码质量度量

### 1. 质量指标定义

#### 代码复杂度指标
```python
# 使用radon计算复杂度
# pip install radon

# 圈复杂度检查
radon cc rag/ api/ --min B  # B级以上复杂度

# 维护性指数
radon mi rag/ api/ --min B

# 代码行数统计
radon raw rag/ api/
```

#### 测试覆盖率目标
```bash
# Python测试覆盖率
pytest --cov=rag --cov=api --cov-report=html --cov-fail-under=80

# 前端测试覆盖率
cd web && npm run test:coverage -- --coverage-threshold=80
```

### 2. 代码审查清单

#### Pull Request检查项
- [ ] **功能性**
  - [ ] 代码实现符合需求
  - [ ] 边界条件处理完整
  - [ ] 错误处理适当

- [ ] **代码质量**
  - [ ] 命名清晰有意义
  - [ ] 函数长度 < 50行
  - [ ] 圈复杂度 < 10
  - [ ] 无重复代码

- [ ] **安全性**
  - [ ] 输入验证完整
  - [ ] 无SQL注入风险
  - [ ] 敏感信息不在代码中

- [ ] **性能**
  - [ ] 无明显性能问题
  - [ ] 数据库查询优化
  - [ ] 缓存使用合理

- [ ] **测试**
  - [ ] 单元测试覆盖率 > 80%
  - [ ] 集成测试通过
  - [ ] 手动测试验证

- [ ] **文档**
  - [ ] API文档更新
  - [ ] 代码注释充分
  - [ ] README更新

### 3. 持续改进计划

#### 技术债务管理
```python
# 技术债务跟踪
# TODO: 重构chat_model.py，拆分为多个模块 (优先级: 高, 预计工作量: 3天)
# FIXME: 修复doc.py中的内存泄漏问题 (优先级: 中, 预计工作量: 1天)
# HACK: 临时解决方案，需要在v2.0中重新设计 (优先级: 低, 预计工作量: 5天)

class TechnicalDebt:
    """技术债务跟踪"""

    def __init__(self):
        self.debts = []

    def add_debt(self, description: str, priority: str, estimated_effort: str, file_path: str):
        """添加技术债务项"""
        self.debts.append({
            'description': description,
            'priority': priority,
            'estimated_effort': estimated_effort,
            'file_path': file_path,
            'created_date': datetime.now(),
            'status': 'open'
        })

    def generate_report(self) -> str:
        """生成技术债务报告"""
        high_priority = [d for d in self.debts if d['priority'] == '高']
        return f"高优先级技术债务: {len(high_priority)}项"
```

#### 代码质量趋势监控
```yaml
# .github/workflows/quality-metrics.yml
name: Quality Metrics

on:
  schedule:
    - cron: '0 2 * * *'  # 每天凌晨2点运行

jobs:
  collect-metrics:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0  # 获取完整历史

    - name: Calculate metrics
      run: |
        # 代码行数趋势
        echo "Lines of code: $(find rag api -name '*.py' | xargs wc -l | tail -1)"

        # 复杂度趋势
        radon cc rag/ api/ --json > complexity.json

        # 测试覆盖率趋势
        pytest --cov=rag --cov=api --cov-report=json

    - name: Upload metrics
      # 上传到监控系统
      run: |
        curl -X POST "$METRICS_ENDPOINT" \
          -H "Content-Type: application/json" \
          -d @complexity.json
```

## 🔧 环境配置规范

### 1. 开发环境一致性

#### 🔴 MUST 强制性规则

```bash
# Python版本：必须使用指定的Python版本
# .python-version 文件
3.9.18

# 虚拟环境：必须使用虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate.bat  # Windows

# 依赖管理：必须锁定依赖版本
# requirements.txt 必须包含精确版本
flask==2.3.3
peewee==3.16.3
redis==4.6.0
openai==1.3.5

# 开发依赖分离
# requirements-dev.txt
black==23.9.1
flake8==6.1.0
mypy==1.6.1
pytest==7.4.3
pytest-cov==4.1.0

# 环境变量：必须使用.env文件管理
# .env.example (提交到版本控制)
DATABASE_URL=postgresql://user:password@localhost:5432/ragflow
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-secret-key-here
LLM_TIMEOUT_SECONDS=600
LOG_LEVEL=INFO

# .env (不提交到版本控制，本地配置)
DATABASE_URL=postgresql://dev_user:dev_pass@localhost:5432/ragflow_dev
REDIS_URL=redis://localhost:6379/1
SECRET_KEY=dev-secret-key-123456789
LLM_TIMEOUT_SECONDS=300
LOG_LEVEL=DEBUG
```

#### 🟡 SHOULD 强烈建议

```bash
# Docker开发环境：应该提供Docker配置
# docker-compose.dev.yml
version: '3.8'
services:
  ragflow-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
      - /app/.venv  # 避免挂载虚拟环境
    ports:
      - "9380:9380"
      - "5678:5678"  # debugpy端口
    environment:
      - PYTHONPATH=/app
      - FLASK_ENV=development
      - FLASK_DEBUG=1
    depends_on:
      - postgres-dev
      - redis-dev

  postgres-dev:
    image: postgres:15
    environment:
      POSTGRES_DB: ragflow_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_pass
    ports:
      - "5433:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data

  redis-dev:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data

volumes:
  postgres_dev_data:
  redis_dev_data:

# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# IDE配置：应该提供统一的IDE配置
# .vscode/settings.json
{
  "python.defaultInterpreterPath": "./.venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length", "100"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    ".venv": true
  }
}

# .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "RAGFlow Server",
      "type": "python",
      "request": "launch",
      "program": "api/ragflow_server.py",
      "console": "integratedTerminal",
      "env": {
        "PYTHONPATH": "${workspaceFolder}",
        "FLASK_ENV": "development"
      }
    },
    {
      "name": "Task Executor",
      "type": "python",
      "request": "launch",
      "program": "rag/svr/task_executor.py",
      "args": ["0"],
      "console": "integratedTerminal",
      "env": {
        "PYTHONPATH": "${workspaceFolder}"
      }
    }
  ]
}
```

### 2. 配置验证规范

#### 🔴 MUST 强制性规则

```python
# 配置验证：必须在启动时验证所有配置
from pydantic import BaseSettings, validator
from typing import Optional
import os

class RAGFlowConfig(BaseSettings):
    """RAGFlow配置类"""

    # 数据库配置
    database_url: str
    database_pool_size: int = 20
    database_timeout: int = 30

    # Redis配置
    redis_url: str
    redis_max_connections: int = 100

    # LLM配置
    llm_timeout_seconds: int = 600
    llm_max_tokens: int = 4096
    llm_temperature: float = 0.1

    # 安全配置
    secret_key: str
    jwt_secret: str
    allowed_origins: str = "http://localhost:3000"

    # 文件上传配置
    max_upload_size: int = 100 * 1024 * 1024  # 100MB
    max_upload_files: int = 256
    upload_path: str = "./uploads"

    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None

    @validator('secret_key')
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError('SECRET_KEY must be at least 32 characters long')
        return v

    @validator('database_url')
    def validate_database_url(cls, v):
        if not v.startswith(('postgresql://', 'mysql://', 'sqlite:///')):
            raise ValueError('Invalid database URL format')
        return v

    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'LOG_LEVEL must be one of {valid_levels}')
        return v.upper()

    @validator('llm_temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('LLM temperature must be between 0.0 and 2.0')
        return v

    class Config:
        env_file = '.env'
        case_sensitive = False

# 配置初始化和验证
def load_config() -> RAGFlowConfig:
    """加载和验证配置"""
    try:
        config = RAGFlowConfig()

        # 验证文件路径
        os.makedirs(config.upload_path, exist_ok=True)

        # 验证数据库连接
        # 这里可以添加数据库连接测试

        return config
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        raise SystemExit(1)

# 在应用启动时验证配置
config = load_config()
```

### 3. 多环境配置管理

#### 🔴 MUST 强制性规则

```python
# 环境区分：必须明确区分不同环境
import os
from enum import Enum

class Environment(Enum):
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

def get_environment() -> Environment:
    """获取当前环境"""
    env_name = os.getenv('RAGFLOW_ENV', 'development').lower()
    try:
        return Environment(env_name)
    except ValueError:
        logger.warning(f"Unknown environment '{env_name}', defaulting to development")
        return Environment.DEVELOPMENT

# 环境特定配置
class EnvironmentConfig:
    """环境特定配置"""

    @staticmethod
    def get_config(env: Environment) -> dict:
        configs = {
            Environment.DEVELOPMENT: {
                'debug': True,
                'log_level': 'DEBUG',
                'database_pool_size': 5,
                'redis_max_connections': 10,
                'enable_profiling': True,
                'cors_origins': ['http://localhost:3000', 'http://127.0.0.1:3000']
            },
            Environment.TESTING: {
                'debug': False,
                'log_level': 'WARNING',
                'database_pool_size': 2,
                'redis_max_connections': 5,
                'enable_profiling': False,
                'cors_origins': ['http://localhost:3000']
            },
            Environment.STAGING: {
                'debug': False,
                'log_level': 'INFO',
                'database_pool_size': 10,
                'redis_max_connections': 20,
                'enable_profiling': False,
                'cors_origins': ['https://staging.ragflow.com']
            },
            Environment.PRODUCTION: {
                'debug': False,
                'log_level': 'WARNING',
                'database_pool_size': 20,
                'redis_max_connections': 100,
                'enable_profiling': False,
                'cors_origins': ['https://ragflow.com']
            }
        }
        return configs.get(env, configs[Environment.DEVELOPMENT])

# 配置文件结构
# config/
# ├── __init__.py
# ├── base.py          # 基础配置
# ├── development.py   # 开发环境配置
# ├── testing.py       # 测试环境配置
# ├── staging.py       # 预发布环境配置
# └── production.py    # 生产环境配置

# config/base.py
class BaseConfig:
    """基础配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY')
    DATABASE_URL = os.environ.get('DATABASE_URL')
    REDIS_URL = os.environ.get('REDIS_URL')

# config/development.py
class DevelopmentConfig(BaseConfig):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    DATABASE_POOL_SIZE = 5

# config/production.py
class ProductionConfig(BaseConfig):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'
    DATABASE_POOL_SIZE = 20

    @classmethod
    def validate(cls):
        """生产环境配置验证"""
        required_vars = ['SECRET_KEY', 'DATABASE_URL', 'REDIS_URL']
        missing = [var for var in required_vars if not getattr(cls, var)]
        if missing:
            raise ValueError(f"Missing required environment variables: {missing}")
```

---

## 📚 参考资源

### 官方文档
- [Python PEP 8 Style Guide](https://pep8.org/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React Best Practices](https://react.dev/learn)

### 工具链
- **Python**: black, flake8, mypy, pytest, bandit
- **TypeScript**: prettier, eslint, typescript, jest
- **CI/CD**: GitHub Actions, pre-commit
- **监控**: Prometheus, Grafana, Sentry

### 代码质量标准
- **文件大小**: Python < 500行, TypeScript < 300行
- **函数复杂度**: 圈复杂度 < 10, 函数长度 < 50行
- **测试覆盖率**: 单元测试 > 80%, 集成测试 > 60%
- **文档覆盖率**: 公共API 100%, 内部函数 > 70%

---

## 📊 规范完整性评估报告

### ✅ **已完善的规范领域**

1. **🐍 Python编程规范** - 完整度: 95%
   - ✅ 命名约定（强制性规则）
   - ✅ 代码结构和组织
   - ✅ 错误处理规范
   - ✅ 类型注解规范
   - ✅ 日志记录规范

2. **⚛️ TypeScript/React编程规范** - 完整度: 90%
   - ✅ 组件命名和文件组织
   - ✅ TypeScript类型定义
   - ✅ React Hooks使用规范
   - ✅ 状态管理规范
   - ✅ 错误处理和请求管理

3. **🌐 API设计规范** - 完整度: 100%
   - ✅ RESTful API设计原则
   - ✅ API文档规范
   - ✅ 错误处理规范
   - ✅ 统一响应格式

4. **🗄️ 数据库操作规范** - 完整度: 100%
   - ✅ ORM使用规范
   - ✅ 查询优化
   - ✅ 事务管理
   - ✅ 连接池配置

5. **🔄 并发编程规范** - 完整度: 100%
   - ✅ 异步编程规范
   - ✅ 线程安全规范
   - ✅ 资源管理
   - ✅ 死锁避免

6. **📝 版本控制规范** - 完整度: 100%
   - ✅ Git提交规范
   - ✅ 分支管理规范
   - ✅ 版本发布规范
   - ✅ 代码审查流程

7. **🔧 环境配置规范** - 完整度: 100%
   - ✅ 开发环境一致性
   - ✅ 配置验证规范
   - ✅ 多环境配置管理
   - ✅ Docker化配置

8. **🚀 CI/CD和部署规范** - 完整度: 95%
   - ✅ 代码质量检查流水线
   - ✅ 预提交钩子配置
   - ✅ Docker化部署规范
   - ✅ 监控和日志规范

9. **🔒 安全规范** - 完整度: 90%
   - ✅ 安全配置检查
   - ✅ 输入验证规范
   - ✅ API限流规范
   - ✅ 权限控制

10. **📊 代码质量标准** - 完整度: 100%
    - ✅ 文件大小限制
    - ✅ 函数复杂度控制
    - ✅ 测试覆盖率要求
    - ✅ 代码审查清单

### 🎯 **规范表述改进成果**

#### **分级系统引入**
- 🔴 **MUST (必须)**: 强制性规则，违反将导致代码审查不通过
- 🟡 **SHOULD (应该)**: 强烈建议遵循，有特殊情况可例外但需说明
- 🟢 **MAY (可以)**: 可选建议，团队可根据情况选择
- 🚫 **MUST NOT (禁止)**: 明确禁止的做法

#### **表述明确性提升**
- ✅ 将模糊的"建议"改为明确的"必须"/"应该"/"可以"
- ✅ 为每个规范提供具体的代码示例
- ✅ 明确违反规范的后果和检查方式
- ✅ 提供可执行的检查清单

### 📈 **实用性评估**

#### **优势**
1. **全面覆盖**: 涵盖了软件开发生命周期的所有重要环节
2. **分级明确**: 三级规范系统让团队能够区分优先级
3. **示例丰富**: 每个规范都有正面和反面的代码示例
4. **工具支持**: 提供了完整的自动化工具配置
5. **可执行性**: 包含具体的检查清单和验证方法

#### **适用场景**
- ✅ 新团队成员入职培训
- ✅ 代码审查标准制定
- ✅ CI/CD流水线配置
- ✅ 项目重构指导
- ✅ 技术债务管理

### 🔄 **持续改进建议**

1. **定期更新**: 建议每季度回顾和更新规范
2. **团队反馈**: 收集开发团队的使用反馈
3. **工具集成**: 将规范检查集成到IDE和CI/CD中
4. **培训计划**: 制定基于此规范的团队培训计划
5. **度量跟踪**: 建立规范遵循度的度量指标

### 📋 **执行建议**

#### **短期目标 (1-2周)**
- [ ] 配置pre-commit钩子
- [ ] 设置CI/CD质量检查
- [ ] 团队规范培训

#### **中期目标 (1-2月)**
- [ ] 重构大文件（chat_model.py等）
- [ ] 标准化错误处理
- [ ] 完善测试覆盖率

#### **长期目标 (3-6月)**
- [ ] 建立代码质量度量体系
- [ ] 实施技术债务管理
- [ ] 优化开发工作流程

---

*本规范文档基于RAGFlow项目v0.17.1的代码分析，建议定期更新以反映项目演进。最后更新时间：2024年12月*
